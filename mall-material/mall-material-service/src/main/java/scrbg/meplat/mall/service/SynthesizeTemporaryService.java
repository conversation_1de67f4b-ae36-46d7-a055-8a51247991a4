package scrbg.meplat.mall.service;

import java.math.BigDecimal;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;

import scrbg.meplat.mall.dto.bidding.AuditBusinessDTO;
import scrbg.meplat.mall.dto.bidding.SynthesizeTemporaryDto;
import scrbg.meplat.mall.entity.SynthesizeTemporary;
import scrbg.meplat.mall.entity.SynthesizeTemporaryDtl;
import scrbg.meplat.mall.vo.GetSynthesizeTemporaryPlanDetailVO;
/**
 * @描述：大宗临购单 服务类
 * @作者: ye
 * @日期: 2023-10-07
 */
public interface SynthesizeTemporaryService extends IService<SynthesizeTemporary> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SynthesizeTemporary> queryWrapper);

        void create(SynthesizeTemporary synthesizeTemporary);
        void update(SynthesizeTemporary synthesizeTemporary);
        SynthesizeTemporary getById(String id);

        void delete(String id);

        void deleteBatch( List<String> ids);

        /**
         * 新增大宗清单
         * @param vo
         */
        void createSynthesizeTemporary(SynthesizeTemporary vo);

        /**
         * 查询当前机构的数据
         * @param jsonObject
         * @param q
         * @return
         */
        PageUtils getThisOrgList(JSONObject jsonObject, LambdaQueryWrapper<SynthesizeTemporary> q);

        /**
         * 根据编号获取数据
         * @param sn
         * @return
         */
        SynthesizeTemporary getBySn(String sn,String orgId);

        /**
         * 修改数据
         * @param dto
         */
        void updateInfo(SynthesizeTemporary dto);

        /**
         * 删除数据
         * @param id
         */
        void deleteInfo(String id);

        /**
         * 删除明细数据
         * @param id
         */
        void deleteInfoItem(String id);

        /**
         * 供应商查询大宗临购清单
         * @param jsonObject
         * @param q
         * @return
         */
        PageUtils supplierListByEntity(JSONObject jsonObject, LambdaQueryWrapper<SynthesizeTemporary> q);

        /**
         * 根据编号获取清单数据（供应商）
         * @param sn
         * @return
         */
        SynthesizeTemporary getSupplierBilBySn(String sn);

        /**
         * 批量修改明细并确认（供应商）
         *
         * @param dtls
         * @param outPhaseInterest
         */
        void batchUpdateItems(List<SynthesizeTemporaryDtl> dtls, BigDecimal outPhaseInterest);

        /**
         * 供应商删除单据
         * @param id
         */
        void supplierDeleteInfo(String id);

        /**
         * 推送大宗临购计划到pcwp
         *
         * @param id
         * @param idStr
         * @param farArg
         */
        void submitSynthesizeTemporaryPlan(String id, String idStr, StringBuilder farArg);

        /**
         * 导出excel
         *
         * @param id
         * @param response
         */
        void exportExcel(String id, HttpServletResponse response);


        /**
         * 根据pcwp计划id获取组装计划vo
         *
         * @param id
         * @return
         */
        GetSynthesizeTemporaryPlanDetailVO getSynthesizeTemporaryPlanDetail(String id);

        /**
         * pcwp删除草稿大宗临购计划时商城修改状态为可推送
         * @param id
         */
        void updateStayPush(String id);

        /**
         * 审核单据
         * @param dto
         */
        void auditBusiness(AuditBusinessDTO dto);

        /**
         * 供应商拒绝单据
         * @param dto
         */
        void refuseBusiness(AuditBusinessDTO dto);

        /**
         * 物资分公司查看所有的大宗清单
         * @param jsonObject
         * @param synthesizeTemporaryLambdaQueryWrapper
         * @return
         */
        PageUtils platformListByEntity(JSONObject jsonObject, LambdaQueryWrapper<SynthesizeTemporary> synthesizeTemporaryLambdaQueryWrapper);

        /**
         * 供应商拒绝单据给收货单位
         * @param dto
         */
        void auditRefuseOrg(AuditBusinessDTO dto);

        /**
         * 生成大宗临购清单竞价
         * @param dto
         */
        void createBidding(SynthesizeTemporaryDto dto);

        boolean updateState(SynthesizeTemporary synthesizeTemporary);
}
