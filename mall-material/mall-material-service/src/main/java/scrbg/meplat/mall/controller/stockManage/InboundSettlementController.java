package scrbg.meplat.mall.controller.stockManage;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.InboundSettlementManage;
import scrbg.meplat.mall.service.stockManage.InboundSettlementService;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/stock/inboundSettlement")
@ApiSort(value = 500)
@Api(tags = "入库结算管理（后台）")
public class InboundSettlementController {


    private InboundSettlementService inboundSettlementService;


    @Autowired
    public void setInboundSettlementService(InboundSettlementService inboundSettlementService) {
        this.inboundSettlementService = inboundSettlementService;
    }

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR<InboundSettlementManage> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils<InboundSettlementManage> page = inboundSettlementService.queryPage(jsonObject, new LambdaQueryWrapper<InboundSettlementManage>());
        return PageR.success(page);
    }
    @GetMapping("/export")
    @ApiOperation(value = "导出入库结算单")
    public R<Object> export(String id,HttpServletResponse response) {
        inboundSettlementService.export(id,response);
        return R.success("人库结算单导出成功");
    }
    @PostMapping("/save")
    @ApiOperation(value = "新增入库结算单")
    public R<Object> save(@RequestBody InboundSettlementManage inboundSettlementManage) {
        inboundSettlementManage.setInboundType(1);
        inboundSettlementService.saveSettlement(inboundSettlementManage);
        return R.success();
    }
    @PostMapping("/update")
    @ApiOperation(value = "修改入库结算单")
    public R<Object> update(@RequestBody InboundSettlementManage inboundSettlementManage) {
        inboundSettlementManage.setInboundType(1);
        inboundSettlementService.updateSettlement(inboundSettlementManage);
        return R.success();
    }
    @GetMapping("/updateState")
    @ApiOperation(value = "改变入库结算单状态")
    @Transactional
    public R<Object> updateState(String id, int state) {
        inboundSettlementService.updateState(id, state);
        return R.success();
    }
    @PostMapping("/saveAndSubmit")
    @ApiOperation(value = "新增并提交入库结算单")
    public R<Object> saveAndSubmit(@RequestBody InboundSettlementManage inboundSettlementManage) {
        inboundSettlementManage.setInboundType(1);
        inboundSettlementService.saveAndSubmitSettlement(inboundSettlementManage);
        return R.success();
    }
    @PostMapping("/updateAndSubmit")
    @ApiOperation(value = "编辑并提交入库结算单")
    public R<Object> updateAndSubmit(@RequestBody InboundSettlementManage inboundSettlementManage) {
        inboundSettlementManage.setInboundType(1);
        inboundSettlementService.updateAndSubmitSettlement(inboundSettlementManage);
        return R.success();
    }
    @GetMapping("/submit/{id}")
    @ApiOperation(value = "新增入库结算单")
    public R<Object> submit(@PathVariable String id) {
        inboundSettlementService.submitSettlement(id);
        return R.success();
    }
}
