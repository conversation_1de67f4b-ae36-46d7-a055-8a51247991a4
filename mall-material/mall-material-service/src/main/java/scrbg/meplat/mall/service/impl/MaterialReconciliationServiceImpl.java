package scrbg.meplat.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.rabbitMQ.TTRabbitMQConnectionTest;
import scrbg.meplat.mall.config.rabbitMQ.TTToDoRabbitMQUtil;
import scrbg.meplat.mall.config.rabbitMQ.ToDoMessageBody;
import scrbg.meplat.mall.controller.stockManage.vo.OutboundSettlementVO;
import scrbg.meplat.mall.dto.AuditDTO;
import scrbg.meplat.mall.dto.order.AddSettlementAmountDTO;
import scrbg.meplat.mall.dto.order.AddSettlementAmountParentDTO;
import scrbg.meplat.mall.dto.order.AddSettlementAmountUpdateDTO;
import scrbg.meplat.mall.dto.order.CheckReconciliationIsCancellationVO;
import scrbg.meplat.mall.dto.reconciliation.MaterialReconciliationUpdateDTO;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.dto.user.OrgAndSon;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.DealOrderInfoMapper;
import scrbg.meplat.mall.mapper.MaterialReconciliationDtlMapper;
import scrbg.meplat.mall.mapper.MaterialReconciliationMapper;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.dto.PcwpAcceptanceRequest;
import scrbg.meplat.mall.pcwp.dto.PcwpRevolAcceptanceDto;
import scrbg.meplat.mall.pcwp.dto.PcwpSiteReceiptRequest;
import scrbg.meplat.mall.pcwp.third.model.ReconciliationDtl;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.service.stockManage.OutboundSettlementService;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.vo.platform.ReconciliationLedgerListVo;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述：物资验收 服务类
 * @作者: ye
 * @日期: 2023-07-26
 */
@Slf4j
@Service
public class MaterialReconciliationServiceImpl extends ServiceImpl<MaterialReconciliationMapper, MaterialReconciliation> implements MaterialReconciliationService {
    @Autowired
    MaterialReconciliationDtlService materialReconciliationDtlService;

    @Autowired
    OrdersService ordersService;

    @Autowired
    PlatformDealFeeDtlService platformDealFeeDtlService;

    @Autowired
    MallConfig mallConfig;

    @Autowired
    AuditRecordService auditRecordService;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    DealOrderInfoService dealOrderInfoService;

    @Autowired
    DealOrderInfoMapper dealOrderInfoMapper;

    @Autowired
    MaterialReconciliationDtlMapper materialReconciliationDtlMapper;

    @Autowired
    private OutboundSettlementService outboundSettlementService;


    @Autowired
    ProductService productService;

    @Autowired
    private InvoiceRecordService invoiceRecordService;

    //查询pcwp物资数据。修改对账和商品的物资信息
    private static final String BASICS_MATERIAL_LIST = "/thirdapi/matarialpurchase/queryPageMaterialDtl";
    private static final String BASICS_MATERIAL_INFO = "/thirdapi/matarialpurchase/getMaterialInfoById";

    // 反写对账单暂扣数量
    private static final String WRITE_BACK_BILL_LOCK_QUANTITY_URL = "/thirdapi/siteReceiving/writeBackBillLockQuantiy";

    // 反写已审核数量
    private static final String WRITE_BACK_BILL_QUANTIY_URL = "/thirdapi/siteReceiving/writeBackBillQuantiy";

    // 保存物资验收
    private static final String SAVE_ACCEPTANCE_URL = "/thirdapi/acceptance/externalSystem/saveAcceptance";

    // 清除id
    private static final String CLEAR_RELATION_ID_URL = "/thirdapi/acceptance/clearRelationId?id=";

    // 检查外部单据是否能作废
    private static final String CHECK_OUT_BILL_CON_BEINVALIDATED = "/thirdapi/acceptance/checkOutBillCanBeInvalidated?billId=";

    // 是否可以推送对账单
    private static final String IS_CON_OPERA_BILL_URL = "/thirdapi/monthlySettlement/isCanOperaBill";

    @Autowired
    UserService userService;

    @Autowired
    PcwpService pcwpService;

    @Autowired
    RestTemplateUtils restTemplateUtils;

    @Autowired
    InterfaceLogsService interfaceLogsService;

    @Autowired
    TTRabbitMQConnectionTest ttRabbitMQConnectionTest;

    @Resource
    private TTToDoRabbitMQUtil ttToDoRabbitMQUtil;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> q) {
        if (mallConfig.profilesActive.equals("dev")) {
            // 开发环境数据权限控制
            // 要查询的下级机构数据
            // 机构数据查看权限（1本机及子级2只看本级3指定）
            Integer dataSelect = (Integer) jsonObject.get("dataSelect");
            List dataScopes = (List) jsonObject.get("dataScope");
            UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
            String roleName = RoleEnum.ROLE_5.getName();
            List<MallRole> mallRoles = currentUser.getMallRoles();
            if (CollectionUtils.isEmpty(mallRoles) || mallRoles.stream().noneMatch(t -> t.getName().equals(roleName))) {
                throw new BusinessException(500, "当前用户无物资采购平台履约系统权限！");
            }
            MallRole role = mallRoles.stream().filter(t -> t.getName().equals(roleName)).findFirst().get();
            if (dataScopes != null && dataSelect == 1 && role.getOrgSearch() == 1) {

                List<OrgAndSon> orgAndSons = currentUser.getOrgAndSon();
                List<String> collect = orgAndSons.stream().map(t -> t.getOrgId()).collect(Collectors.toList());
//                q.in(!CollectionUtils.isEmpty(collect), MaterialReconciliation::getPurchasingOrgId, collect);
            } else if (dataScopes != null && dataSelect == 2) {
                q.eq(MaterialReconciliation::getPurchasingOrgId, ThreadLocalUtil.getCurrentUser().getOrgId());
            } else if (dataScopes != null && dataSelect == 3 && role.getOrgSearch() == 1) {
                // 指定
//                q.in(!CollectionUtils.isEmpty(dataScopes), MaterialReconciliation::getPurchasingOrgId, dataScopes);
                // 获取当前用户的下级机构（除自己之外）
                if (CollectionUtils.isEmpty(dataScopes)) {
                    List<OrgAndSon> orgAndSons = currentUser.getOrgAndSon();
                    List<String> collect = orgAndSons.stream().map(OrgAndSon::getOrgId).filter(orgId -> !orgId.equals(currentUser.getOrgId())).collect(Collectors.toList());
//                    q.in(!CollectionUtils.isEmpty(collect), MaterialReconciliation::getPurchasingOrgId, collect);
                }
            } else {
                q.eq(MaterialReconciliation::getPurchasingOrgId, ThreadLocalUtil.getCurrentUser().getOrgId());
            }
        } else {
            q.eq(MaterialReconciliation::getPurchasingOrgId, ThreadLocalUtil.getCurrentUser().getOrgId());
        }
        // 查询供应商不包括草稿的数据
        q.and((t) -> {
            t.in(MaterialReconciliation::getCreateType, 1, 3).or(t2 -> {
                t2.eq(MaterialReconciliation::getCreateType, 2).ne(MaterialReconciliation::getState, 0);
            });
        });

        String keywords = (String) jsonObject.get("keywords");
        String title = (String) jsonObject.get("title");
        String supplierName = (String) jsonObject.get("supplierName");
        String purchasingOrgName = (String) jsonObject.get("purchasingOrgName");
        String reconciliationNo = (String) jsonObject.get("reconciliationNo");
        String sourceBillNo = (String) jsonObject.get("sourceBillNo");

        Integer type = (Integer) jsonObject.get("type");
        Integer businessType = (Integer) jsonObject.get("businessType");
        Integer createType = (Integer) jsonObject.get("createType");
        Integer state = (Integer) jsonObject.get("state");
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        Integer reconciliationProductType = (Integer) jsonObject.get("reconciliationProductType");

        q.eq(type != null, MaterialReconciliation::getType, type);
        q.eq(createType != null, MaterialReconciliation::getCreateType, createType);
        q.eq(state != null, MaterialReconciliation::getState, state);
        if (reconciliationProductType != null) { //业务类型空
//            q.in(MaterialReconciliation::getBusinessType, 1, 2);
//            q.in(MaterialReconciliation::getReconciliationProductType, 0, 1,2,10,13);
            if (reconciliationProductType == 0 || reconciliationProductType == 10) { //零星采购
                q.in(MaterialReconciliation::getReconciliationProductType, 0, 10);
            } else if (reconciliationProductType == 1 || reconciliationProductType == 13) { //大宗临购
                q.in(MaterialReconciliation::getReconciliationProductType, 1, 13);
            } else if (reconciliationProductType == 2) {//周转材料
                q.eq(MaterialReconciliation::getReconciliationProductType, reconciliationProductType);
            }
        }
        if (StringUtils.isNotBlank(title)) q.eq(MaterialReconciliation::getTitle, title.trim());
        if (StringUtils.isNotBlank(supplierName)) q.eq(MaterialReconciliation::getSupplierName, supplierName.trim());
        if (StringUtils.isNotBlank(purchasingOrgName))
            q.eq(MaterialReconciliation::getPurchasingOrgName, purchasingOrgName.trim());
        if (StringUtils.isNotBlank(reconciliationNo))
            q.eq(MaterialReconciliation::getReconciliationNo, reconciliationNo.trim());
        if (StringUtils.isNotBlank(sourceBillNo)) q.eq(MaterialReconciliation::getSourceBillNo, sourceBillNo.trim());

        if (orderBy != null) {
            if (orderBy == 0) {
                q.orderByDesc(MaterialReconciliation::getGmtCreate);
            }
            if (orderBy == 1) {
                q.orderByDesc(MaterialReconciliation::getStartTime);
            }
            if (orderBy == 2) {
                q.orderByDesc(MaterialReconciliation::getEndTime);
            }
        } else {
            q.orderByDesc(MaterialReconciliation::getGmtCreate);
        }
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(MaterialReconciliation::getReconciliationNo, keywords.trim()).or().like(MaterialReconciliation::getSupplierName, keywords.trim()).or().like(MaterialReconciliation::getPurchasingOrgName, keywords.trim());

            });
        }
        IPage<MaterialReconciliation> page = this.page(new Query<MaterialReconciliation>().getPage(jsonObject), q);
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(MaterialReconciliation d, int isNotPush) {
        String keyId = d.getKeyId();
        // 处理金额
        getPlanContractAmount(d);
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        d.setAcceptanceId(user.getFarUserId());
        d.setAcceptanceName(user.getUserName());
        d.setOrgName(user.getEnterpriseName());
        d.setOrgId(user.getOrgId());
        String materialPlanNo = createMaterialPlanNo(d.getPurchasingOrgId());
        d.setReconciliationNo(materialPlanNo);
        d.setEnterpriseId(user.getEnterpriseId());
        if (mallConfig.isPlatformFee == 1) {
            // 调用缴费交易费用接口
            platformDealFeeDtlService.checkDealAmount(d, 0);
        }
        d.setState(0);
        if (d.getIsSubmit() == 1) {
            d.setState(2);
        }
        boolean save = save(d);
        if (save) {
            for (MaterialReconciliationDtl md : d.getDtl()) {
                if (isNotPush == 1) {
                    isWarehouseIdExist(md);
                }
                md.setReconciliationId(d.getReconciliationId());
                setProductNameAndProductName(md);
                materialReconciliationDtlService.save(md);
            }
            if (isNotPush == 1) { //推送PCWP
                PcwpRes<Void> res;
                Object logData;
                String logOperation;
                if (d.getReconciliationProductType() == 2) {//周转材料
                    List<UpdatePlanDtl> updatePlanDtls = getUpdateQuCountMForRevol(d);
                    KeyedPayload<List<UpdatePlanDtl>> payload = KeyedPayload.<List<UpdatePlanDtl>>builder().data(updatePlanDtls).keyId(keyId).orgId(d.getPurchasingOrgId()).build();
                    res = pcwpService.updateRevolPlanDtl(payload);
                    logData = payload;
                    logOperation = "create";
                } else {//零星采购或者大宗临购
                    List<ReconciliationDtl> reconciliationDtls = getReconciliationDtls(d);
                    KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder().data(reconciliationDtls).keyId(keyId).orgId(d.getPurchasingOrgId()).build();
                    res = pcwpService.writeBackBillLockQuantiy(payload);
                    logData = payload;
                    logOperation = "create";
                }
                if (res.getCode() == null || res.getCode() != 200) {
                    throw new BusinessException("【远程异常】" + res.getMessage());
                }
                createAndSaveInterfaceLog(keyId, logOperation, d, logData, res, 1, 1, null);
            }
        }
        return d.getReconciliationNo();
    }


    /**
     * 新增对账单每次统计暂扣数量
     *
     * @param d
     * @return
     */
    @NotNull
    private List<Map<String, Object>> getUpdateQuCountM(MaterialReconciliation d) {
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        List<String> ids = lambdaQuery().eq(MaterialReconciliation::getSourceBillId, d.getSourceBillId()).eq(MaterialReconciliation::getBusinessType, d.getBusinessType()).in(MaterialReconciliation::getState, 0, 1, 2, 4).select(MaterialReconciliation::getReconciliationId).list().stream().map(t -> t.getReconciliationId()).collect(Collectors.toList());
        for (MaterialReconciliationDtl dtl1 : d.getDtl()) {
            HashMap<String, Object> map = new HashMap<>();
            List<MaterialReconciliationDtl> dtlQuList = materialReconciliationDtlService.lambdaQuery().in(MaterialReconciliationDtl::getReconciliationId, ids).eq(MaterialReconciliationDtl::getReceiptBillDtlId, dtl1.getReceiptBillDtlId()).select(MaterialReconciliationDtl::getQuantity, MaterialReconciliationDtl::getAcceptanceAmount).list();
            // 计算数量
            BigDecimal addQu = new BigDecimal(0);
            BigDecimal addAmount = new BigDecimal(0);
            for (MaterialReconciliationDtl materialReconciliationDtl : dtlQuList) {
                addQu = addQu.add(materialReconciliationDtl.getQuantity());
                addAmount = addAmount.add(materialReconciliationDtl.getAcceptanceAmount());
            }
            map.put("quantity", addQu);
            map.put("amount", addAmount);
            map.put("dtlId", dtl1.getReceiptBillDtlId());
            map.put("orgId", d.getPurchasingOrgId());
            maps.add(map);
        }

        // 根据属性判断重复，并删除重复的 Map 对象
        List<Map<String, Object>> uniqueMaps = removeDuplicateByProperty(maps, "dtlId");
        return uniqueMaps;
    }


    /**
     * 将MaterialReconciliationDtl列表转换为ReconciliationDtl列表（按dtlId分组聚合，金额和数量取负值）
     *
     * @param dtlList MaterialReconciliationDtl列表
     * @return ReconciliationDtl列表
     */
    @NotNull
    private List<ReconciliationDtl> convertToReconciliationDtlsWithNegate(List<MaterialReconciliationDtl> dtlList) {
        List<ReconciliationDtl> reconciliationDtls = new ArrayList<>();
        Map<String, List<MaterialReconciliationDtl>> collect = dtlList.stream().collect(Collectors.groupingBy(MaterialReconciliationDtl::getReceiptBillDtlId));

        for (Map.Entry<String, List<MaterialReconciliationDtl>> entry : collect.entrySet()) {
            String dtlId = entry.getKey();
            List<MaterialReconciliationDtl> dataList = entry.getValue();
            BigDecimal addQu = new BigDecimal(0);
            BigDecimal addAmount = new BigDecimal(0);
            for (MaterialReconciliationDtl materialReconciliationDtl : dataList) {
                addQu = addQu.add(materialReconciliationDtl.getQuantity());
                addAmount = addAmount.add(materialReconciliationDtl.getAcceptanceAmount());
            }
            // 取负值并设置精度
            addAmount = addAmount.negate().setScale(4, BigDecimal.ROUND_HALF_UP);
            addQu = addQu.negate().setScale(4, BigDecimal.ROUND_HALF_UP);

            ReconciliationDtl reconciliationDtl = ReconciliationDtl.builder().dtlId(dtlId).amount(addAmount).quantity(addQu).build();
            reconciliationDtls.add(reconciliationDtl);
        }
        return reconciliationDtls;
    }

    /**
     * 将MaterialReconciliationDtl列表转换为ReconciliationDtl列表（按dtlId分组聚合）
     *
     * @param dtlList MaterialReconciliationDtl列表
     * @return ReconciliationDtl列表
     */
    @NotNull
    private List<ReconciliationDtl> convertToReconciliationDtls(List<MaterialReconciliationDtl> dtlList) {
        List<ReconciliationDtl> reconciliationDtls = new ArrayList<>();
        Map<String, List<MaterialReconciliationDtl>> collect = dtlList.stream().collect(Collectors.groupingBy(MaterialReconciliationDtl::getReceiptBillDtlId));

        for (Map.Entry<String, List<MaterialReconciliationDtl>> entry : collect.entrySet()) {
            String dtlId = entry.getKey();
            List<MaterialReconciliationDtl> dataList = entry.getValue();
            BigDecimal addQu = new BigDecimal(0);
            BigDecimal addAmount = new BigDecimal(0);
            for (MaterialReconciliationDtl materialReconciliationDtl : dataList) {
                addQu = addQu.add(materialReconciliationDtl.getQuantity());
                addAmount = addAmount.add(materialReconciliationDtl.getAcceptanceAmount());
            }
            ReconciliationDtl reconciliationDtl = ReconciliationDtl.builder().dtlId(dtlId).amount(addAmount).quantity(addQu).build();
            reconciliationDtls.add(reconciliationDtl);
        }
        return reconciliationDtls;
    }

    /**
     * 新增对账单每次统计暂扣数量（返回ReconciliationDtl对象）
     *
     * @param d
     * @return
     */
    @NotNull
    private List<ReconciliationDtl> getReconciliationDtls(MaterialReconciliation d) {
        ArrayList<ReconciliationDtl> reconciliationDtls = new ArrayList<>();
        List<String> ids = lambdaQuery().eq(MaterialReconciliation::getSourceBillId, d.getSourceBillId()).eq(MaterialReconciliation::getBusinessType, d.getBusinessType()).in(MaterialReconciliation::getState, 0, 1, 2, 4).select(MaterialReconciliation::getReconciliationId).list().stream().map(t -> t.getReconciliationId()).collect(Collectors.toList());
        for (MaterialReconciliationDtl dtl1 : d.getDtl()) {
            List<MaterialReconciliationDtl> dtlQuList = materialReconciliationDtlService.lambdaQuery().in(MaterialReconciliationDtl::getReconciliationId, ids).eq(MaterialReconciliationDtl::getReceiptBillDtlId, dtl1.getReceiptBillDtlId()).select(MaterialReconciliationDtl::getQuantity, MaterialReconciliationDtl::getAcceptanceAmount).list();
            // 计算数量
            BigDecimal addQu = new BigDecimal(0);
            BigDecimal addAmount = new BigDecimal(0);
            for (MaterialReconciliationDtl materialReconciliationDtl : dtlQuList) {
                addQu = addQu.add(materialReconciliationDtl.getQuantity());
                addAmount = addAmount.add(materialReconciliationDtl.getAcceptanceAmount());
            }
            ReconciliationDtl reconciliationDtl = ReconciliationDtl.builder().dtlId(dtl1.getReceiptBillDtlId()).quantity(addQu).amount(addAmount).build();
            reconciliationDtls.add(reconciliationDtl);
        }

        // 根据dtlId去重
        Map<String, ReconciliationDtl> uniqueMap = new LinkedHashMap<>();
        for (ReconciliationDtl reconciliationDtl : reconciliationDtls) {
            uniqueMap.put(reconciliationDtl.getDtlId(), reconciliationDtl);
        }
        return new ArrayList<>(uniqueMap.values());
    }

    /**
     * 新增对账单每次统计暂扣数量（周转材料专用）
     *
     * @param d
     * @return
     */
    @NotNull
    private List<UpdatePlanDtl> getUpdateQuCountMForRevol(MaterialReconciliation d) {
        ArrayList<UpdatePlanDtl> updatePlanDtls = new ArrayList<>();
        List<String> ids = lambdaQuery().eq(MaterialReconciliation::getSourceBillId, d.getSourceBillId()).eq(MaterialReconciliation::getBusinessType, d.getBusinessType()).in(MaterialReconciliation::getState, 0, 1, 2, 4).select(MaterialReconciliation::getReconciliationId).list().stream().map(t -> t.getReconciliationId()).collect(Collectors.toList());
        for (MaterialReconciliationDtl dtl1 : d.getDtl()) {
            List<MaterialReconciliationDtl> dtlQuList = materialReconciliationDtlService.lambdaQuery().in(MaterialReconciliationDtl::getReconciliationId, ids).eq(MaterialReconciliationDtl::getReceiptBillDtlId, dtl1.getReceiptBillDtlId()).select(MaterialReconciliationDtl::getQuantity, MaterialReconciliationDtl::getAcceptanceAmount).list();
            // 计算数量
            BigDecimal addQu = new BigDecimal(0);
            BigDecimal addAmount = new BigDecimal(0);
            for (MaterialReconciliationDtl materialReconciliationDtl : dtlQuList) {
                addQu = addQu.add(materialReconciliationDtl.getQuantity());
                addAmount = addAmount.add(materialReconciliationDtl.getAcceptanceAmount());
            }
            UpdatePlanDtl updatePlanDtl = UpdatePlanDtl.builder().dtlId(dtl1.getReceiptBillDtlId()).billId(d.getSourceBillId()).number(addQu).amount(addAmount).build();
            updatePlanDtls.add(updatePlanDtl);
        }

        // 根据dtlId去重
        Map<String, UpdatePlanDtl> uniqueMap = new LinkedHashMap<>();
        for (UpdatePlanDtl updatePlanDtl : updatePlanDtls) {
            uniqueMap.put(updatePlanDtl.getDtlId(), updatePlanDtl);
        }
        return new ArrayList<>(uniqueMap.values());
    }

    public static List<Map<String, Object>> removeDuplicateByProperty(ArrayList<Map<String, Object>> maps, String property) {
        Set<Object> uniqueValues = new HashSet<>();
        List<Map<String, Object>> uniqueMaps = new ArrayList<>();

        for (Map<String, Object> map : maps) {
            Object value = map.get(property);
            if (!uniqueValues.contains(value)) {
                uniqueValues.add(value);
                uniqueMaps.add(map);
            }
        }
        return uniqueMaps;
    }

    private static BigDecimal getCreateAmount(MaterialReconciliation d, BigDecimal reconciliationAmount, Integer type, MathContext mathContext) {
        // 浮动价格
        if (type == 1) {
            for (MaterialReconciliationDtl md : d.getDtl()) {

                BigDecimal quantity = md.getQuantity();
                if (quantity == null) {
                    throw new BusinessException("数量不能为空！");
                }
                md.setSourceQuantity(quantity);
                Integer updateType = md.getUpdateType();
                // 未修改单价
                if (updateType == null) {
                    BigDecimal acceptanceAmount = md.getAcceptanceAmount();
                    // 获得平均价
                    BigDecimal price = acceptanceAmount.divide(quantity, new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    md.setPrice(price);
                    md.setNoRatePrice(TaxCalculator.calculateNotTarRateAmount(price, d.getTaxRate()));

                    md.setFreightPrice(price);
                    md.setFixationPrice(new BigDecimal(0));
                    reconciliationAmount = reconciliationAmount.add(acceptanceAmount);
                }
                // 修改了单价
                if (updateType != null && updateType == 1) {
                    BigDecimal freightPrice = md.getFreightPrice();
                    BigDecimal fixationPrice = md.getFixationPrice();
                    if (freightPrice == null) {
                        throw new BusinessException("到货网价不能为空！");
                    }
                    if (fixationPrice == null) {
                        fixationPrice = new BigDecimal(0);
                    }
                    // 含税
                    BigDecimal addPrice = freightPrice.add(fixationPrice);
                    BigDecimal amount = md.getQuantity().multiply(addPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                    md.setAcceptanceAmount(amount);
                    BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(addPrice, d.getTaxRate());
                    md.setNoRatePrice(noRatePrice);
                    md.setAcceptanceNoRateAmount(TaxCalculator.noTarRateItemAmount(amount, noRatePrice, md.getQuantity(), d.getTaxRate()));
                    reconciliationAmount = reconciliationAmount.add(amount);
                }
            }
        }

        // 固定价格
        if (type == 2) {
            for (MaterialReconciliationDtl md : d.getDtl()) {
                BigDecimal quantity = md.getQuantity();
                if (quantity == null) {
                    throw new BusinessException("数量不能为空！");
                }
                md.setSourceQuantity(quantity);
                Integer updateType = md.getUpdateType();
                // 未修改单价
                if (updateType == null) {
                    BigDecimal acceptanceAmount = md.getAcceptanceAmount();
                    // 获得平均价
                    BigDecimal price = acceptanceAmount.divide(quantity, new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    md.setPrice(price);
                    md.setNoRatePrice(TaxCalculator.calculateNotTarRateAmount(price, d.getTaxRate()));
                    md.setFreightPrice(new BigDecimal(0));
                    md.setFixationPrice(new BigDecimal(0));
                    reconciliationAmount = reconciliationAmount.add(acceptanceAmount);
                }
                // 修改了单价
                if (updateType != null && updateType == 1) {
                    BigDecimal amount = quantity.multiply(md.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    md.setFreightPrice(new BigDecimal(0));
                    md.setFixationPrice(new BigDecimal(0));
                    md.setAcceptanceAmount(amount);
                    BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(md.getPrice(), d.getTaxRate());
                    md.setNoRatePrice(noRatePrice);
                    md.setAcceptanceNoRateAmount(TaxCalculator.noTarRateItemAmount(amount, noRatePrice, quantity, d.getTaxRate()));
                    reconciliationAmount = reconciliationAmount.add(amount);
                }
            }
        }
        return reconciliationAmount;
    }

    private static void getPlanContractAmountUpdate(MaterialReconciliationUpdateDTO d) {
        BigDecimal reconciliationAmount = new BigDecimal(0);
        BigDecimal reconciliationNoRateAmount1 = new BigDecimal(0);
        for (MaterialReconciliationDtl md : d.getDtl()) {
            BigDecimal price = md.getPrice();
            BigDecimal amount = md.getQuantity().multiply(price).setScale(2, BigDecimal.ROUND_HALF_UP);
            md.setAcceptanceAmount(amount);
            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(price, d.getTaxRate());
            md.setNoRatePrice(noRatePrice);
            BigDecimal nRateAmount = TaxCalculator.noTarRateItemAmount(amount, noRatePrice, md.getQuantity(), d.getTaxRate());
            md.setAcceptanceNoRateAmount(nRateAmount);
            reconciliationAmount = reconciliationAmount.add(amount);
            reconciliationNoRateAmount1 = reconciliationNoRateAmount1.add(nRateAmount);
        }
        // 校验前端计算的金额和后端是否一致
        if (reconciliationAmount.compareTo(d.getReconciliationAmount()) != 0) {
            throw new BusinessException("含税总金额计算不一致！");
        }
        BigDecimal reconciliationNoRateAmount = TaxCalculator.noTarRateAmount(reconciliationNoRateAmount1, reconciliationAmount, d.getTaxRate());
        if (reconciliationNoRateAmount.compareTo(d.getReconciliationNoRateAmount()) != 0) {
            throw new BusinessException("不含税总金额计算不一致！");
        }
        d.setReconciliationAmount(reconciliationAmount);
        d.setReconciliationNoRateAmount(reconciliationNoRateAmount);
    }


    private static void getPlanContractAmount(MaterialReconciliation d) {
        BigDecimal reconciliationAmount = new BigDecimal(0);
        BigDecimal reconciliationAddNoRate = new BigDecimal(0);
        for (MaterialReconciliationDtl md : d.getDtl()) {
            md.setSourceQuantity(md.getQuantity());
            BigDecimal price = md.getPrice();
            BigDecimal amount = md.getQuantity().multiply(price).setScale(2, BigDecimal.ROUND_HALF_UP);
            if (amount.compareTo(md.getAcceptanceAmount()) != 0) {
                BigDecimal acceptanceAmount = md.getAcceptanceAmount();
                System.out.println(amount);

            }
            md.setAcceptanceAmount(amount);
            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(price, d.getTaxRate());
            md.setNoRatePrice(noRatePrice);
            BigDecimal nRateAmount = TaxCalculator.noTarRateItemAmount(amount, noRatePrice, md.getQuantity(), d.getTaxRate());
            md.setAcceptanceNoRateAmount(nRateAmount);
            reconciliationAmount = reconciliationAmount.add(amount);
            reconciliationAddNoRate = reconciliationAddNoRate.add(nRateAmount);
        }
        // 校验前端计算的金额和后端是否一致
        if (reconciliationAmount.compareTo(d.getReconciliationAmount()) != 0) {
            throw new BusinessException("含税总金额计算不一致！");
        }
        BigDecimal reconciliationNoRateAmount = TaxCalculator.noTarRateAmount(reconciliationAddNoRate, reconciliationAmount, d.getTaxRate());
        if (reconciliationNoRateAmount.compareTo(d.getReconciliationNoRateAmount()) != 0) {
            throw new BusinessException("不含税总金额计算不一致！");
        }
        d.setReconciliationAmount(reconciliationAmount);
        d.setReconciliationNoRateAmount(reconciliationNoRateAmount);
    }

    @Override
    public String createMaterialPlanNo(String orgId) {
        EnterpriseInfo byId = enterpriseInfoService.getById(orgId);
//        EnterpriseInfo byId = enterpriseInfoService.findByInteriorId(orgId);
        String shortCode = byId.getShortCode();
        Date now = new Date();
        String yyyYmm = DateUtil.getYYYYmmdd(new Date(), "yyyyMMdd");
        String planNo = shortCode + "CLDZ" + yyyYmm;
        List<MaterialReconciliation> list = lambdaQuery().like(MaterialReconciliation::getReconciliationNo, planNo).orderByDesc(MaterialReconciliation::getReconciliationId).last("LIMIT 1").list();
        if (list != null && list.size() > 0) {
            String s = list.get(0).getReconciliationNo();
            String substring = s.substring(s.length() - 3);
            int number = Integer.parseInt(substring);
            number++;
            String format = String.format("%03d", number);
            planNo = planNo + format;
        } else {
            planNo = planNo + "001";
        }
        return planNo;
    }

    @Override
    public List<MaterialReconciliation> ListByIds(List<String> reconciliationIds) {
        List<MaterialReconciliation> list = lambdaQuery().in(MaterialReconciliation::getReconciliationId, reconciliationIds).list();
        return list;
    }

    /**
     * 大宗金额重新计算
     *
     * @param d
     */
    private static void getCreateSTAmount(MaterialReconciliation d) {
        BigDecimal reconciliationAmount = new BigDecimal(0);
        BigDecimal reconciliationNoRateAmount = new BigDecimal(0);
        // 浮动价格
        for (MaterialReconciliationDtl md : d.getDtl()) {
            md.setSourceQuantity(md.getQuantity());
            if (md.getQuantity() == null) {
                throw new BusinessException("数量不能为空！");
            }
//            Integer type = d.getType();
            Integer type = 1;
            if (type == null) {
                throw new BusinessException("价格类型错误！");
            }
            BigDecimal price = null;
            if (type == 1) {
                BigDecimal freightPrice = md.getFreightPrice();
                BigDecimal fixationPrice = md.getFixationPrice();
                price = freightPrice.add(fixationPrice);

            }
            if (type == 2) {
                BigDecimal outFactoryPrice = md.getOutFactoryPrice();
                BigDecimal transportPrice = md.getTransportPrice();
                price = outFactoryPrice.add(transportPrice);
            }
            BigDecimal taxRate = d.getTaxRate() == null ? new BigDecimal(0) : d.getTaxRate();
            BigDecimal amount = md.getQuantity().multiply(price).setScale(2, BigDecimal.ROUND_HALF_UP);
            md.setAcceptanceAmount(amount);
            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(price, taxRate);
            md.setNoRatePrice(noRatePrice);
            BigDecimal nRateAmount = TaxCalculator.noTarRateItemAmount(amount, noRatePrice, md.getQuantity(), taxRate);
            md.setAcceptanceNoRateAmount(nRateAmount);
            reconciliationAmount = reconciliationAmount.add(amount);
            reconciliationNoRateAmount = reconciliationNoRateAmount.add(nRateAmount);
        }
        // 校验前端计算的金额和后端是否一致
        if (reconciliationAmount.compareTo(d.getReconciliationAmount()) != 0) {
//            throw new BusinessException("含税总金额计算不一致！");
        }
//        BigDecimal reconciliationNoRateAmount = TaxCalculator.calculateNotTarRateAmount(reconciliationAmount, d.getTaxRate());
        if (reconciliationNoRateAmount.compareTo(d.getReconciliationNoRateAmount()) != 0) {
//            throw new BusinessException("不含税总金额计算不一致！");
        }
        d.setReconciliationAmount(reconciliationAmount);
        d.setReconciliationNoRateAmount(reconciliationNoRateAmount);
    }


    private static void getCreateSTAmountByUpdate(MaterialReconciliationUpdateDTO d) {
        BigDecimal reconciliationAmount = new BigDecimal(0);
        BigDecimal reconciliationNoRateAmount = new BigDecimal(0);
        // 浮动价格
        for (MaterialReconciliationDtl md : d.getDtl()) {
            if (md.getQuantity() == null) {
                throw new BusinessException("数量不能为空！");
            }
            Integer type = d.getType();
            if (type == null) {
                throw new BusinessException("价格类型错误！");
            }
            BigDecimal price = null;
            if (type == 1) {
                BigDecimal freightPrice = md.getFreightPrice();
                BigDecimal fixationPrice = md.getFixationPrice();
                price = freightPrice.add(fixationPrice);

            }
            if (type == 2) {
                BigDecimal outFactoryPrice = md.getOutFactoryPrice();
                BigDecimal transportPrice = md.getTransportPrice();
                price = outFactoryPrice.add(transportPrice);
            }
            BigDecimal amount = md.getQuantity().multiply(price).setScale(2, BigDecimal.ROUND_HALF_UP);
            md.setAcceptanceAmount(amount);
            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(price, d.getTaxRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
            md.setNoRatePrice(noRatePrice);
            BigDecimal nRateAmount = TaxCalculator.noTarRateItemAmount(amount, noRatePrice, md.getQuantity(), d.getTaxRate());
            md.setAcceptanceNoRateAmount(nRateAmount);
            reconciliationAmount = reconciliationAmount.add(amount);
            reconciliationNoRateAmount = reconciliationNoRateAmount.add(nRateAmount);
        }
        // 校验前端计算的金额和后端是否一致
        if (reconciliationAmount.compareTo(d.getReconciliationAmount()) != 0) {
            throw new BusinessException("含税总金额计算不一致！");
        }
//        BigDecimal reconciliationNoRateAmount = TaxCalculator.calculateNotTarRateAmount(reconciliationAmount, d.getTaxRate());
        if (reconciliationNoRateAmount.compareTo(d.getReconciliationNoRateAmount()) != 0) {
            throw new BusinessException("不含税总金额计算不一致！");
        }
        d.setReconciliationAmount(reconciliationAmount);
        d.setReconciliationNoRateAmount(reconciliationNoRateAmount);
    }

    @Override
    public void update(MaterialReconciliation materialReconciliation) {
        super.updateById(materialReconciliation);
    }

    @Override
    public MaterialReconciliation getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 新增对账单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createMaterialReconciliation(MaterialReconciliation d) {
        d.setReconciliationId(null);
        d.setReconciliationNo(null);
        String orgShort = d.getOrgShort();
        String creditCode = d.getCreditCode();
        Integer count = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getShortCode, orgShort).count();
        Integer count2 = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getSocialCreditCode, creditCode).count();

        if (count2 == 0 && count == 0) {
            EnterpriseInfo one = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseName, d.getSupplierName()).one();
            if (one == null) {
                throw new BusinessException("物资采购平未找到供应商信息！");
            } else {
                d.setOrgShort(one.getShortCode());
                d.setCreditCode(one.getSocialCreditCode());
            }
        }
        // 清除远程传的数据
        d.setSupplierIsAffirm(0);
        d.setPurchaseIsAffirm(0);
        d.setSupplierAffirmTime(null);
        d.setPurchaseAffirmTime(null);

        String idStr = d.getKeyId();
        String materialPlanNo = createMaterialPlanNo(d.getPurchasingOrgId());
        d.setReconciliationNo(materialPlanNo);
        Integer businessType = d.getBusinessType();
        if (businessType == null) {
            throw new BusinessException("业务类型错误！");
        }
        if (businessType != 6) {
            d.setType(2);
        } else {
            if (d.getType() == null) {
                throw new BusinessException("未携带对账价格类型！");
            }
        }
        // 合同
        if (businessType == 1) {
            d.setReconciliationProductType(12);
            if (mallConfig.isPlatformFee == 1) {
                // 调用缴费交易费用接口
                platformDealFeeDtlService.checkDealAmount(d, 0);
            }
        }
        if (businessType == 2) {
            d.setReconciliationProductType(10);
            if (mallConfig.isPlatformFee == 1) {
                // 调用缴费交易费用接口
                platformDealFeeDtlService.checkDealAmount(d, 0);
            }
        }
        if (businessType == 6) {
            // 大宗查询
            d.setReconciliationProductType(13);
            String orderId = d.getDtl().get(0).getOrderId();
            if (StringUtils.isEmpty(orderId)) {
                throw new BusinessException("订单id不能为空！");
            }
            Orders one = ordersService.lambdaQuery().eq(Orders::getOrderId, orderId).select(Orders::getPaymentWeek, Orders::getOutPhaseInterest).one();
            if (one == null) {
                throw new BusinessException("订单不存在！");
            }
            d.setPaymentWeek(one.getPaymentWeek());
            d.setOutPhaseInterest(one.getOutPhaseInterest());

            BigDecimal toAmount = new BigDecimal(0);
            for (int i = 0; i < d.getDtl().size(); i++) {
                MaterialReconciliationDtl md = d.getDtl().get(i);
                md.setReconciliationDtlId(null);
                if (md.getQuantity() == null) {
                    throw new BusinessException("数量不能为空！");
                }
                Integer type = d.getType();
                if (type == null) {
                    throw new BusinessException("价格类型错误！");
                }
                if (type == 1) {
                    BigDecimal freightPrice = md.getFreightPrice();
                    BigDecimal fixationPrice = md.getFixationPrice();
                    if ((freightPrice == null || fixationPrice == null) || (freightPrice.compareTo(BigDecimal.ZERO) == 0 || fixationPrice.compareTo(BigDecimal.ZERO) == 0)) {
                        throw new BusinessException("商品：【" + md.getMaterialName() + "】未填入网价或固定费用！");
                    }
                    // 计算含税
                    BigDecimal pr1 = TaxCalculator.calculateYesTarRateAmount(freightPrice, d.getTaxRate());
                    BigDecimal pr2 = TaxCalculator.calculateYesTarRateAmount(fixationPrice, d.getTaxRate());
                    md.setFreightPrice(pr1);
                    md.setFixationPrice(pr2);
                } else if (type == 2) {
                    BigDecimal outFactoryPrice = md.getOutFactoryPrice();
                    BigDecimal transportPrice = md.getTransportPrice();
                    if ((outFactoryPrice == null || transportPrice == null) || (outFactoryPrice.compareTo(BigDecimal.ZERO) == 0 || transportPrice.compareTo(BigDecimal.ZERO) == 0)) {
                        throw new BusinessException("商品：【" + md.getMaterialName() + "】未填入出厂价或运杂费！");
                    }
                    BigDecimal pr1 = TaxCalculator.calculateYesTarRateAmount(outFactoryPrice, d.getTaxRate());
                    BigDecimal pr2 = TaxCalculator.calculateYesTarRateAmount(transportPrice, d.getTaxRate());
                    md.setOutFactoryPrice(pr1);
                    md.setTransportPrice(pr2);
                } else {
                    throw new BusinessException("价格类型错误！");
                }
                // 计算含税金额
                if (i == d.getDtl().size() - 1) {
                    BigDecimal amount = d.getReconciliationAmount().subtract(toAmount);
                    md.setAcceptanceAmount(amount);
                    md.setPrice(amount.divide(md.getQuantity(), new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP));
                } else {
                    BigDecimal amount = TaxCalculator.calculateYesTarRateAmount(md.getAcceptanceNoRateAmount(), d.getTaxRate());
                    md.setAcceptanceAmount(amount);
                    md.setPrice(amount.divide(md.getQuantity(), new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP));
                    toAmount = toAmount.add(amount);
                }
                setProductNameAndProductName(md);
                materialReconciliationDtlService.save(md);
            }
        }
        d.setCreateType(3);
        d.setState(0);
        boolean save = save(d);
        if (save) {
            if (d.getBusinessType() == 6) {
                for (MaterialReconciliationDtl md : d.getDtl()) {
                    md.setReconciliationDtlId(null);
                    md.setReconciliationId(d.getReconciliationId());
                    materialReconciliationDtlService.save(md);
                }
            } else {
                BigDecimal toAmount = new BigDecimal(0);
                for (int i = 0; i < d.getDtl().size(); i++) {
                    MaterialReconciliationDtl md = d.getDtl().get(i);
                    md.setReconciliationDtlId(null);
                    md.setReconciliationId(d.getReconciliationId());

                    // 计算含税金额
                    if (i == d.getDtl().size() - 1) {
                        BigDecimal amount = d.getReconciliationAmount().subtract(toAmount);
                        md.setAcceptanceAmount(amount);
                        md.setPrice(amount.divide(md.getQuantity(), new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP));
                    } else {
                        BigDecimal amount = TaxCalculator.calculateYesTarRateAmount(md.getAcceptanceNoRateAmount(), d.getTaxRate());
                        md.setAcceptanceAmount(amount);
                        md.setPrice(amount.divide(md.getQuantity(), new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP));
                        toAmount = toAmount.add(amount);
                    }
                    materialReconciliationDtlService.save(md);
                }
            }
        }
        // 成功保存日志
        createAndSaveInterfaceLog(idStr, "createMaterialReconciliation", d, d, d.getReconciliationId(), 1, 3, null);
        return d.getReconciliationId();
    }

    /**
     * 回滚对账单新增
     *
     * @param keyId
     */
    @Override
    public void rollBackReconciliationCreate(String keyId) {
        InterfaceLogs interfaceLogs = interfaceLogsService.lambdaQuery().eq(InterfaceLogs::getSecretKey, keyId).eq(InterfaceLogs::getIsSuccess, 1).eq(InterfaceLogs::getLogType, 3).one();
        if (interfaceLogs != null) {
            String localArguments = interfaceLogs.getLocalArguments();
            Map map = JSON.parseObject(localArguments, Map.class);
            String reconciliationId = (String) map.get("reconciliationId");
            delete(reconciliationId);
        }

        // 成功保存日志
        createAndSaveInterfaceLog(keyId, "rollBackReconciliationCreate", null, null, null, 1, 4, null);
    }

    @Autowired
    OrderItemService orderItemService;

    @Override
    public MaterialReconciliation materialReconciliationGetBySn(String sn) {
        MaterialReconciliation one = lambdaQuery().eq(MaterialReconciliation::getReconciliationNo, sn).one();
        if (one == null) return one;
        List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, one.getReconciliationId()).orderByDesc(MaterialReconciliationDtl::getOrderSn, MaterialReconciliationDtl::getQuantity, MaterialReconciliationDtl::getMaterialName, MaterialReconciliationDtl::getAcceptanceAmount).list();
//        if (one.getBusinessType() == 6) {
////            for (MaterialReconciliationDtl item : list) {
////                OrderItem one1 = orderItemService.lambdaQuery().eq(OrderItem::getOrderItemId, item.getOrderDtlId())
////                        .select(OrderItem::getProductName).one();
////                if (one1 != null) {
////                    item.setMaterialName(one1.getProductName());
////                }
////            }
//        }
        one.setDtl(list);
        List<AuditRecord> r = auditRecordService.lambdaQuery().eq(AuditRecord::getRelevanceId, one.getReconciliationId()).eq(AuditRecord::getRelevanceType, 5).list();
        one.setAuditRecords(r);
        return one;
    }

    /**
     * 批量提交审核
     *
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationSubmitCheck(List<String> ids) {
        List<MaterialReconciliation> list = lambdaQuery().in(MaterialReconciliation::getReconciliationId, ids).select(MaterialReconciliation::getReconciliationId, MaterialReconciliation::getState).list();
        if (!CollectionUtils.isEmpty(list)) {
            for (MaterialReconciliation materialReconciliation : list) {
                Integer state = materialReconciliation.getState();
                if (state == 0 || state == 1 || state == 4) {
                    // 推送TT代
                    ttRabbitMQConnectionTest.testConnection();
//                    String EMPLOYEE_NUMBER = "036529";
//                    String USER_ID = "391E2FB8-F295-4045-8CDC-340AD3DE6700";
//                    // 1. 创建待办消息体
//                    ToDoMessageBody todo = TTToDoRabbitMQUtil.createTodoBody(
//                            "对账单编号"+materialReconciliation.getReconciliationNo(), // 待办唯一ID
//                            EMPLOYEE_NUMBER,                                 // 员工号：036529
//                            USER_ID,                                        // TT用户ID
//                            "对账单审核",                               // 标题
//                            "对账单审核",                     // 描述信息
//                            ""                              // 跳转URL
//                    );
//
//                    // 2. 推送TT代办
//                    TTToDoRabbitMQUtil.sendSingleToDo(todo);
                    materialReconciliation.setState(2);
                    update(materialReconciliation);
                }
            }
        }
    }

    /**
     * 审核对账单
     *
     * @param dto
     * @param keyId
     * @param farArg
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationAuditPlan(AuditDTO dto, String keyId, StringBuilder farArg, int isNotPush) {

        String id = dto.getId();
        MaterialReconciliation byId = lambdaQuery().eq(MaterialReconciliation::getReconciliationId, id).one();
        if (byId == null) {
            throw new BusinessException("对账单不存在！");
        } else {
            Integer state1 = byId.getState();
            Integer isOpen = dto.getIsOpen();
            // 是待审核才进行审核
            if (state1 == 2 && isOpen != null) {
                // 通过
                if (isOpen == 1) {
                    createApprovalAuditRecord(id);
                    byId.setState(2);
                    lambdaUpdate().eq(MaterialReconciliation::getReconciliationId, id).set(MaterialReconciliation::getState, 3).update();
                    //自动生成出库结算单
                    createOutStockSettlement(byId);
                    if (mallConfig.isPlatformFee == 1) {
                        // 调用缴费交易费用接口
                        platformDealFeeDtlService.checkDealAmount(byId, 1);
                    }
                    // 如果是pcwp新增直接返回
//                    if (byId.getCreateType() == 3) {
//                        return null;
//                    }
                    if (isNotPush == 1) {//对账单是否推送PCWP
                        Boolean r11Bool = null;
                        LocalDate currentDate = LocalDate.now();
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
                        String dateString = currentDate.format(formatter);
                        //  判断是否能推送验收单
                        try {
                            PcwpRes<Boolean> pcwpRes = pcwpService.isCanOperaBill(byId.getPurchasingOrgId(), DateUtil.getymd(LocalDate.now()));
                            if (pcwpRes.getCode() == null || pcwpRes.getCode() != 200) {
                                throw new BusinessException("【远程异常】判断是否可推送验收单：" + pcwpRes.getMessage());
                            }
                            r11Bool = pcwpRes.getData();
                        } catch (Exception e) {
                            throw new BusinessException("【远程异常】判断是否可推送验收单：" + e.getMessage());
                        }
                        if (r11Bool != null && r11Bool == true) {
                            // 通过新增草稿
                            HashMap<String, Object> dataMap = new HashMap<>();
                            HashMap<String, Object> data = new HashMap<>();
                            ArrayList<Map> dtls = new ArrayList<>();
                            BigDecimal acceptanceQuantity = new BigDecimal(0);
                            List<MaterialReconciliationDtl> countDtlList = materialReconciliationDtlMapper.getDtlCountListByReconciliationId(id);
                            for (MaterialReconciliationDtl md : countDtlList) {
                                // 保存明细
                                BigDecimal acceptanceNoRateAmount = md.getAcceptanceNoRateAmount();
                                BigDecimal quantity = md.getQuantity();
                                acceptanceQuantity = acceptanceQuantity.add(quantity);
                                HashMap<String, Object> dtl = new HashMap<>();
                                dtl.put("acceptanceAmount", acceptanceNoRateAmount);
                                dtl.put("tradeId", md.getTradeId());
                                dtl.put("orderId", md.getOrderId());
                                dtl.put("orderNo", md.getOrderSn());
                                dtl.put("acceptanceQuantity", quantity);
                                dtl.put("materialClassId", md.getMaterialClassId());
                                dtl.put("materialClassName", md.getMaterialClassName());
                                dtl.put("materialId", md.getMaterialId());
                                dtl.put("materialName", md.getMaterialName());
                                // 单价取均价
                                BigDecimal price = acceptanceNoRateAmount.divide(quantity, new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP);
                                dtl.put("price", price);
                                dtl.put("taxAmount", md.getTaxAmount());
                                dtl.put("spec", md.getSpec());
                                dtl.put("texture", md.getTexture());
                                dtl.put("unit", md.getUnit());
                                dtl.put("warehouseId", md.getWarehouseId());
                                dtl.put("warehouseName", md.getWarehouseName());
                                dtls.add(dtl);
                            }

                            data.put("dtls", dtls);
                            String s = DateUtil.getyyymmddHHmmssStr(byId.getGmtCreate());
                            ;
                            data.put("acceptanceDate", s);
                            data.put("acceptanceQuantity", acceptanceQuantity);
                            data.put("totalAmount", byId.getReconciliationAmount());
                            data.put("taxAmount", byId.getReconciliationAmount().subtract(byId.getReconciliationNoRateAmount()));
                            data.put("acceptanceAmount", byId.getReconciliationNoRateAmount());
                            // 不管是供应商新增还是采购员新增单据统一使用采购员的id
                            data.put("businessType", byId.getBusinessType());

                            if (byId.getCreateType() != 2) {
                                User userOne = userService.lambdaQuery().eq(User::getUserId, byId.getFounderId()).select(User::getInteriorId, User::getNickName).one();
                                data.put("founderId", userOne.getInteriorId());
                                data.put("founderName", userOne.getNickName());
                                data.put("acceptancerId", userOne.getInteriorId());
                                data.put("acceptancerName", userOne.getNickName());
                            } else {
                                data.put("founderId", byId.getPurchaserId());
                                data.put("founderName", byId.getPurchaserName());
                                data.put("acceptancerId", byId.getPurchaserId());
                                data.put("acceptancerName", byId.getPurchaserName());
                            }

                            data.put("orgId", byId.getPurchasingOrgId());
                            data.put("orgName", byId.getPurchasingOrgName());
                            data.put("outerId", byId.getReconciliationId());
                            data.put("outerNo", byId.getReconciliationNo());
                            data.put("purchaserId", byId.getPurchaserId());
                            data.put("purchaserName", byId.getPurchaserName());
                            data.put("purchasingUnitId", byId.getPurchasingOrgId());
                            data.put("purchasingUnitName", byId.getPurchasingOrgName());
                            data.put("remarks", byId.getRemarks());
                            data.put("sourceBillId", byId.getSourceBillId());
                            data.put("sourceBillNo", byId.getSourceBillNo());
                            data.put("sourceBillName", byId.getSourceBillName());
                            data.put("supplierId", byId.getSupplierId());
                            data.put("supplierName", byId.getSupplierName());
                            data.put("taxRate", byId.getTaxRate());
                            dataMap.put("data", data);
                            dataMap.put("keyId", keyId);

                            farArg.append(JSON.toJSONString(dataMap));
                            // 推送草稿验收单
                            R r = null;
                            try {
                                r = restTemplateUtils.postPCWP2(mallConfig.prodPcwp2Url02 + SAVE_ACCEPTANCE_URL, dataMap);
                            } catch (Exception e) {
                                throw new BusinessException("【远程异常】" + e.getMessage());
                            }
                            if (r.getCode() == null || r.getCode() != 200) {
                                throw new BusinessException("【远程异常】" + r.getMessage());
                            }
                            // 拿到验收id
                            String billId = (String) r.getData();
                            lambdaUpdate()
                                    .eq(MaterialReconciliation::getReconciliationId, id)
                                    .set(MaterialReconciliation::getRelevanceId, billId)
                                    .set(MaterialReconciliation::getIsNotPush, 1)
                                    .update();
                            // 成功保存日志
                            createAndSaveInterfaceLog(keyId, "materialReconciliationAuditPlan", dto, dataMap, r, 1, 1, null);
                        }
                        List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, id).list();
                        // 已审核数量
                        List<ReconciliationDtl> reconciliationDtls = convertToReconciliationDtls(list);
                        // 调用已审核数量
                        KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder().data(reconciliationDtls).keyId(keyId).orgId(byId.getPurchasingOrgId()).build();
                        PcwpRes<Void> r2 = null;
                        try {
                            r2 = pcwpService.writeBackBillQuantiy(payload);
                        } catch (Exception e) {
                            throw new BusinessException("【远程异常】" + e.getMessage());
                        }
                        if (r2.getCode() == null || r2.getCode() != 200) {
                            throw new BusinessException("【远程异常】" + r2.getMessage());
                        }
                        createAndSaveInterfaceLog(keyId, "materialReconciliationAuditPlan", dto, payload, r2, 1, 1, null);
                    }
                }
                // 未通过
                if (isOpen == 0) {
                    createRejectionAuditRecord(id, dto.getAuditResult());
                    lambdaUpdate()
                            .eq(MaterialReconciliation::getReconciliationId, id)
                            .set(MaterialReconciliation::getState, 4)
                            .update();
                }
            }
        }
    }

    private void createOutStockSettlement(MaterialReconciliation reconciliation) {
        if (null == reconciliation) {
            return;
        }
        List<MaterialReconciliationDtl> dtl = reconciliation.getDtl();
        List<OutboundSettlementVO> list = new ArrayList<>();
        List<BigDecimal> price1 = new ArrayList<>();
        List<BigDecimal> price2 = new ArrayList<>();
        if (null == dtl || dtl.isEmpty()) {
            dtl = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliation.getReconciliationId()).list();
            dtl.forEach(item -> {
                Product product = productService.getById(item.getTradeId());
                list.add(assemblyOutboundSettlementVO(product, item, reconciliation));
            });
        }
        OutboundSettlementManage manage = new OutboundSettlementManage();
        manage.setOutboundTime(new Date());
        manage.setReconciliationId(reconciliation.getReconciliationId());
        manage.setSupplierType(reconciliation.getReconciliationProductType());
        manage.setPurchasingOrgId(reconciliation.getPurchasingOrgId());
        manage.setPurchasingOrgName(reconciliation.getPurchasingOrgName());
        manage.setSupplierId(reconciliation.getSupplierId());
        manage.setSupplierName(reconciliation.getSupplierName());
        manage.setOutboundType(2);
        manage.setXsRateAmount(reconciliation.getReconciliationAmount());
        manage.setXsNoRateAmount(reconciliation.getReconciliationNoRateAmount());
        manage.setCgRateAmount(list.stream().map(OutboundSettlementVO::getCgTotalAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        manage.setCgNoRateAmount(list.stream().map(OutboundSettlementVO::getCgNoRateAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        BiddingProjectInfo projectInfo = new BiddingProjectInfo();
        manage.setProjectName(projectInfo.getProjectName());
        manage.setProjectAddress(projectInfo.getAddress());
        manage.setNum(list.stream().map(OutboundSettlementVO::getQuantity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        manage.setSupplierInvoiceStatus(reconciliation.getInvoiceState() == 2 ? 1 : 0);
        manage.setApplyInvoiceTime(reconciliation.getInvoiceState() == 1 ? new Date() : null);
        manage.setContractNo(reconciliation.getSourceBillNo());
        InvoiceRecord invoiceRecord = invoiceRecordService.getInvoiceRecord(reconciliation.getPurchasingOrgId());
        manage.setTicketReceivingUnit(invoiceRecord.getCompany());
        manage.setInvoiceNum(invoiceRecord.getInvoiceRecordId());
        manage.setTicketReceivingUnitAddress(invoiceRecord.getUserAddress());
        manage.setTicketReceivingUnitTaxNo(invoiceRecord.getDutyParagraph());
        manage.setTicketReceivingUnitPhone(invoiceRecord.getUserPhone());
        manage.setTicketReceivingUnitBank(invoiceRecord.getBank());
        manage.setTicketReceivingUnitAccount(invoiceRecord.getBankAccount());
        manage.setSettlementInfo(JSON.toJSONString(list));
        outboundSettlementService.saveAndSubmitSettlement(manage);
    }

    private static OutboundSettlementVO assemblyOutboundSettlementVO(Product product, MaterialReconciliationDtl dtl, MaterialReconciliation reconciliation) {
        OutboundSettlementVO vo = new OutboundSettlementVO();
        vo.setRelevanceName(product.getRelevanceName());
        vo.setProductName(product.getProductName());
        vo.setSpec(dtl.getSpec());
        vo.setUnit(dtl.getUnit());
        vo.setQuantity(dtl.getQuantity());
        vo.setCgPrice(product.getPurchasePrice());
        vo.setCgTotalAmount(product.getPurchasePrice().multiply(dtl.getQuantity()).setScale(2, RoundingMode.HALF_UP));
        vo.setCgNoRateAmount(TaxCalculator.calculateNotTarRateAmount(product.getPurchasePrice(), dtl.getTaxRate()));
        vo.setXsPrice(product.getSellPrice());
        vo.setXsTotalAmount(dtl.getAcceptanceAmount());
        vo.setXsNoRateAmount(dtl.getAcceptanceNoRateAmount());
        vo.setPurchasingOrgName(reconciliation.getPurchasingOrgName());
        vo.setSupplierName(reconciliation.getSupplierName());
        return vo;
    }

    @Override
    public void updateMaterialInfo(ArrayList<Map<String, Object>> dtls) {
        List<String> materialIds = dtls.stream().map(item -> item.get("materialId").toString()).collect(Collectors.toList());
        List<Product> materialInfos = productService.lambdaQuery().in(Product::getRelevanceId, materialIds).select(Product::getRelevanceId, Product::getRelevanceNo, Product::getRelevanceName).list().stream().distinct().collect(Collectors.toList());
        String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + BASICS_MATERIAL_LIST;
        for (Product materialInfo : materialInfos) {
            HashMap<Object, Object> paramsMap = new HashMap<>();
            paramsMap.put("pageIndex", 1);
            paramsMap.put("pageSize", 10);
            paramsMap.put("materialNo", materialInfo.getRelevanceNo());
            PageUtils<Map> r = null;
            try {
                r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
            } catch (Exception e) {
                throw new BusinessException("【远程异常】获取基础库物资错误：" + e.getMessage());
            }
            List<Map> list = r.getList();
            if (CollectionUtils.isEmpty(list)) {

                R r2 = restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + BASICS_MATERIAL_INFO + "?id=" + materialInfo.getRelevanceId());
                if (r2.getCode() == null || r2.getCode() != 200) {
                    throw new BusinessException("【远程异常】" + materialInfo.getRelevanceName() + "错误");
                } else {
                    Map map = (Map) r2.getData();
                    productService.lambdaUpdate().set(Product::getClassId, map.get("classId")).set(Product::getClassPath, map.get("materialClassId")).set(Product::getRelevanceName, map.get("materialName")).eq(Product::getRelevanceNo, map.get("billNo")).eq(Product::getRelevanceId, map.get("billId")).ne(Product::getClassId, map.get("classId")).update();
                    materialReconciliationDtlService.lambdaUpdate().set(MaterialReconciliationDtl::getMaterialClassId, map.get("materialClassId")).set(MaterialReconciliationDtl::getMaterialClassName, map.get("classNamePath")).eq(MaterialReconciliationDtl::getMaterialId, map.get("billId")).ne(MaterialReconciliationDtl::getMaterialClassId, map.get("materialClassId")).update();
                }
            } else {
                for (Map map : list) {

                    if (map.get("billId").equals(materialInfo.getRelevanceId())) {
                        productService.lambdaUpdate().set(Product::getClassId, map.get("classId")).set(Product::getClassPath, map.get("classIdPath")).set(Product::getRelevanceName, map.get("classNamePath")).eq(Product::getRelevanceNo, map.get("billNo")).eq(Product::getRelevanceId, map.get("billId")).ne(Product::getClassId, map.get("classId")).update();
                        materialReconciliationDtlService.lambdaUpdate().set(MaterialReconciliationDtl::getMaterialClassId, map.get("classIdPath")).set(MaterialReconciliationDtl::getMaterialClassName, map.get("classNamePath")).eq(MaterialReconciliationDtl::getMaterialId, map.get("billId")).ne(MaterialReconciliationDtl::getMaterialClassId, map.get("classIdPath")).update();
                    }
                }
            }


        }
    }

    /**
     * 修改对账
     *
     * @param d
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationUpdate(MaterialReconciliationUpdateDTO d, int isNotPush) {
        MaterialReconciliation materialReconciliation = getById(d.getReconciliationId());
        if (materialReconciliation == null) return;
        if (materialReconciliation.getState() == 2 || materialReconciliation.getState() == 3) {
            return;
        }
        Integer createType = materialReconciliation.getCreateType();
        if (createType == 3) {
            if (d.getIsSubmit() == 1) {
                materialReconciliation.setState(2);
                update(materialReconciliation);
                return;
            }
        }
        getPlanContractAmountUpdate(d);
        ArrayList<MaterialReconciliationDtl> dtl = new ArrayList<>();
        for (MaterialReconciliationDtl md : d.getDtl()) {
            if (isNotPush == 1) { //pcwp仓库id不能为空
                isWarehouseIdExist(md);
            }
            if (StringUtils.isBlank(md.getReconciliationDtlId())) {
                dtl.add(md);
                continue;
            }
            dtl.add(md);
        }
        materialReconciliationDtlService.saveOrUpdateBatch(dtl);
        BeanUtils.copyProperties(d, materialReconciliation);
        update(materialReconciliation);
        if (d.getIsSubmit() == 1) {
            materialReconciliation.setState(2);
        }
        update(materialReconciliation);

        //如果数据没有改变，不调用接口
        if (createType != 3 && isNotPush == 1) {
            List<Map<String, Object>> uniqueMaps = getUpdateQuDTOM(d, materialReconciliation);
            //如果数据没有改变，不调用接口
            List<Map<String, Object>> chaMap = getMaps(d, materialReconciliation);
            String keyId = d.getKeyId();
            List<Map<String, Object>> finalMaps = chaMap.size() != 0 ? chaMap : uniqueMaps;
            List<ReconciliationDtl> reconciliationDtls = new ArrayList<>();
            for (Map<String, Object> map : finalMaps) {
                ReconciliationDtl reconciliationDtl = ReconciliationDtl.builder().dtlId((String) map.get("dtlId")).quantity((BigDecimal) map.get("quantity")).amount((BigDecimal) map.get("amount")).build();
                reconciliationDtls.add(reconciliationDtl);
            }
            KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder().data(reconciliationDtls).keyId(keyId).orgId(materialReconciliation.getPurchasingOrgId()).build();
            PcwpRes<Void> r = null;
            try {
                r = pcwpService.writeBackBillLockQuantiy(payload);
            } catch (Exception e) {
                throw new BusinessException("【远程异常】" + e.getMessage());
            }
            if (r.getCode() == null || r.getCode() != 200) {
                throw new BusinessException("【远程异常】" + r.getMessage());
            }
            createAndSaveInterfaceLog(keyId, "materialReconciliationUpdate", d, payload, r, 1, 1, null);
        }

    }

    @NotNull
    private List<Map<String, Object>> getMaps(MaterialReconciliationUpdateDTO d, MaterialReconciliation materialReconciliation) {
        //修改对账单，记录修改的差异
        List<Map<String, Object>> chaMap = new ArrayList<>();

        //计算对账数据是否被修改

        for (MaterialReconciliationDtl md : d.getDtl()) {
            isWarehouseIdExist(md);
            if (StringUtils.isNotBlank(md.getReconciliationDtlId())) {

                MaterialReconciliationDtl byId = materialReconciliationDtlService.getById(md.getReconciliationDtlId());
                BigDecimal subtract = md.getQuantity().subtract(byId.getQuantity());
                HashMap<String, Object> map = new HashMap<>();
                if (subtract.compareTo(BigDecimal.ZERO) != 0) {
                    map.put("quantity", subtract);
                    BigDecimal amount = subtract.multiply(md.getPrice());
                    map.put("amount", amount);
                    chaMap.add(map);
                } else {
                    map.put("quantity", 0);
                    map.put("amount", 0);
                }
                map.put("dtlId", byId.getReceiptBillDtlId());
                map.put("orgId", materialReconciliation.getPurchasingOrgId());
                chaMap.add(map);
            }
        }
        return chaMap;
    }

    private List<Map<String, Object>> getUpdateQuDTOM(MaterialReconciliationUpdateDTO d, MaterialReconciliation materialReconciliation) {
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        List<String> ids = lambdaQuery().eq(MaterialReconciliation::getSourceBillId, materialReconciliation.getSourceBillId()).eq(MaterialReconciliation::getBusinessType, materialReconciliation.getBusinessType()).in(MaterialReconciliation::getState, 0, 1, 2, 4).select(MaterialReconciliation::getReconciliationId).list().stream().map(t -> t.getReconciliationId()).collect(Collectors.toList());
        for (MaterialReconciliationDtl dtl1 : d.getDtl()) {
            HashMap<String, Object> map = new HashMap<>();
            List<MaterialReconciliationDtl> dtlQuList = materialReconciliationDtlService.lambdaQuery().in(MaterialReconciliationDtl::getReconciliationId, ids).eq(MaterialReconciliationDtl::getReceiptBillDtlId, dtl1.getReceiptBillDtlId()).select(MaterialReconciliationDtl::getQuantity, MaterialReconciliationDtl::getAcceptanceAmount).list();
            // 计算数量
            BigDecimal addQu = new BigDecimal(0);
            BigDecimal addAmount = new BigDecimal(0);
            for (MaterialReconciliationDtl materialReconciliationDtl : dtlQuList) {
                addQu = addQu.add(materialReconciliationDtl.getQuantity());
                addAmount = addAmount.add(materialReconciliationDtl.getAcceptanceAmount());
            }
            map.put("quantity", addQu);
            map.put("amount", addAmount);
            map.put("dtlId", dtl1.getReceiptBillDtlId());
            map.put("orgId", materialReconciliation.getPurchasingOrgId());
            maps.add(map);
        }

        // 根据属性判断重复，并删除重复的 Map 对象
        List<Map<String, Object>> uniqueMaps = removeDuplicateByProperty(maps, "dtlId");
        return uniqueMaps;
    }

    /**
     * 计算
     *
     * @param d
     * @param type
     * @param reconciliationAmount
     * @param mathContext
     * @param materialReconciliation
     * @return
     */
    private static BigDecimal getUpdateAmount(MaterialReconciliationUpdateDTO d, Integer type, BigDecimal reconciliationAmount, MathContext mathContext, MaterialReconciliation materialReconciliation) {
        // 浮动价格
        if (type == 1) {
            for (MaterialReconciliationDtl md : d.getDtl()) {
                if (md.getQuantity() == null) {
                    throw new BusinessException("数量不能为空！");
                }
                Integer updateType = md.getUpdateType();
                // 未修改单价
                if (updateType == null) {
                    BigDecimal acceptanceAmount = md.getAcceptanceAmount();
                    // 获得平均价
                    BigDecimal price = acceptanceAmount.divide(md.getQuantity(), new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    md.setPrice(price);
                    md.setNoRatePrice(TaxCalculator.calculateNotTarRateAmount(price, materialReconciliation.getTaxRate()));
                    md.setFreightPrice(price);
                    md.setFixationPrice(new BigDecimal(0));
                    reconciliationAmount = reconciliationAmount.add(acceptanceAmount);
                }
                // 修改了单价
                if (updateType != null && updateType == 1) {
                    BigDecimal freightPrice = md.getFreightPrice();
                    BigDecimal fixationPrice = md.getFixationPrice();
                    if (freightPrice == null) {
                        throw new BusinessException("到货网价不能为空！");
                    }
                    if (fixationPrice == null) {
                        fixationPrice = new BigDecimal(0);
                    }
                    BigDecimal addPrice = freightPrice.add(fixationPrice);
                    BigDecimal amount = md.getQuantity().multiply(addPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                    md.setAcceptanceAmount(amount);
                    BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(md.getPrice(), materialReconciliation.getTaxRate());
                    md.setAcceptanceNoRateAmount(noRatePrice.multiply(md.getQuantity()));
                    reconciliationAmount = reconciliationAmount.add(amount);
                }
            }
        }

        // 固定价格
        if (type == 2) {
            for (MaterialReconciliationDtl md : d.getDtl()) {
                Integer updateType = md.getUpdateType();
                // 未修改单价
                if (updateType == null) {
                    BigDecimal acceptanceAmount = md.getAcceptanceAmount();
                    // 获得平均价
                    BigDecimal price = acceptanceAmount.divide(md.getQuantity(), new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP);
                    md.setPrice(price);
                    md.setNoRatePrice(TaxCalculator.calculateNotTarRateAmount(price, materialReconciliation.getTaxRate()));
                    md.setFreightPrice(new BigDecimal(0));
                    md.setFixationPrice(new BigDecimal(0));
                    reconciliationAmount = reconciliationAmount.add(acceptanceAmount);
                }
                // 修改了单价
                if (updateType != null && updateType == 1) {
                    BigDecimal amount = md.getQuantity().multiply(md.getPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    md.setFreightPrice(new BigDecimal(0));
                    md.setFixationPrice(new BigDecimal(0));
                    md.setAcceptanceAmount(amount);
                    BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(md.getPrice(), materialReconciliation.getTaxRate());
                    md.setAcceptanceNoRateAmount(noRatePrice.multiply(md.getQuantity()));
                    reconciliationAmount = reconciliationAmount.add(amount);
                }
            }
        }
        return reconciliationAmount;
    }

    /**
     * 删除对账
     *
     * @param reconciliationId
     * @param keyId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationDelete(String reconciliationId, String keyId, int isNotPush) {
        MaterialReconciliation materialReconciliation = getById(reconciliationId);
        Integer state = materialReconciliation.getState();
        Integer createType = materialReconciliation.getCreateType();
        String relevanceId = materialReconciliation.getRelevanceId();
        // 如果是pcwp新增的删除需要调用清除id
        if (isNotPush == 1) {//对账单是否推送PCWP
            if (createType == 3) {//pcwp新增对账单
                PcwpRes<Boolean> r = null;
                try {
                    if (materialReconciliation.getBusinessType() == 2 || materialReconciliation.getBusinessType() == 6) {
                        //零星采购或者大宗临购
                        r = pcwpService.clearRelationId(relevanceId, materialReconciliation.getPurchasingOrgId());
                    }
                } catch (Exception e) {
                    throw new BusinessException("【远程异常】" + e.getMessage());
                }
                if (r.getCode() == null || r.getCode() != 200) {
                    throw new BusinessException("【远程异常】" + r.getMessage());
                }
            } else if (createType == 1 || createType == 2 || createType == 3) {
                ArrayList<Map<String, Object>> maps = new ArrayList<>();
                List<String> ids = lambdaQuery().eq(MaterialReconciliation::getSourceBillId, materialReconciliation.getSourceBillId()).eq(MaterialReconciliation::getBusinessType, materialReconciliation.getBusinessType()).in(MaterialReconciliation::getState, 0, 1, 2, 4).select(MaterialReconciliation::getReconciliationId).list().stream().map(t -> t.getReconciliationId()).collect(Collectors.toList());
                List<MaterialReconciliationDtl> dtls = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).list();
                for (MaterialReconciliationDtl dtl1 : dtls) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("quantity", dtl1.getQuantity().negate());
                    map.put("amount", dtl1.getAcceptanceAmount().negate());
                    map.put("dtlId", dtl1.getReceiptBillDtlId());
                    map.put("orgId", materialReconciliation.getPurchasingOrgId());
                    maps.add(map);
                }
                if (materialReconciliation.getBusinessType() == 2 || materialReconciliation.getBusinessType() == 6) {
                    //零星采购或者大宗临购
                    // 根据属性判断重复，并删除重复的 Map 对象
                    List<Map<String, Object>> uniqueMaps = removeDuplicateByProperty(maps, "dtlId");
                    // 转换为 ReconciliationDtl 列表
                    List<ReconciliationDtl> reconciliationDtls = new ArrayList<>();
                    for (Map<String, Object> map : uniqueMaps) {
                        ReconciliationDtl reconciliationDtl = ReconciliationDtl.builder().dtlId((String) map.get("dtlId")).quantity((BigDecimal) map.get("quantity")).amount((BigDecimal) map.get("amount")).build();
                        reconciliationDtls.add(reconciliationDtl);
                    }
                    KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder().data(reconciliationDtls).keyId(keyId).orgId(materialReconciliation.getPurchasingOrgId()).build();
                    PcwpRes<Void> r = null;
                    try {
                        r = pcwpService.writeBackBillLockQuantiy(payload);
                    } catch (Exception e) {
                        throw new BusinessException("【远程异常】" + e.getMessage());
                    }
                    if (r.getCode() == null || r.getCode() != 200) {
                        throw new BusinessException("【远程异常】" + r.getMessage());
                    }
                } else if (materialReconciliation.getBusinessType() == 7) {//周转材料
                    // 根据属性判断重复，并删除重复的 Map 对象
                    List<Map<String, Object>> uniqueMaps = removeDuplicateByProperty(maps, "dtlId");
                    // 转换为 UpdatePlanDtl 列表
                    List<UpdatePlanDtl> updatePlanDtls = new ArrayList<>();
                    for (Map<String, Object> map : uniqueMaps) {
                        UpdatePlanDtl updatePlanDtl = UpdatePlanDtl.builder()
                                .dtlId((String) map.get("dtlId"))
                                .billId(materialReconciliation.getSourceBillId())
                                .number((BigDecimal) map.get("quantity"))
                                .amount((BigDecimal) map.get("amount"))
                                .build();
                        updatePlanDtls.add(updatePlanDtl);
                    }
                    KeyedPayload<List<UpdatePlanDtl>> payload = KeyedPayload.<List<UpdatePlanDtl>>builder()
                            .data(updatePlanDtls)
                            .keyId(keyId)
                            .orgId(materialReconciliation.getPurchasingOrgId())
                            .build();
                    // 记录日志、处理异常
                    PcwpRes<Void> r = null;
                    try {
                        r = pcwpService.updateRevolPlanDtl(payload);
                    } catch (Exception e) {
                        throw new BusinessException("【远程异常】" + e.getMessage());
                    }
                    if (r.getCode() == null || r.getCode() != 200) {
                        throw new BusinessException("【远程异常】" + r.getMessage());
                    }
                    // 成功保存日志
                    createAndSaveInterfaceLog(keyId, "materialReconciliationDelete", reconciliationId, payload, r, 1, 1, null);
                }
                createAndSaveInterfaceLog(keyId, "materialReconciliationDelete", reconciliationId, null, null, 1, 1, null);
            }
        }
        // 删除
        delete(reconciliationId);
        // 删除
        materialReconciliationDtlService.lambdaUpdate().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).remove();
    }

    /**
     * 删除草稿清除对账单关联id
     *
     * @param reconciliationId
     */
    @Override
    public void clearRelevanceId(String reconciliationId) {
        if (StringUtils.isNotBlank(reconciliationId)) {
            lambdaUpdate().eq(MaterialReconciliation::getReconciliationId, reconciliationId).set(MaterialReconciliation::getRelevanceId, null).set(MaterialReconciliation::getRelevanceSn, null).update();
        }

    }

    /**
     * 确认单据
     *
     * @param reconciliationId
     */
    @Override
    public void materialReconciliationAffirm(String reconciliationId) {
        if (StringUtils.isNotBlank(reconciliationId)) {
            lambdaUpdate().eq(MaterialReconciliation::getReconciliationId, reconciliationId).eq(MaterialReconciliation::getState, 3).set(MaterialReconciliation::getPurchaseIsAffirm, 1).set(MaterialReconciliation::getPurchaseAffirmTime, new Date()).update();
        }
    }

    /**
     * 判断是否可作废
     *
     * @param reconciliationId
     * @return
     */
    @Override
    public Boolean isCancellationByRelevanceId(String reconciliationId) {
        MaterialReconciliation one = lambdaQuery().eq(MaterialReconciliation::getReconciliationId, reconciliationId).select(MaterialReconciliation::getState).one();
        if (one == null) {
            return true;
        } else if (one.getState() != 7) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 作废单据
     *
     * @param reconciliationId
     * @param result
     * @param keyId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationCancellation(String reconciliationId, String result, String keyId, int isNotPush) {
//        MaterialReconciliation byId = lambdaQuery().eq(MaterialReconciliation::getReconciliationId, reconciliationId)
//                .eq(MaterialReconciliation::getState, 3).one();
        MaterialReconciliation byId = lambdaQuery().eq(MaterialReconciliation::getReconciliationId, reconciliationId).one();
        if (byId == null) return;
        if (byId.getSettleAmount().compareTo(BigDecimal.ZERO) > 0) {
            throw new BusinessException("已结算不能作废操作！");
        }
        Boolean data = null;
        Integer createType = byId.getCreateType();
        // 商城新增需要校验pcwp是否作废
        if (isNotPush == 1) {
            if (createType == 1 || createType == 2) {
                if (StringUtils.isBlank(byId.getRelevanceId())) {
                    data = true;
                } else {
                    PcwpRes<Boolean> r11 = null;
                    try {
                        r11 = pcwpService.isCanOperaBill(byId.getPurchasingOrgId(), DateUtil.getymd(LocalDate.now()));
                    } catch (Exception e) {
                        throw new BusinessException("【远程异常】" + e.getMessage());
                    }
                    if (r11.getCode() == null || r11.getCode() != 200) {
                        throw new BusinessException("【远程异常】" + r11.getMessage());
                    } else {
                        data = (Boolean) r11.getData();
                    }
                }
            } else {
                // pcwp推送的直接作废
                data = true;
            }
            if (mallConfig.isPlatformFee == 1) {
                // 保存交易明细
                platformDealFeeDtlService.addNegativeFeeCancellation(byId);
            }
            if (data) {
                HashMap<Object, Object> writeBackBillQuMap = new HashMap<>();
                R<Map> r2 = null;
                if (createType == 1 || createType == 2) {//要推送
                    // 调用接口
                    List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).list();
                    // 已审核数量
                    ArrayList<Map> quMaps = new ArrayList<>();
                    Map<String, List<MaterialReconciliationDtl>> collect = list.stream().collect(Collectors.groupingBy(MaterialReconciliationDtl::getReceiptBillDtlId));
                    for (Map.Entry<String, List<MaterialReconciliationDtl>> entry : collect.entrySet()) {
                        String dtlId = entry.getKey();
                        List<MaterialReconciliationDtl> dataList = entry.getValue();
                        HashMap<Object, Object> map = new HashMap<>();
                        BigDecimal addQu = new BigDecimal(0);
                        BigDecimal addAmount = new BigDecimal(0);
                        for (MaterialReconciliationDtl materialReconciliationDtl : dataList) {
                            addQu = addQu.add(materialReconciliationDtl.getQuantity());
                            addAmount = addAmount.add(materialReconciliationDtl.getAcceptanceAmount());
                        }
                        addAmount = addAmount.negate().setScale(2, BigDecimal.ROUND_HALF_UP);
                        addQu = addQu.negate().setScale(4, BigDecimal.ROUND_HALF_UP);
                        map.put("dtlId", dtlId);
                        map.put("amount", addAmount);
                        map.put("quantity", addQu);
                        map.put("orgId", byId.getPurchasingOrgId());
                        quMaps.add(map);
                    }
                    // 调用已审核数量
                    writeBackBillQuMap.put("data", quMaps);
                    writeBackBillQuMap.put("keyId", keyId);
                    writeBackBillQuMap.put("orgId", byId.getPurchasingOrgId());
                    try {
                        r2 = restTemplateUtils.postPCWP2(mallConfig.prodPcwp2Url02 + WRITE_BACK_BILL_QUANTIY_URL, writeBackBillQuMap);
                    } catch (Exception e) {
                        throw new BusinessException("【远程异常】" + e.getMessage());
                    }
                    if (r2.getCode() == null || r2.getCode() != 200) {
                        throw new BusinessException("【远程异常】" + r2.getMessage());
                    }
                }
                if (createType == 3) {
                    String relevanceId = byId.getRelevanceId();
                    if (StringUtils.isNotBlank(relevanceId)) {
                        R<Map> r = null;
                        try {
                            r = restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + CLEAR_RELATION_ID_URL + relevanceId + "&orgId=" + byId.getPurchasingOrgId());
                        } catch (Exception e) {
                            throw new BusinessException("【远程异常】" + e.getMessage());
                        }
                        if (r.getCode() == null || r.getCode() != 200) {
                            throw new BusinessException("【远程异常】" + r.getMessage());
                        }
                    }
                }
                // 成功保存日志
                createAndSaveInterfaceLog(keyId, "materialReconciliationCancellation", reconciliationId, writeBackBillQuMap, r2, 1, 1, null);
                MaterialReconciliation materialReconciliation = new MaterialReconciliation();
                materialReconciliation.setReconciliationId(reconciliationId);
                materialReconciliation.setState(7);
                materialReconciliation.setNullifyReason(result);
                UserLogin user = ThreadLocalUtil.getCurrentUser();
                materialReconciliation.setNullifyCreatorLocalId(user.getUserId());
                materialReconciliation.setNullifyCreatorId(user.getFarUserId());
                materialReconciliation.setNullifyCreator(user.getUserName());
                materialReconciliation.setNullifyCreated(new Date());
                update(materialReconciliation);
            } else {
                throw new BusinessException("pcwp存在有效单据不可作废！");
            }
        } else {
            if (mallConfig.isPlatformFee == 1) { //作废取消交易明细
                platformDealFeeDtlService.addNegativeFeeCancellation(byId);
            }
            //更新对账单信息
            MaterialReconciliation materialReconciliation = new MaterialReconciliation();
            materialReconciliation.setReconciliationId(reconciliationId);
            materialReconciliation.setState(7);
            materialReconciliation.setNullifyReason(result);
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            materialReconciliation.setNullifyCreatorLocalId(user.getUserId());
            materialReconciliation.setNullifyCreatorId(user.getFarUserId());
            materialReconciliation.setNullifyCreator(user.getUserName());
            materialReconciliation.setNullifyCreated(new Date());
            update(materialReconciliation);
        }
    }

    /**
     * 导出账单（采购员）
     *
     * @param reconciliationId
     * @param response
     */
    @Override
    public void outputExcel(String reconciliationId, HttpServletResponse response) {
        MaterialReconciliation materialReconciliation = getById(reconciliationId);
        List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).orderByDesc(MaterialReconciliationDtl::getOrderSn, MaterialReconciliationDtl::getQuantity, MaterialReconciliationDtl::getMaterialName, MaterialReconciliationDtl::getAcceptanceAmount).list();
        materialReconciliation.setDtl(list);
        // 处理date
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        String startTimeStr = dateFormat.format(materialReconciliation.getStartTime());
        String endTimeStr = dateFormat.format(materialReconciliation.getEndTime());

        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(materialReconciliation);

        BigDecimal totalQu = new BigDecimal(0);
        BigDecimal totalAmount = new BigDecimal(0);
        BigDecimal noRateTotalAmount = new BigDecimal(0);
        for (MaterialReconciliationDtl materialReconciliationDtl : list) {
            totalQu = totalQu.add(materialReconciliationDtl.getQuantity());
            totalAmount = totalAmount.add(materialReconciliationDtl.getAcceptanceAmount());
            noRateTotalAmount = noRateTotalAmount.add(materialReconciliationDtl.getAcceptanceNoRateAmount());
        }
        // 如果需要保留2位小数
        BigDecimal taxAmount = totalAmount.subtract(noRateTotalAmount).setScale(2, RoundingMode.HALF_UP);
        stringObjectMap.put("supplierName", materialReconciliation.getSupplierName());//收货单位
        stringObjectMap.put("warehouseName", materialReconciliation.getPurchasingOrgName());//供货单位
        stringObjectMap.put("auditDate", startTimeStr + '-' + endTimeStr);
        stringObjectMap.put("noRateTotalAmount", noRateTotalAmount);
        stringObjectMap.put("rateTotalAmount", totalAmount);
        stringObjectMap.put("taxAmount", taxAmount);
        stringObjectMap.put("dtlExcel", list);
//        stringObjectMap.put("totalQuantity", totalQu);
//        stringObjectMap.put("totalAmount", totalAmount);
        //含税反推不含税
//        BigDecimal noRateTotalAmount1 = TaxCalculator.calculateNotTarRateAmount(totalAmount, materialReconciliation.getTaxRate());
//        stringObjectMap.put("noRateTotalAmount", noRateTotalAmount);
        String src = mallConfig.templateFormUrl;

        if (materialReconciliation.getType() == 1) {
            // 浮动价格
            try {
                ExcelForWebUtil.exportExcel(response, stringObjectMap, "固定价对账单模板.xlsx", src, materialReconciliation.getPurchaserName() + "浮动价格对账单.xlsx");
//                ExcelForWebUtil.exportExcel(response, stringObjectMap, "浮动价格对账单模板.xlsx", src, materialReconciliation.getPurchaserName() + "浮动价格对账单.xlsx");
            } catch (Exception e) {
                log.error("导出失败信息：" + e.getMessage());
                throw new BusinessException("导出失败！");
            }
        }
        if (materialReconciliation.getType() == 2) {
            // 固定价格
            try {
                ExcelForWebUtil.exportExcel(response, stringObjectMap, "固定价对账单模板.xlsx", src, materialReconciliation.getPurchaserName() + "固定价格对账单.xlsx");
//                ExcelForWebUtil.exportExcel(response, stringObjectMap, "固定价格对账单模板.xlsx", src, materialReconciliation.getPurchaserName() + "固定价格对账单.xlsx");
            } catch (Exception e) {
                log.error("导出失败信息：" + e.getMessage());
                throw new BusinessException("导出失败！");
            }
        }

    }

    /**
     * 查询对账单列表（供应商）
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils supplierListByEntity(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> q) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = user.getEnterpriseId();
        // 查询机构信息
        EnterpriseInfo e = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, enterpriseId).select(EnterpriseInfo::getInteriorId, EnterpriseInfo::getShortCode, EnterpriseInfo::getSocialCreditCode).one();
        Integer isInterior = user.getIsInterior();

        if (isInterior == 1) {
            // 内部供应商
            q.eq(MaterialReconciliation::getOrgShort, e.getShortCode());
        }
        if (isInterior == 0) {
            // 外部供应商
            q.eq(MaterialReconciliation::getCreditCode, e.getSocialCreditCode());
        }
        q.and((t) -> {
            t.in(MaterialReconciliation::getCreateType, 1, 2).or(t2 -> {
                t2.in(MaterialReconciliation::getCreateType, 1, 3).eq(MaterialReconciliation::getState, 3);
            });
        });

        String keywords = (String) jsonObject.get("keywords");
        String title = (String) jsonObject.get("title");
        String supplierName = (String) jsonObject.get("supplierName");
        String purchasingOrgName = (String) jsonObject.get("purchasingOrgName");
        String reconciliationNo = (String) jsonObject.get("reconciliationNo");
        String sourceBillNo = (String) jsonObject.get("sourceBillNo");


        Integer type = (Integer) jsonObject.get("type");
        Integer businessType = (Integer) jsonObject.get("businessType");
        Integer createType = (Integer) jsonObject.get("createType");
        Integer state = (Integer) jsonObject.get("state");
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        Integer reconciliationProductType = (Integer) jsonObject.get("reconciliationProductType");

        q.eq(type != null, MaterialReconciliation::getType, type);
        q.eq(createType != null, MaterialReconciliation::getCreateType, createType);
        q.eq(state != null, MaterialReconciliation::getState, state);
        if (reconciliationProductType != null && reconciliationProductType == 0) { //零星采购
            q.in(MaterialReconciliation::getReconciliationProductType, 0, 10);
        }
        if (reconciliationProductType != null && reconciliationProductType == 1) { //大宗临购
            q.in(MaterialReconciliation::getReconciliationProductType, 1, 13);
        }
        if (reconciliationProductType != null && reconciliationProductType == 2) { //周转材料
            q.eq(MaterialReconciliation::getReconciliationProductType, reconciliationProductType);
        }
//        if (businessType == null) {
//            q.in(MaterialReconciliation::getBusinessType, 1, 2);
//        } else {
//            q.eq(MaterialReconciliation::getBusinessType, businessType);
//        }

        if (StringUtils.isNotBlank(title)) q.eq(MaterialReconciliation::getTitle, title.trim());
        if (StringUtils.isNotBlank(supplierName)) q.eq(MaterialReconciliation::getSupplierName, supplierName.trim());
        if (StringUtils.isNotBlank(purchasingOrgName))
            q.eq(MaterialReconciliation::getPurchasingOrgName, purchasingOrgName.trim());
        if (StringUtils.isNotBlank(reconciliationNo))
            q.eq(MaterialReconciliation::getReconciliationNo, reconciliationNo.trim());
        if (StringUtils.isNotBlank(sourceBillNo)) q.eq(MaterialReconciliation::getSourceBillNo, sourceBillNo.trim());

        if (orderBy != null) {
            if (orderBy == 0) {
                q.orderByDesc(MaterialReconciliation::getGmtCreate);
            }
            if (orderBy == 1) {
                q.orderByDesc(MaterialReconciliation::getStartTime);
            }
            if (orderBy == 2) {
                q.orderByDesc(MaterialReconciliation::getEndTime);
            }
        } else {
            q.orderByDesc(MaterialReconciliation::getGmtCreate);

        }
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(MaterialReconciliation::getTitle, keywords.trim()).or().like(MaterialReconciliation::getSupplierName, keywords.trim()).or().like(MaterialReconciliation::getReconciliationNo, keywords.trim()).or().like(MaterialReconciliation::getPurchasingOrgName, keywords.trim());

            });
        }
        IPage<MaterialReconciliation> page = this.page(new Query<MaterialReconciliation>().getPage(jsonObject), q);
        return new PageUtils(page);
    }

    /**
     * 供应商新增
     *
     * @param d
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String materialReconciliationSupplierCreate(MaterialReconciliation d, StringBuilder result, int isNotPush) {

        if (d.getIsSubmit() == 1) {
            d.setState(1);
        }
        getPlanContractAmount(d);
        d.setCreateType(2);
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        d.setAcceptanceId(user.getFarUserId());
        d.setAcceptanceName(user.getUserName());
        d.setOrgName(user.getEnterpriseName());
        d.setOrgId(user.getOrgId());
        String materialPlanNo = createMaterialPlanNo(d.getPurchasingOrgId());
        d.setReconciliationNo(materialPlanNo);
        d.setEnterpriseId(user.getEnterpriseId());
        if (d.getIsSubmit() == 0) {
            d.setState(0);
        } else {
            d.setState(1);
        }
        if (mallConfig.isPlatformFee == 1) {
            platformDealFeeDtlService.checkDealAmount(d, 0);
        }
        boolean save = save(d);
        if (save) {
            List<MaterialReconciliationDtl> dtl = d.getDtl();
            for (MaterialReconciliationDtl md : dtl) {
                if (md.getWarehouseId() == null && isNotPush == 1) {
                    throw new BusinessException("仓库不能为空");
                }
                md.setReconciliationId(d.getReconciliationId());
                if (isNotPush == 1) { //注释setProductNameAndProductName
                    OrderItem one = orderItemService.lambdaQuery().eq(OrderItem::getOrderSn, md.getOrderSn()).eq(md.getOrderDtlId() != null, OrderItem::getOrderItemId, md.getOrderDtlId()).eq(OrderItem::getProductId, md.getTradeId()).one();
                    if (one != null) {
                        md.setProductName(one.getProductName());
                        md.setMaterialName(one.getRelevanceName());
                    }
                } else {
                    md.setReconciliationNo(materialPlanNo);
                    md.setReconciliationDtlId(IdWorker.getIdStr());
                }
                materialReconciliationDtlService.save(md);
            }

            if (isNotPush != 1) { //未推送PCWP,本地计划
                return d.getReconciliationNo();
            } else { //已推送PCWP,需要推送
                String keyId = d.getKeyId();
                PcwpRes<Void> res;
                Object logData;
                String logOperation;
                if (d.getReconciliationProductType() == 2) {//周转材料
                    List<UpdatePlanDtl> updatePlanDtls = getUpdateQuCountMForRevol(d);
                    KeyedPayload<List<UpdatePlanDtl>> payload = KeyedPayload.<List<UpdatePlanDtl>>builder().data(updatePlanDtls).keyId(keyId).orgId(d.getPurchasingOrgId()).build();
                    res = pcwpService.updateRevolPlanDtl(payload);
                    logData = payload;
                    logOperation = "create";
                } else {//零星采购或者大宗临购
                    List<ReconciliationDtl> reconciliationDtls = getReconciliationDtls(d);
                    KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder().data(reconciliationDtls).keyId(keyId).orgId(d.getPurchasingOrgId()).build();
                    res = pcwpService.writeBackBillLockQuantiy(payload);
                    logData = payload;
                    logOperation = "materialReconciliationSupplierCreate";
                }
                if (res.getCode() == null || res.getCode() != 200) {
                    throw new BusinessException("【远程异常】" + res.getMessage());
                }
                createAndSaveInterfaceLog(keyId, logOperation, d, logData, res, 1, 1, null);
            }
        }
        return d.getReconciliationNo();
    }

    private void setProductNameAndProductName(MaterialReconciliationDtl md) {
        if (md.getTradeId() != null) {
            OrderItem one = orderItemService.lambdaQuery().eq(OrderItem::getOrderSn, md.getOrderSn()).eq(md.getOrderDtlId() != null, OrderItem::getOrderItemId, md.getOrderDtlId()).eq(OrderItem::getProductId, md.getTradeId()).one();
            if (one != null) {
                if (one.getProductType() != 12) {
                    md.setProductName(one.getProductName());
                    md.setMaterialName(one.getRelevanceName());
                } else {
                    md.setProductName(one.getProductName());
                    md.setMaterialName(one.getRelevanceName());
                }
            }
        }
    }

    /**
     * 批量提交
     *
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationSubmit(List<String> ids) {
        List<MaterialReconciliation> list = lambdaQuery().in(MaterialReconciliation::getReconciliationId, ids).select(MaterialReconciliation::getReconciliationId, MaterialReconciliation::getState).list();
        if (!CollectionUtils.isEmpty(list)) {
            for (MaterialReconciliation materialReconciliation : list) {
                Integer state = materialReconciliation.getState();
                if (state == 0) {
                    materialReconciliation.setState(1);
                    update(materialReconciliation);
                }
            }
        }
    }

    /**
     * 修改对账供应商
     *
     * @param d
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationSupplierUpdate(MaterialReconciliationUpdateDTO d, StringBuilder fag, int isNotPush) {
        MaterialReconciliation materialReconciliation = getById(d.getReconciliationId());
        if (materialReconciliation == null) return;
        if (materialReconciliation.getState() == 2 || materialReconciliation.getState() == 3) {
            return;
        }
        getPlanContractAmountUpdate(d);
        BeanUtils.copyProperties(d, materialReconciliation);
        if (d.getIsSubmit() == 1) {
            materialReconciliation.setState(2);
        }
        update(materialReconciliation);
        ArrayList<MaterialReconciliationDtl> dtl = new ArrayList<>();
        for (MaterialReconciliationDtl md : d.getDtl()) {
            if (isNotPush == 1) {
                isWarehouseIdExist(md);
            }
            if (StringUtils.isBlank(md.getReconciliationDtlId())) {
                materialReconciliationDtlService.save(md);
                dtl.add(md);
                continue;
            }
            materialReconciliationDtlService.update(md);
            dtl.add(md);
        }
        if (isNotPush == 1) {
            List<Map<String, Object>> chaMap = getMaps(d, materialReconciliation);
            // 调用远程进行新增
            String keyId = d.getKeyId();
            List<Map<String, Object>> uniqueMaps = getUpdateQuDTOM(d, materialReconciliation);
            List<Map<String, Object>> finalMaps = chaMap.size() != 0 ? chaMap : uniqueMaps;
            // 转换为 ReconciliationDtl 列表
            List<ReconciliationDtl> reconciliationDtls = new ArrayList<>();
            for (Map<String, Object> map : finalMaps) {
                ReconciliationDtl reconciliationDtl = ReconciliationDtl.builder().dtlId((String) map.get("dtlId")).quantity((BigDecimal) map.get("quantity")).amount((BigDecimal) map.get("amount")).build();
                reconciliationDtls.add(reconciliationDtl);
            }
            KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder().data(reconciliationDtls).keyId(keyId).orgId(materialReconciliation.getPurchasingOrgId()).build();
            fag.append(payload);
            PcwpRes<Void> r = null;
            try {
                r = pcwpService.writeBackBillLockQuantiy(payload);
            } catch (Exception e) {
                throw new BusinessException("【远程异常】" + e.getMessage());
            }
            if (r.getCode() == null || r.getCode() != 200) {
                throw new BusinessException("【远程异常】" + r.getMessage());
            }
            createAndSaveInterfaceLog(keyId, "materialReconciliationSupplierUpdate", d, payload, r, 1, 1, null);
        }
    }

    private void isWarehouseIdExist(MaterialReconciliationDtl md) {
        String warehouseId = md.getWarehouseId();
        if (warehouseId == null) {
            throw new BusinessException("仓库id不能为空！");
        }
    }


    /**
     * 供应商确认单据
     *
     * @param reconciliationId
     */
    @Override
    public void materialReconciliationSupplierAffirm(String reconciliationId) {
        if (StringUtils.isNotBlank(reconciliationId)) {
            lambdaUpdate().eq(MaterialReconciliation::getReconciliationId, reconciliationId).eq(MaterialReconciliation::getState, 3).set(MaterialReconciliation::getSupplierIsAffirm, 1).set(MaterialReconciliation::getSupplierAffirmTime, new Date()).update();
        }
    }

    /**
     * 对账单增加已结算金额
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSettlementAmount(AddSettlementAmountParentDTO dto) {
        Integer flag = dto.getFlag();
        String keyId = dto.getKeyId();
        // 记录修改前的
        ArrayList<AddSettlementAmountUpdateDTO> oldList = new ArrayList<>();
        ArrayList<AddSettlementAmountUpdateDTO> newList = new ArrayList<>();
        if (flag == 1) {
            Map<String, List<AddSettlementAmountDTO>> collect = dto.getDtos().stream().collect(Collectors.groupingBy(AddSettlementAmountDTO::getReconciliationId));
            for (Map.Entry<String, List<AddSettlementAmountDTO>> entry : collect.entrySet()) {
                String reconciliationId = entry.getKey();
                List<AddSettlementAmountDTO> dtos = entry.getValue();
                MaterialReconciliation m = getById(reconciliationId);
                if (m == null) {
                    throw new BusinessException("对账单不存在！");
                }
                AddSettlementAmountUpdateDTO updateD = new AddSettlementAmountUpdateDTO();
                AddSettlementAmountUpdateDTO updateOldD = new AddSettlementAmountUpdateDTO();
                // 修改DTO
                ArrayList<MaterialReconciliationDtl> updateS = new ArrayList<>();
                // 旧修改DTO
                ArrayList<MaterialReconciliationDtl> updateOlds = new ArrayList<>();

                // 旧对账单
                MaterialReconciliation oldM = new MaterialReconciliation();
                oldM.setReconciliationId(reconciliationId);
                oldM.setSettleAmount(m.getSettleAmount());
                updateOldD.setMaterialReconciliation(oldM);

                for (AddSettlementAmountDTO d : dtos) {
                    BigDecimal amount = d.getAmount();
                    if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new BusinessException("结算金额输入错误！");
                    }
                    LambdaQueryChainWrapper<MaterialReconciliationDtl> qqq = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).and(qw -> qw.eq(MaterialReconciliationDtl::getMaterialName, d.getMaterialName()).or().eq(MaterialReconciliationDtl::getProductName, d.getMaterialName()));
                    if (d.getMaterialId() == null) {
                        qqq.isNull(MaterialReconciliationDtl::getMaterialId);
                    } else {
                        qqq.eq(MaterialReconciliationDtl::getMaterialId, d.getMaterialId());
                    }
                    if (d.getSpec() == null) {
                        qqq.isNull(MaterialReconciliationDtl::getSpec);
                    } else {
                        qqq.eq(MaterialReconciliationDtl::getSpec, d.getSpec());
                    }
                    if (d.getUnit() == null) {
                        qqq.isNull(MaterialReconciliationDtl::getUnit);
                    } else {
                        qqq.eq(MaterialReconciliationDtl::getUnit, d.getUnit());
                    }
                    if (d.getTexture() == null) {
                        qqq.isNull(MaterialReconciliationDtl::getTexture);
                    } else {
                        qqq.eq(MaterialReconciliationDtl::getTexture, d.getTexture());
                    }
                    List<MaterialReconciliationDtl> dtls = qqq.list();
                    if (CollectionUtils.isEmpty(dtls)) {
                        throw new BusinessException("对账单明细不存在！物资id：" + d.getMaterialId() + "，物资名称：" + d.getMaterialName());
                    }
                    // 处理某一个明细金额分发
                    for (int i = 0; i < dtls.size(); i++) {
                        MaterialReconciliationDtl md = dtls.get(i);

                        // 旧明细
                        MaterialReconciliationDtl oldDtl = new MaterialReconciliationDtl();
                        oldDtl.setReconciliationDtlId(md.getReconciliationDtlId());
                        oldDtl.setSettledAmount(md.getSettledAmount());
                        updateOlds.add(oldDtl);


                        BigDecimal settledAmount = md.getSettledAmount();
                        BigDecimal acceptanceAmount = md.getAcceptanceAmount();
                        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                            break;
                        }
                        // 最后一个
                        if (i == dtls.size() - 1) {
                            // 分发完毕了
                            if (acceptanceAmount.compareTo(settledAmount) <= 0) {
                                md.setSettledAmount(settledAmount.add(amount));
                                amount = amount.subtract(amount);
                                continue;
                            } else {
                                // 未结算完
                                BigDecimal sub = acceptanceAmount.subtract(settledAmount);
                                if (sub.compareTo(amount) > 0) {
                                    md.setSettledAmount(settledAmount.add(amount));
                                    amount = amount.subtract(amount);
                                } else if (sub.compareTo(amount) < 0) {
                                    // 小于amount，直接追加全部，因为最后一次
                                    md.setSettledAmount(settledAmount.add(amount));
                                    amount = amount.subtract(amount);
                                } else {
                                    // 相等
                                    md.setSettledAmount(settledAmount.add(sub));
                                    amount = amount.subtract(amount);
                                }
                                continue;
                            }
                        }
                        // 如果相等表示已分发完毕
                        if (acceptanceAmount.compareTo(settledAmount) == 0) {
                            continue;
                        }

                        // 需要填充结算金额
                        if (acceptanceAmount.compareTo(settledAmount) > 0) {
                            // 拿到需要补充的金额
                            BigDecimal sub = acceptanceAmount.subtract(settledAmount);
                            if (sub.compareTo(amount) > 0) {
                                md.setSettledAmount(settledAmount.add(amount));
                                amount = amount.subtract(amount);
                            } else if (sub.compareTo(amount) < 0) {
                                md.setSettledAmount(settledAmount.add(sub));
                                amount = amount.subtract(sub);
                            } else {
                                // 相等
                                md.setSettledAmount(settledAmount.add(sub));
                                amount = amount.subtract(amount);
                            }
                        }
                    }

                    //旧
                    updateOldD.setMaterialReconciliationDtls(updateOlds);


                    // 明细
                    for (MaterialReconciliationDtl md : dtls) {
                        MaterialReconciliationDtl materialReconciliationDtl = new MaterialReconciliationDtl();
                        materialReconciliationDtl.setReconciliationDtlId(md.getReconciliationDtlId());
                        materialReconciliationDtl.setSettledAmount(md.getSettledAmount());
                        updateS.add(materialReconciliationDtl);
                    }

                    updateD.setMaterialReconciliationDtls(updateS);
                    materialReconciliationDtlService.updateBatchById(updateD.getMaterialReconciliationDtls());

                }


                // 统计总金额
                BigDecimal totalAmount = new BigDecimal(0);
                List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).list();
                for (MaterialReconciliationDtl dtl : list) {
                    totalAmount = totalAmount.add(dtl.getSettledAmount());
                }
                MaterialReconciliation updateM = new MaterialReconciliation();
                updateM.setReconciliationId(reconciliationId);
                updateM.setSettleAmount(totalAmount);

                // 修改主表
                update(updateM);

                // 保存要修改的数据
                updateD.setMaterialReconciliation(updateM);
                updateD.setMaterialReconciliationDtls(updateS);

                // 记录旧的
                oldList.add(updateOldD);
                // 记录新的
                newList.add(updateD);
            }
            // 成功保存日志
            createAndSaveInterfaceLog(keyId, "addSettlementAmountFlag1", newList, dto, null, 1, 3, oldList);
            log.warn("结算金额追加修改前数据：" + JSON.toJSONString(oldList));

        }
        if (flag == 2) {
            Map<String, List<AddSettlementAmountDTO>> collect = dto.getDtos().stream().collect(Collectors.groupingBy(AddSettlementAmountDTO::getReconciliationId));
            for (Map.Entry<String, List<AddSettlementAmountDTO>> entry : collect.entrySet()) {
                String reconciliationId = entry.getKey();
                List<AddSettlementAmountDTO> dtos = entry.getValue();
                MaterialReconciliation m = getById(reconciliationId);
                AddSettlementAmountUpdateDTO updateD = new AddSettlementAmountUpdateDTO();
                // 旧修改DTO
                AddSettlementAmountUpdateDTO updateOldD = new AddSettlementAmountUpdateDTO();
                // 修改DTO
                ArrayList<MaterialReconciliationDtl> updateS = new ArrayList<>();

                ArrayList<MaterialReconciliationDtl> updateOlds = new ArrayList<>();
                MaterialReconciliation oldM = new MaterialReconciliation();
                oldM.setReconciliationId(reconciliationId);
                oldM.setSettleAmount(m.getSettleAmount());
                updateOldD.setMaterialReconciliation(oldM);


                for (AddSettlementAmountDTO d : dtos) {
                    BigDecimal amount = d.getAmount();
                    if (amount == null) {
                        throw new BusinessException("结算金额输入错误！");
                    }
                    LambdaQueryChainWrapper<MaterialReconciliationDtl> qqq = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).and(qw -> qw.eq(MaterialReconciliationDtl::getMaterialName, d.getMaterialName()).or().eq(MaterialReconciliationDtl::getProductName, d.getMaterialName()));
                    if (d.getMaterialId() == null) {
                        qqq.isNull(MaterialReconciliationDtl::getMaterialId);
                    } else {
                        qqq.eq(MaterialReconciliationDtl::getMaterialId, d.getMaterialId());
                    }
                    if (d.getSpec() == null) {
                        qqq.isNull(MaterialReconciliationDtl::getSpec);
                    } else {
                        qqq.eq(MaterialReconciliationDtl::getSpec, d.getSpec());
                    }
                    if (d.getUnit() == null) {
                        qqq.isNull(MaterialReconciliationDtl::getUnit);
                    } else {
                        qqq.eq(MaterialReconciliationDtl::getUnit, d.getUnit());
                    }
                    if (d.getTexture() == null) {
                        qqq.isNull(MaterialReconciliationDtl::getTexture);
                    } else {
                        qqq.eq(MaterialReconciliationDtl::getTexture, d.getTexture());
                    }
                    List<MaterialReconciliationDtl> dtls = qqq.list();
                    if (CollectionUtils.isEmpty(dtls)) {
                        throw new BusinessException("对账单明细不存在！物资id：" + d.getMaterialId() + "，物资名称：" + d.getMaterialName());
                    }
                    ArrayList<MaterialReconciliationDtl> updateDtls = new ArrayList<>();
                    // 处理某一个明细金额分发
                    for (int i = dtls.size() - 1; i >= 0; i--) {
                        MaterialReconciliationDtl md = dtls.get(i);
                        // 旧明细
                        MaterialReconciliationDtl oldDtl = new MaterialReconciliationDtl();
                        oldDtl.setReconciliationDtlId(md.getReconciliationDtlId());
                        oldDtl.setSettledAmount(md.getSettledAmount());
                        updateOlds.add(oldDtl);

                        // 已结算金额
                        BigDecimal settledAmount = md.getSettledAmount();

                        // 回滚金额等于0退出
                        if (amount.compareTo(BigDecimal.ZERO) == 0) {
                            break;
                        }
                        if (settledAmount.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }

                        // 结算金额大于0
                        if (settledAmount.compareTo(BigDecimal.ZERO) > 0 && amount.compareTo(BigDecimal.ZERO) > 0) {
                            // 如果回滚金额大于当前结算金额
                            if (amount.compareTo(settledAmount) > 0) {
                                // 金额减去
                                amount = amount.subtract(settledAmount);
                                md.setSettledAmount(new BigDecimal(0));
                                updateDtls.add(md);
                            } else if (amount.compareTo(settledAmount) < 0) {
                                md.setSettledAmount(settledAmount.subtract(amount));
                                amount = new BigDecimal(0);
                                updateDtls.add(md);
                            } else {
                                // 等于，赋值0
                                amount = new BigDecimal(0);
                                md.setSettledAmount(new BigDecimal(0));
                                updateDtls.add(md);
                            }
                        } else if (amount.compareTo(BigDecimal.ZERO) < 0) {
                            //已结算金额（settledAmount）+冲红金额（amount）不能是负数,如果出错，抛出错误
                            if (settledAmount.add(amount).compareTo(BigDecimal.ZERO) < 0) {
                                throw new BusinessException("回滚金额不能大于已结算金额！");
                            } else {
                                md.setSettledAmount(settledAmount.add(amount));
                                updateDtls.add(md);
                            }

                        }
                    }

                    //旧
                    updateOldD.setMaterialReconciliationDtls(updateOlds);
                    // 修改明细
                    for (MaterialReconciliationDtl md : updateDtls) {
                        MaterialReconciliationDtl materialReconciliationDtl = new MaterialReconciliationDtl();
                        materialReconciliationDtl.setReconciliationDtlId(md.getReconciliationDtlId());
                        materialReconciliationDtl.setSettledAmount(md.getSettledAmount());
                        updateS.add(materialReconciliationDtl);
                    }
                    materialReconciliationDtlService.updateBatchById(updateS);
                }
                // 修改主表
                BigDecimal totalAmount = new BigDecimal(0);
                List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).list();
                for (MaterialReconciliationDtl dtl : list) {
                    totalAmount = totalAmount.add(dtl.getSettledAmount());
                }
                MaterialReconciliation updateM = new MaterialReconciliation();
                updateM.setReconciliationId(reconciliationId);
                updateM.setSettleAmount(totalAmount);
                update(updateM);

                // 保存要修改的数据
                updateD.setMaterialReconciliation(updateM);
                updateD.setMaterialReconciliationDtls(updateS);

                // 删除所有结算单
                String settleAccountsId = dto.getSettleAccountsId();
                dealOrderInfoService.lambdaUpdate().eq(DealOrderInfo::getSettleAccountsId, settleAccountsId).remove();
                // 记录旧的
                oldList.add(updateOldD);
                // 记录新的
                newList.add(updateD);

            }
            // 成功保存日志
            createAndSaveInterfaceLog(keyId, "addSettlementAmountFlag2", newList, dto, null, 1, 3, oldList, "flag2");
            log.warn("结算作废调用数据：" + JSON.toJSONString(oldList));

        }
    }

    /**
     * 回滚对账单结算金额
     *
     * @param keyId
     * @param flag
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollBackAddSettlementAmount(String keyId, Integer flag) {
        if (flag == 1) {
            InterfaceLogs interfaceLogs = interfaceLogsService.lambdaQuery().eq(InterfaceLogs::getSecretKey, keyId).eq(InterfaceLogs::getIsSuccess, 1).eq(InterfaceLogs::getBusinessFlag, "flag1").eq(InterfaceLogs::getLogType, 3).one();
            if (interfaceLogs != null) {
                String farArguments = interfaceLogs.getFarArguments();
                AddSettlementAmountParentDTO dto = JSON.parseObject(farArguments, AddSettlementAmountParentDTO.class);
                // 回滚数据并且删除结算单
                extracted(dto);
                // 成功保存日志
                createAndSaveInterfaceLog(keyId, "rollBackAddSettlementAmountFlag1", null, null, null, 1, 4, null);

            }
        }
        if (flag == 2) {
            InterfaceLogs interfaceLogs = interfaceLogsService.lambdaQuery().eq(InterfaceLogs::getSecretKey, keyId).eq(InterfaceLogs::getIsSuccess, 1).eq(InterfaceLogs::getBusinessFlag, "flag2").eq(InterfaceLogs::getLogType, 3).one();
            if (interfaceLogs != null) {
                String farArguments = interfaceLogs.getFarArguments();
                AddSettlementAmountParentDTO dto = JSON.parseObject(farArguments, AddSettlementAmountParentDTO.class);

                // 增加金额
                extracted1(dto);

                String settleAccountsId = dto.getSettleAccountsId();

                // 修改结算单为0，不删除
                dealOrderInfoMapper.updateTo0BySettleAccountsId(settleAccountsId);

                // 成功保存日志
                createAndSaveInterfaceLog(keyId, "rollBackAddSettlementAmountFlag2", null, null, null, 1, 4, null);

            }
        }

    }

    private void extracted1(AddSettlementAmountParentDTO dto) {
        Map<String, List<AddSettlementAmountDTO>> collect = dto.getDtos().stream().collect(Collectors.groupingBy(AddSettlementAmountDTO::getReconciliationId));
        for (Map.Entry<String, List<AddSettlementAmountDTO>> entry : collect.entrySet()) {
            String reconciliationId = entry.getKey();
            List<AddSettlementAmountDTO> dtos = entry.getValue();


            AddSettlementAmountUpdateDTO updateD = new AddSettlementAmountUpdateDTO();
            // 修改DTO
            ArrayList<MaterialReconciliationDtl> updateS = new ArrayList<>();

            for (AddSettlementAmountDTO d : dtos) {
                BigDecimal amount = d.getAmount();
                if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new BusinessException("结算金额输入错误！");
                }
                LambdaQueryChainWrapper<MaterialReconciliationDtl> qqq = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).eq(MaterialReconciliationDtl::getMaterialName, d.getMaterialName());
                if (d.getMaterialId() == null) {
                    qqq.isNull(MaterialReconciliationDtl::getMaterialId);
                } else {
                    qqq.eq(MaterialReconciliationDtl::getMaterialId, d.getMaterialId());
                }
                if (d.getSpec() == null) {
                    qqq.isNull(MaterialReconciliationDtl::getSpec);
                } else {
                    qqq.eq(MaterialReconciliationDtl::getSpec, d.getSpec());
                }

                if (d.getUnit() == null) {
                    qqq.isNull(MaterialReconciliationDtl::getUnit);
                } else {
                    qqq.eq(MaterialReconciliationDtl::getUnit, d.getUnit());
                }

                if (d.getTexture() == null) {
                    qqq.isNull(MaterialReconciliationDtl::getTexture);
                } else {
                    qqq.eq(MaterialReconciliationDtl::getTexture, d.getTexture());
                }

                List<MaterialReconciliationDtl> dtls = qqq.list();
                if (CollectionUtils.isEmpty(dtls)) {
                    throw new BusinessException("对账单明细不存在！物资id：" + d.getMaterialId() + "，物资名称：" + d.getMaterialName());
                }


                // 处理某一个明细金额分发
                for (int i = 0; i < dtls.size(); i++) {
                    MaterialReconciliationDtl md = dtls.get(i);


                    BigDecimal settledAmount = md.getSettledAmount();
                    BigDecimal acceptanceAmount = md.getAcceptanceAmount();
                    if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                    // 最后一个
                    if (i == dtls.size() - 1) {
                        // 分发完毕了
                        if (acceptanceAmount.compareTo(settledAmount) <= 0) {
                            md.setSettledAmount(settledAmount.add(amount));
                            amount = amount.subtract(amount);
                            continue;
                        } else {
                            // 未结算完
                            BigDecimal sub = acceptanceAmount.subtract(settledAmount);
                            if (sub.compareTo(amount) > 0) {
                                md.setSettledAmount(settledAmount.add(amount));
                                amount = amount.subtract(amount);
                            } else if (sub.compareTo(amount) < 0) {
                                // 小于amount，直接追加全部，因为最后一次
                                md.setSettledAmount(settledAmount.add(amount));
                                amount = amount.subtract(amount);
                            } else {
                                // 相等
                                md.setSettledAmount(settledAmount.add(sub));
                                amount = amount.subtract(amount);
                            }
                            continue;
                        }
                    }
                    // 如果相等表示已分发完毕
                    if (acceptanceAmount.compareTo(settledAmount) == 0) {
                        continue;
                    }

                    // 需要填充结算金额
                    if (acceptanceAmount.compareTo(settledAmount) > 0) {
                        // 拿到需要补充的金额
                        BigDecimal sub = acceptanceAmount.subtract(settledAmount);
                        if (sub.compareTo(amount) > 0) {
                            md.setSettledAmount(settledAmount.add(amount));
                            amount = amount.subtract(amount);
                        } else if (sub.compareTo(amount) < 0) {
                            md.setSettledAmount(settledAmount.add(sub));
                            amount = amount.subtract(sub);
                        } else {
                            // 相等
                            md.setSettledAmount(settledAmount.add(sub));
                            amount = amount.subtract(amount);
                        }
                    }
                }

                // 明细
                for (MaterialReconciliationDtl md : dtls) {
                    MaterialReconciliationDtl materialReconciliationDtl = new MaterialReconciliationDtl();
                    materialReconciliationDtl.setReconciliationDtlId(md.getReconciliationDtlId());
                    materialReconciliationDtl.setSettledAmount(md.getSettledAmount());
                    updateS.add(materialReconciliationDtl);
                }

                updateD.setMaterialReconciliationDtls(updateS);
                materialReconciliationDtlService.updateBatchById(updateD.getMaterialReconciliationDtls());
            }

            // 统计总金额
            BigDecimal totalAmount = new BigDecimal(0);
            List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).list();
            for (MaterialReconciliationDtl dtl : list) {
                totalAmount = totalAmount.add(dtl.getSettledAmount());
            }
            MaterialReconciliation updateM = new MaterialReconciliation();
            updateM.setReconciliationId(reconciliationId);
            updateM.setSettleAmount(totalAmount);

            // 修改主表
            update(updateM);
            // 保存要修改的数据
            updateD.setMaterialReconciliation(updateM);
            updateD.setMaterialReconciliationDtls(updateS);

        }
    }

    private void extracted(AddSettlementAmountParentDTO dto) {
        // 直接减去
        Map<String, List<AddSettlementAmountDTO>> collect = dto.getDtos().stream().collect(Collectors.groupingBy(AddSettlementAmountDTO::getReconciliationId));
        for (Map.Entry<String, List<AddSettlementAmountDTO>> entry : collect.entrySet()) {
            String reconciliationId = entry.getKey();
            List<AddSettlementAmountDTO> dtos = entry.getValue();

            AddSettlementAmountUpdateDTO updateD = new AddSettlementAmountUpdateDTO();
            // 修改DTO
            ArrayList<MaterialReconciliationDtl> updateS = new ArrayList<>();

            for (AddSettlementAmountDTO d : dtos) {
                BigDecimal amount = d.getAmount();
                if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new BusinessException("结算金额输入错误！");
                }
                LambdaQueryChainWrapper<MaterialReconciliationDtl> qqq = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).eq(MaterialReconciliationDtl::getMaterialName, d.getMaterialName());
                if (d.getMaterialId() == null) {
                    qqq.isNull(MaterialReconciliationDtl::getMaterialId);
                } else {
                    qqq.eq(MaterialReconciliationDtl::getMaterialId, d.getMaterialId());
                }
                if (d.getSpec() == null) {
                    qqq.isNull(MaterialReconciliationDtl::getSpec);
                } else {
                    qqq.eq(MaterialReconciliationDtl::getSpec, d.getSpec());
                }
                if (d.getUnit() == null) {
                    qqq.isNull(MaterialReconciliationDtl::getUnit);
                } else {
                    qqq.eq(MaterialReconciliationDtl::getUnit, d.getUnit());
                }
                if (d.getTexture() == null) {
                    qqq.isNull(MaterialReconciliationDtl::getTexture);
                } else {
                    qqq.eq(MaterialReconciliationDtl::getTexture, d.getTexture());
                }
                List<MaterialReconciliationDtl> dtls = qqq.list();
                if (CollectionUtils.isEmpty(dtls)) {
                    throw new BusinessException("对账单明细不存在！物资id：" + d.getMaterialId() + "，物资名称：" + d.getMaterialName());
                }
                ArrayList<MaterialReconciliationDtl> updateDtls = new ArrayList<>();
                // 处理某一个明细金额分发
                for (int i = dtls.size() - 1; i >= 0; i--) {
                    MaterialReconciliationDtl md = dtls.get(i);

                    // 已结算金额
                    BigDecimal settledAmount = md.getSettledAmount();

                    // 回滚金额等于0退出
                    if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                    if (settledAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }

                    // 结算金额大于0
                    if (settledAmount.compareTo(BigDecimal.ZERO) > 0) {
                        // 如果回滚金额大于当前结算金额
                        if (amount.compareTo(settledAmount) > 0) {
                            // 金额减去
                            amount = amount.subtract(settledAmount);
                            md.setSettledAmount(new BigDecimal(0));
                            updateDtls.add(md);
                        } else if (amount.compareTo(settledAmount) < 0) {
                            md.setSettledAmount(settledAmount.subtract(amount));
                            amount = new BigDecimal(0);
                            updateDtls.add(md);
                        } else {
                            // 等于，赋值0
                            amount = new BigDecimal(0);
                            md.setSettledAmount(new BigDecimal(0));
                            updateDtls.add(md);
                        }
                    }
                }

                // 修改明细
                for (MaterialReconciliationDtl md : updateDtls) {
                    MaterialReconciliationDtl materialReconciliationDtl = new MaterialReconciliationDtl();
                    materialReconciliationDtl.setReconciliationDtlId(md.getReconciliationDtlId());
                    materialReconciliationDtl.setSettledAmount(md.getSettledAmount());
                    updateS.add(materialReconciliationDtl);
                }
                materialReconciliationDtlService.updateBatchById(updateS);
            }
            // 修改主表
            BigDecimal totalAmount = new BigDecimal(0);
            List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).list();
            for (MaterialReconciliationDtl dtl : list) {
                totalAmount = totalAmount.add(dtl.getSettledAmount());
            }
            MaterialReconciliation updateM = new MaterialReconciliation();
            updateM.setReconciliationId(reconciliationId);
            updateM.setSettleAmount(totalAmount);

            // 修改主表
            update(updateM);

            // 保存要修改的数据
            updateD.setMaterialReconciliation(updateM);
            updateD.setMaterialReconciliationDtls(updateS);

            // 删除所有结算单
            String settleAccountsId = dto.getSettleAccountsId();
            dealOrderInfoService.lambdaUpdate().eq(DealOrderInfo::getSettleAccountsId, settleAccountsId).remove();

        }
    }

    /**
     * 主动推送对账单
     *
     * @param reconciliationId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationPushAcceptance(String reconciliationId) {
        MaterialReconciliation byId = getById(reconciliationId);
        Boolean r11Bool = null;
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String dateString = currentDate.format(formatter);
        String keyId = IdWorker.getIdStr();
        if (byId.getBusinessType() == 2 || byId.getBusinessType() == 6) {//零星采购或者大宗临购
            //  判断是否能推送验收单
            PcwpRes<Boolean> r11 = null;
            try {
                r11 = pcwpService.isCanOperaBill(byId.getPurchasingOrgId(), DateUtil.getymd(LocalDate.now()));
            } catch (Exception e) {
                throw new BusinessException("【远程异常】判断是否可推送验收单：" + e.getMessage());
            }
            if (r11.getCode() == null || r11.getCode() != 200) {
                throw new BusinessException("【远程异常】判断是否可推送验收单：" + r11.getMessage());
            } else {
                r11Bool = (Boolean) r11.getData();
            }
            log.warn("判断是否可推送验收返回：" + r11);
            if (r11Bool == null || r11Bool == false) {
                throw new BusinessException("当前为pcwp月结处理中，暂时无法推送验收单！");
            }
            HashMap<String, Object> dataMap = new HashMap<>();
            HashMap<String, Object> data = new HashMap<>();
            ArrayList<Map> dtls = new ArrayList<>();
            BigDecimal acceptanceQuantity = new BigDecimal(0);

            List<MaterialReconciliationDtl> countDtlList = materialReconciliationDtlMapper.getDtlCountListByReconciliationId(reconciliationId);
            for (MaterialReconciliationDtl md : countDtlList) {
                // 保存明细
                BigDecimal acceptanceNoRateAmount = md.getAcceptanceNoRateAmount();
                BigDecimal quantity = md.getQuantity();
                acceptanceQuantity = acceptanceQuantity.add(quantity);
                HashMap<String, Object> dtl = new HashMap<>();
                dtl.put("acceptanceAmount", acceptanceNoRateAmount);
                dtl.put("tradeId", md.getTradeId());
                dtl.put("orderId", md.getOrderId());
                dtl.put("orderNo", md.getOrderSn());
                dtl.put("acceptanceQuantity", quantity);
                dtl.put("materialClassId", md.getMaterialClassId());
                dtl.put("materialClassName", md.getMaterialClassName());
                dtl.put("materialId", md.getMaterialId());
                dtl.put("materialName", md.getMaterialName());
                // 单价取均价
                BigDecimal price = acceptanceNoRateAmount.divide(quantity, new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP);
                dtl.put("price", price);
                dtl.put("spec", md.getSpec());
                dtl.put("texture", md.getTexture());
                dtl.put("unit", md.getUnit());
                dtl.put("warehouseId", md.getWarehouseId());
                dtl.put("warehouseName", md.getWarehouseName());
                dtls.add(dtl);
            }
            data.put("dtls", dtls);
            String s = DateUtil.getyyymmddHHmmssStr(byId.getGmtCreate());
            data.put("acceptanceDate", s);
            data.put("acceptanceQuantity", acceptanceQuantity);
            data.put("totalAmount", byId.getReconciliationAmount());
            data.put("taxAmount", byId.getReconciliationAmount().subtract(byId.getReconciliationNoRateAmount()));
            data.put("acceptanceAmount", byId.getReconciliationNoRateAmount());
            // 不管是供应商新增还是采购员新增单据统一使用采购员的id

            if (byId.getCreateType() != 2) {
                User userOne = userService.lambdaQuery().eq(User::getUserId, byId.getFounderId()).select(User::getInteriorId, User::getNickName).one();
                data.put("founderId", userOne.getInteriorId());
                data.put("founderName", userOne.getNickName());
                data.put("acceptancerId", userOne.getInteriorId());
                data.put("acceptancerName", userOne.getNickName());
            } else {
                data.put("founderId", byId.getPurchaserId());
                data.put("founderName", byId.getPurchaserName());
                data.put("acceptancerId", byId.getPurchaserId());
                data.put("acceptancerName", byId.getPurchaserName());
            }
            data.put("businessType", byId.getBusinessType());
            data.put("orgId", byId.getPurchasingOrgId());
            data.put("orgName", byId.getPurchasingOrgName());
            data.put("outerId", byId.getReconciliationId());
            data.put("outerNo", byId.getReconciliationNo());
            data.put("purchaserId", byId.getPurchaserId());
            data.put("purchaserName", byId.getPurchaserName());
            data.put("purchasingUnitId", byId.getPurchasingOrgId());
            data.put("purchasingUnitName", byId.getPurchasingOrgName());
            data.put("remarks", byId.getRemarks());
            data.put("sourceBillId", byId.getSourceBillId());
            data.put("sourceBillNo", byId.getSourceBillNo());
            data.put("sourceBillName", byId.getSourceBillName());
            data.put("supplierId", byId.getSupplierId());
            data.put("supplierName", byId.getSupplierName());
            data.put("taxRate", byId.getTaxRate());
            dataMap.put("data", data);
            dataMap.put("keyId", keyId);
            R r = null;
            try {
                r = restTemplateUtils.postPCWP2(mallConfig.prodPcwp2Url02 + SAVE_ACCEPTANCE_URL, dataMap);
            } catch (Exception e) {
                throw new BusinessException("【远程异常】" + e.getMessage());
            }
            if (r.getCode() == null || r.getCode() != 200) {
                throw new BusinessException("【远程异常】" + r.getMessage());
            }
            // 拿到验收id
            String billId = (String) r.getData();
            if (StringUtils.isBlank(billId)) {
                throw new BusinessException("验收id不存在！");
            }
            lambdaUpdate().eq(MaterialReconciliation::getReconciliationId, reconciliationId).set(MaterialReconciliation::getRelevanceId, billId).update();
        } else if (byId.getBusinessType() == 7) {//周转材料
            //调用pcwp接口 先调用isRevolCanOperaBill 判断，然后调用saveAcceptanceBill
            try {
                log.warn("判断周转材料是否可推送验收请求参数：orgId=" + byId.getPurchasingOrgId() + "&date=" + DateUtil.getymd(LocalDate.now()));
                PcwpRes<Boolean> pcwpRes = pcwpService.isRevolCanOperaBill(byId.getPurchasingOrgId(), DateUtil.getymd(LocalDate.now()));
                if (pcwpRes.getCode() == null || pcwpRes.getCode() != 200) {
                    throw new BusinessException("【远程异常】判断周转材料是否可推送验收单：" + pcwpRes.getMessage());
                }
                r11Bool = pcwpRes.getData();
            } catch (Exception e) {
                throw new BusinessException("【远程异常】判断周转材料是否可推送验收单：" + e.getMessage());
            }

            if (r11Bool != null && r11Bool == true) {
                // 构建周转材料验收单数据
                PcwpRevolAcceptanceDto acceptanceDto = new PcwpRevolAcceptanceDto();
                List<PcwpRevolAcceptanceDto.AcceptanceDtl> acceptanceDtls = new ArrayList<>();
                BigDecimal totalAcceptanceAmount = new BigDecimal(0);
                List<MaterialReconciliationDtl> countDtlList = materialReconciliationDtlMapper.getDtlCountListByReconciliationId(reconciliationId);
                for (MaterialReconciliationDtl md : countDtlList) {
                    // 保存明细
                    totalAcceptanceAmount = totalAcceptanceAmount.add(md.getAcceptanceAmount());
                    PcwpRevolAcceptanceDto.AcceptanceDtl dtl = PcwpRevolAcceptanceDto.AcceptanceDtl.builder()
                            .acceptanceAmount(md.getAcceptanceAmount())
                            .acceptanceQuantity(md.getQuantity())
                            .materialClassId(md.getMaterialClassId())
                            .materialClassName(md.getMaterialClassName())
                            .materialId(md.getMaterialId())
                            .materialName(md.getMaterialName())
                            .price(md.getPrice())
                            .sourceDtlId(md.getReceiptBillDtlId())
                            .sourceAmount(md.getAcceptanceNoRateAmount())
                            .sourceQuantity(md.getQuantity())
                            .spec(md.getSpec())
                            .taxAmount(md.getTaxAmount())
                            .texture(md.getTexture())
                            .unit(md.getUnit())
                            .warehouseId(md.getWarehouseId())
                            .warehouseName(md.getWarehouseName())
                            .build();
                    acceptanceDtls.add(dtl);
                }

                // 设置验收单主要信息
                acceptanceDto.setAcceptanceDtls(acceptanceDtls);
                acceptanceDto.setAcceptanceAmount(totalAcceptanceAmount);
                acceptanceDto.setAcceptanceDate(DateUtil.getyyymmddHHmmssStr(new Date()));
                acceptanceDto.setBillId(byId.getSourceBillId());
                acceptanceDto.setBillNo(byId.getSourceBillNo());
                acceptanceDto.setBillSource(1);
                acceptanceDto.setBusinessTypeKey(String.valueOf(byId.getBusinessType()));
                acceptanceDto.setBusinessTypeValue("周转材料");
                acceptanceDto.setPurchasingUnitId(byId.getPurchasingOrgId());
                acceptanceDto.setPurchasingUnitName(byId.getPurchasingOrgName());
                acceptanceDto.setRemarks(byId.getRemarks());
                acceptanceDto.setRmbAmount(totalAcceptanceAmount);
                acceptanceDto.setSourceId(byId.getRelevanceId());
                acceptanceDto.setSourceNo(byId.getRelevanceSn());
                acceptanceDto.setState(1);
                acceptanceDto.setSupplierId(byId.getSupplierId());
                acceptanceDto.setSupplierName(byId.getSupplierName());
                acceptanceDto.setTaxAmount(byId.getTaxAmount());
                acceptanceDto.setTaxRate(byId.getTaxRate());
                acceptanceDto.setTotalPriceAndTax(byId.getReconciliationAmount());

                // 设置验收人和采购人信息
                if (byId.getCreateType() != 2) {
                    User userOne = userService.lambdaQuery().eq(User::getUserId, byId.getFounderId()).select(User::getInteriorId, User::getNickName).one();
                    acceptanceDto.setAcceptancerId(userOne.getInteriorId());
                    acceptanceDto.setAcceptancerName(userOne.getNickName());
                    acceptanceDto.setPurchaserId(userOne.getInteriorId());
                    acceptanceDto.setPurchaserName(userOne.getNickName());
                } else {
                    acceptanceDto.setAcceptancerId(byId.getPurchaserId());
                    acceptanceDto.setAcceptancerName(byId.getPurchaserName());
                    acceptanceDto.setPurchaserId(byId.getPurchaserId());
                    acceptanceDto.setPurchaserName(byId.getPurchaserName());
                }

                // 构建请求对象
                KeyedPayload<PcwpRevolAcceptanceDto> request = KeyedPayload.<PcwpRevolAcceptanceDto>builder()
                        .data(acceptanceDto)
                        .keyId(keyId)
                        .orgId(byId.getPurchasingOrgId())
                        .build();

                // 推送周转材料验收单
                PcwpRes<String> pcwpRes = null;
                try {
                    pcwpRes = pcwpService.saveAcceptanceForRevol(request);
                    if (pcwpRes.getCode() == null || pcwpRes.getCode() != 200) {
                        throw new BusinessException("【远程异常】" + pcwpRes.getMessage());
                    }
                } catch (Exception e) {
                    throw new BusinessException("【远程异常】" + e.getMessage());
                }
                // 周转材料验收单推送成功，使用keyId作为关联ID
                String billId = pcwpRes.getData();
                if (StringUtils.isBlank(billId)) {
                    throw new BusinessException("验收关联id不存在！");
                }
                // 更新对账单状态
                lambdaUpdate().eq(MaterialReconciliation::getReconciliationId, reconciliationId)
                        .set(MaterialReconciliation::getRelevanceId, billId)
                        .set(MaterialReconciliation::getIsNotPush, 1)
                        .update();
            }
        }
    }

    /**
     * 根据对账单id查询对账单状态
     *
     * @param reconciliationId
     * @return
     */
    @Override
    public CheckReconciliationIsCancellationVO checkReconciliationIsCancellation(String reconciliationId) {
        MaterialReconciliation byId = getById(reconciliationId);
        if (byId == null) {
            CheckReconciliationIsCancellationVO vo = new CheckReconciliationIsCancellationVO();
            vo.setErrorCode(5001);
            vo.setErrorMsg("对账单不存在！");
            return vo;
        } else {
            Integer state = byId.getState();
            if (state != 3) {
                CheckReconciliationIsCancellationVO vo = new CheckReconciliationIsCancellationVO();
                vo.setErrorCode(5002);
                vo.setErrorMsg("对账单未审核完成！");
                return vo;
            } else {
                return null;
            }
        }
    }

    /**
     * 定时查询待推送数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushMaterialReconciliationData() {
        List<MaterialReconciliation> ms = lambdaQuery().eq(MaterialReconciliation::getState, 3).eq(MaterialReconciliation::getIsNotPush, 1).list();
        if (CollectionUtils.isEmpty(ms)) {
        } else {
            ArrayList<String> strings = new ArrayList<>();
            for (MaterialReconciliation m : ms) {
                MaterialReconciliationService materialReconciliationService = SpringBeanUtil.getBean(MaterialReconciliationService.class);
                try {
                    materialReconciliationService.pushMaterialReconciliationDataOne(m);
                } catch (Exception e) {
                    String str = "对账单id：" + m.getReconciliationId() + "，定时推送验收单出现异常：" + e.getMessage();
                    strings.add(str);
                }
            }
            if (!CollectionUtils.isEmpty(strings)) {
                log.error("定时推送对账单出现异常：");
                log.warn(JSON.toJSONString(strings));
            }
        }

    }

    /**
     * 定时查询待推送数据
     *
     * @param byId
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void pushMaterialReconciliationDataOne(MaterialReconciliation byId) {
        Boolean r11Bool = null;
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String dateString = currentDate.format(formatter);
        String keyId = IdWorker.getIdStr();
        if (byId.getBusinessType() == 2 || byId.getBusinessType() == 6) {
            //  判断是否能推送验收单
            PcwpRes<Boolean> r11 = null;
            try {
                r11 = pcwpService.isCanOperaBill(byId.getPurchasingOrgId(), DateUtil.getymd(LocalDate.now()));
            } catch (Exception e) {
                throw new BusinessException("【远程异常】判断是否可推送验收单：" + e.getMessage());
            }
            if (r11.getCode() == null || r11.getCode() != 200) {
                // 返回不是200异常
                throw new BusinessException("【远程异常】判断是否可推送验收单：" + r11.getMessage());
            } else {
                r11Bool = (Boolean) r11.getData();
            }
            log.warn("判断是否可推送验收返回：" + r11);
            if (r11Bool == null || r11Bool == false) {
                throw new BusinessException("pcwp月结中，请月结结束后在重新收货，参数orgid：" + byId.getPurchasingOrgId() + "日期：" + dateString);
            }
            HashMap<String, Object> dataMap = new HashMap<>();
            HashMap<String, Object> data = new HashMap<>();
            ArrayList<Map> dtls = new ArrayList<>();
            BigDecimal acceptanceQuantity = new BigDecimal(0);
            List<MaterialReconciliationDtl> countDtlList = null;
            if (byId.getBusinessType() == 6) {
                countDtlList = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, byId.getReconciliationId()).list();
            } else {
                countDtlList = materialReconciliationDtlMapper.getDtlCountListByReconciliationId(byId.getReconciliationId());
            }
            for (MaterialReconciliationDtl md : countDtlList) {
                // 保存明细
                BigDecimal acceptanceNoRateAmount = md.getAcceptanceNoRateAmount();
                BigDecimal quantity = md.getQuantity();
                acceptanceQuantity = acceptanceQuantity.add(quantity);
                HashMap<String, Object> dtl = new HashMap<>();
                dtl.put("acceptanceAmount", acceptanceNoRateAmount);
                dtl.put("acceptanceQuantity", quantity);
                dtl.put("tradeId", md.getTradeId());
                dtl.put("orderId", md.getOrderId());
                dtl.put("orderNo", md.getOrderSn());
                dtl.put("materialClassId", md.getMaterialClassId());
                dtl.put("materialClassName", md.getMaterialClassName());
                dtl.put("materialId", md.getMaterialId());
                if (byId.getBusinessType() == 6) {
                    dtl.put("networkPrice", TaxCalculator.calculateNotTarRateAmount(md.getFreightPrice(), byId.getTaxRate()));
                    dtl.put("fixedFee", TaxCalculator.calculateNotTarRateAmount(md.getFixationPrice(), byId.getTaxRate()));
                    dtl.put("factoryPrice", TaxCalculator.calculateNotTarRateAmount(md.getOutFactoryPrice(), byId.getTaxRate()));
                    dtl.put("freight", TaxCalculator.calculateNotTarRateAmount(md.getTransportPrice(), byId.getTaxRate()));
                }
                dtl.put("materialName", md.getMaterialName());
                // 单价取均价
                BigDecimal price = acceptanceNoRateAmount.divide(quantity, new MathContext(18)).setScale(2, BigDecimal.ROUND_HALF_UP);
                if (byId.getBusinessType() == 6) {
                    dtl.put("price", md.getNoRatePrice());
                } else {
                    dtl.put("price", price);
                }
                dtl.put("spec", md.getSpec());
                dtl.put("texture", md.getTexture());
                dtl.put("unit", md.getUnit());
                dtl.put("taxAmount", md.getTaxAmount());
                dtl.put("warehouseId", md.getWarehouseId());
                dtl.put("warehouseName", md.getWarehouseName());
                dtls.add(dtl);
            }
            if (byId.getBusinessType() == 6) {
                data.put("type", byId.getType());
            }
            data.put("dtls", dtls);
            String s = DateUtil.getyyymmddHHmmssStr(byId.getGmtCreate());
            ;
            data.put("acceptanceDate", s);
            data.put("acceptanceQuantity", acceptanceQuantity);
            data.put("totalAmount", byId.getReconciliationAmount());
            data.put("taxAmount", byId.getReconciliationAmount().subtract(byId.getReconciliationNoRateAmount()));
            data.put("acceptanceAmount", byId.getReconciliationNoRateAmount());
            // 不管是供应商新增还是采购员新增单据统一使用采购员的id
            if (byId.getCreateType() != 2) {
                User userOne = userService.lambdaQuery().eq(User::getUserId, byId.getFounderId()).select(User::getInteriorId, User::getNickName).one();
                data.put("founderId", userOne.getInteriorId());
                data.put("founderName", userOne.getNickName());
                data.put("acceptancerId", userOne.getInteriorId());
                data.put("acceptancerName", userOne.getNickName());
            } else {
                data.put("founderId", byId.getPurchaserId());
                data.put("founderName", byId.getPurchaserName());
                data.put("acceptancerId", byId.getPurchaserId());
                data.put("acceptancerName", byId.getPurchaserName());
            }
            data.put("businessType", byId.getBusinessType());
            data.put("orgId", byId.getPurchasingOrgId());
            data.put("orgName", byId.getPurchasingOrgName());
            data.put("outerId", byId.getReconciliationId());
            data.put("outerNo", byId.getReconciliationNo());
            data.put("purchaserId", byId.getPurchaserId());
            data.put("purchaserName", byId.getPurchaserName());
            data.put("purchasingUnitId", byId.getPurchasingOrgId());
            data.put("purchasingUnitName", byId.getPurchasingOrgName());
            data.put("remarks", byId.getRemarks());
            data.put("sourceBillId", byId.getSourceBillId());
            data.put("sourceBillNo", byId.getSourceBillNo());
            data.put("sourceBillName", byId.getSourceBillName());
            data.put("supplierId", byId.getSupplierId());
            data.put("supplierName", byId.getSupplierName());
            data.put("taxRate", byId.getTaxRate());
            dataMap.put("data", data);
            dataMap.put("keyId", keyId);
            R r = null;
            try {
                r = restTemplateUtils.postPCWP2(mallConfig.prodPcwp2Url02 + SAVE_ACCEPTANCE_URL, dataMap);
            } catch (Exception e) {
                throw new BusinessException("【远程异常】" + e.getMessage());
            }
            if (r.getCode() == null || r.getCode() != 200) {
                throw new BusinessException("【远程异常】" + r.getMessage());
            }
            String billId = (String) r.getData();
            if (StringUtils.isBlank(billId)) {
                throw new BusinessException("验收id不存在！");
            }
            lambdaUpdate()
                    .eq(MaterialReconciliation::getReconciliationId, byId.getReconciliationId())
                    .set(MaterialReconciliation::getRelevanceId, billId)
                    .set(MaterialReconciliation::getIsNotPush, 1)
                    .set(MaterialReconciliation::getGmtModified, new Date()).update();
        } else if (byId.getBusinessType() == 7) {//周转材料
            //调用pcwp接口 先调用isRevolCanOperaBill 判断，然后调用saveAcceptanceBill
            try {
                log.warn("判断周转材料是否可推送验收请求参数：orgId=" + byId.getPurchasingOrgId() + "&date=" + DateUtil.getymd(LocalDate.now()));
                PcwpRes<Boolean> pcwpRes = pcwpService.isRevolCanOperaBill(byId.getPurchasingOrgId(), DateUtil.getymd(LocalDate.now()));
                if (pcwpRes.getCode() == null || pcwpRes.getCode() != 200) {
                    throw new BusinessException("【远程异常】判断周转材料是否可推送验收单：" + pcwpRes.getMessage());
                }
                r11Bool = pcwpRes.getData();
            } catch (Exception e) {
                throw new BusinessException("【远程异常】判断周转材料是否可推送验收单：" + e.getMessage());
            }
            if (r11Bool != null && r11Bool == true) {
                // 构建周转材料验收单数据
                PcwpRevolAcceptanceDto acceptanceDto = new PcwpRevolAcceptanceDto();
                List<PcwpRevolAcceptanceDto.AcceptanceDtl> acceptanceDtls = new ArrayList<>();
                BigDecimal totalAcceptanceAmount = new BigDecimal(0);
                List<MaterialReconciliationDtl> countDtlList = materialReconciliationDtlMapper.getDtlCountListByReconciliationId(byId.getReconciliationId());
                for (MaterialReconciliationDtl md : countDtlList) {
                    // 保存明细
                    totalAcceptanceAmount = totalAcceptanceAmount.add(md.getAcceptanceAmount());
                    PcwpRevolAcceptanceDto.AcceptanceDtl dtl = PcwpRevolAcceptanceDto.AcceptanceDtl.builder()
                            .acceptanceAmount(md.getAcceptanceAmount())
                            .acceptanceQuantity(md.getQuantity())
                            .materialClassId(md.getMaterialClassId())
                            .materialClassName(md.getMaterialClassName())
                            .materialId(md.getMaterialId())
                            .materialName(md.getMaterialName())
                            .price(md.getPrice())
                            .sourceDtlId(md.getReceiptBillDtlId())
                            .sourceAmount(md.getAcceptanceNoRateAmount())
                            .sourceQuantity(md.getQuantity())
                            .spec(md.getSpec())
                            .taxAmount(md.getTaxAmount())
                            .texture(md.getTexture())
                            .unit(md.getUnit())
                            .warehouseId(md.getWarehouseId())
                            .warehouseName(md.getWarehouseName())
                            .build();
                    acceptanceDtls.add(dtl);
                }

                // 设置验收单主要信息
                acceptanceDto.setAcceptanceDtls(acceptanceDtls);
                acceptanceDto.setAcceptanceAmount(totalAcceptanceAmount);
                acceptanceDto.setAcceptanceDate(DateUtil.getyyymmddHHmmssStr(new Date()));
                acceptanceDto.setBillId(byId.getSourceBillId());
                acceptanceDto.setBillNo(byId.getSourceBillNo());
                acceptanceDto.setBillSource(1);
                acceptanceDto.setBusinessTypeKey(String.valueOf(byId.getBusinessType()));
                acceptanceDto.setBusinessTypeValue("周转材料");
                acceptanceDto.setPurchasingUnitId(byId.getPurchasingOrgId());
                acceptanceDto.setPurchasingUnitName(byId.getPurchasingOrgName());
                acceptanceDto.setRemarks(byId.getRemarks());
                acceptanceDto.setRmbAmount(totalAcceptanceAmount);
                acceptanceDto.setSourceId(byId.getRelevanceId());
                acceptanceDto.setSourceNo(byId.getRelevanceSn());
                acceptanceDto.setState(1);
                acceptanceDto.setSupplierId(byId.getSupplierId());
                acceptanceDto.setSupplierName(byId.getSupplierName());
                acceptanceDto.setTaxAmount(byId.getTaxAmount());
                acceptanceDto.setTaxRate(byId.getTaxRate());
                acceptanceDto.setTotalPriceAndTax(byId.getReconciliationAmount());

                // 设置验收人和采购人信息
                if (byId.getCreateType() != 2) {
                    User userOne = userService.lambdaQuery().eq(User::getUserId, byId.getFounderId()).select(User::getInteriorId, User::getNickName).one();
                    acceptanceDto.setAcceptancerId(userOne.getInteriorId());
                    acceptanceDto.setAcceptancerName(userOne.getNickName());
                    acceptanceDto.setPurchaserId(userOne.getInteriorId());
                    acceptanceDto.setPurchaserName(userOne.getNickName());
                } else {
                    acceptanceDto.setAcceptancerId(byId.getPurchaserId());
                    acceptanceDto.setAcceptancerName(byId.getPurchaserName());
                    acceptanceDto.setPurchaserId(byId.getPurchaserId());
                    acceptanceDto.setPurchaserName(byId.getPurchaserName());
                }
                // 构建请求对象
                KeyedPayload<PcwpRevolAcceptanceDto> request = KeyedPayload.<PcwpRevolAcceptanceDto>builder()
                        .data(acceptanceDto)
                        .keyId(keyId)
                        .orgId(byId.getPurchasingOrgId())
                        .build();
                // 推送周转材料验收单
                PcwpRes<String> pcwpRes = null;
                try {
                    pcwpRes = pcwpService.saveAcceptanceForRevol(request);
                } catch (Exception e) {
                    throw new BusinessException("【远程异常】" + e.getMessage());
                }
                if (pcwpRes.getCode() == null || pcwpRes.getCode() != 200) {
                    throw new BusinessException("【远程异常】" + pcwpRes.getMessage());
                }
                // 周转材料验收单推送成功，使用keyId作为关联ID
                String billId = pcwpRes.getData();
                if (StringUtils.isBlank(billId)) {
                    throw new BusinessException("验收关联id不存在！");
                }
                // 更新对账单状态
                lambdaUpdate().
                        eq(MaterialReconciliation::getReconciliationId, byId.getReconciliationId())
                        .set(MaterialReconciliation::getRelevanceId, billId)
                        .set(MaterialReconciliation::getIsNotPush, 1)
                        .set(MaterialReconciliation::getGmtModified, new Date())
                        .update();
            }
        }
    }


    @Override
    public PageUtils getShopManageSettlementFromList(JSONObject jsonObject, QueryWrapper<MaterialReconciliation> q) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        q.eq("supplier_id", user.getOrgId());

        String supplierName = (String) jsonObject.get("supplierName");
        String keywords = (String) jsonObject.get("keywords");

        String startFinishDate = (String) jsonObject.get("startFinishDate");
        String endFinishDate = (String) jsonObject.get("endFinishDate");

        String belowPrice = (String) jsonObject.get("belowPrice");
        String abovePrice = (String) jsonObject.get("abovePrice");
        List<String> ids = (ArrayList<String>) jsonObject.get("ids");
        if (io.seata.common.util.StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like("enterprise_name", supplierName).or().like("supplier_name", keywords).or().like("shop_name", keywords);
            });
        }
        if (ids != null && ids.size() > 0) {
            q.in("reconciliation_id", ids);
        }
        q.eq(io.seata.common.util.StringUtils.isNotBlank(supplierName), "enterprise_name", supplierName);
        q.ge(io.seata.common.util.StringUtils.isNotBlank(abovePrice), "settleAmount", abovePrice);
        q.le(io.seata.common.util.StringUtils.isNotBlank(belowPrice), "settleAmount", belowPrice);
//        q.between(io.seata.common.util.StringUtils.isNotEmpty(startFinishDate) && io.seata.common.util.StringUtils.isNotEmpty(endFinishDate), "end_time", startFinishDate, endFinishDate);
        IPage<MaterialReconciliation> page = this.page(new Query<MaterialReconciliation>().getPage(jsonObject), q);
        return new PageUtils(page);

    }

    @Override
    public PageUtils getPlatformSettlementFromList(JSONObject jsonObject, QueryWrapper<MaterialReconciliation> q) {
        String supplierName = (String) jsonObject.get("supplierName");
        String keywords = (String) jsonObject.get("keywords");

        String startFinishDate = (String) jsonObject.get("startFinishDate");
        String endFinishDate = (String) jsonObject.get("endFinishDate");

        String belowPrice = (String) jsonObject.get("belowPrice");
        String abovePrice = (String) jsonObject.get("abovePrice");
        List<String> ids = (ArrayList<String>) jsonObject.get("ids");
        if (io.seata.common.util.StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like("enterprise_name", supplierName).or().like("supplier_name", keywords).or().like("shop_name", keywords);
            });
        }
        if (ids != null && ids.size() > 0) {
            q.in("reconciliation_id", ids);
        }
        q.eq(io.seata.common.util.StringUtils.isNotBlank(supplierName), "enterprise_name", supplierName);
        q.ge(io.seata.common.util.StringUtils.isNotBlank(abovePrice), "settleAmount", abovePrice);
        q.le(io.seata.common.util.StringUtils.isNotBlank(belowPrice), "settleAmount", belowPrice);
//        q.between(io.seata.common.util.StringUtils.isNotEmpty(startFinishDate) && io.seata.common.util.StringUtils.isNotEmpty(endFinishDate), "end_time", startFinishDate, endFinishDate);
        IPage<MaterialReconciliation> page = this.page(new Query<MaterialReconciliation>().getPage(jsonObject), q);
        return new PageUtils(page);
    }

    /**
     * 大宗临购新增对账单
     *
     * @param d
     * @param farArg
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String materialReconciliationSTCreate(MaterialReconciliation d, StringBuilder farArg) {

        String keyId = d.getKeyId();
        getCreateSTAmount(d);
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String materialPlanNo = createMaterialPlanNo(d.getPurchasingOrgId());
        d.setAcceptanceName(user.getUserName());
        d.setOrgName(user.getEnterpriseName());
        d.setOrgId(user.getOrgId());
        d.setReconciliationNo(materialPlanNo);
        d.setAcceptanceId(user.getFarUserId());
        d.setEnterpriseId(user.getEnterpriseId());
        d.setState(0);
        if (d.getIsSubmit() == 1) {
            d.setState(2);
        }
        boolean save = save(d);
        if (save) {
            for (MaterialReconciliationDtl md : d.getDtl()) {
                isWarehouseIdExist(md);
                md.setReconciliationId(d.getReconciliationId());
                setProductNameAndProductName(md);
                materialReconciliationDtlService.save(md);
            }
            PcwpRes<Void> r;
            Object logData;
            String logOperation = "materialReconciliationSTCreate";

            if (d.getReconciliationProductType() == 2) {//周转材料
                List<UpdatePlanDtl> updatePlanDtls = getUpdateQuCountMForRevol(d);
                KeyedPayload<List<UpdatePlanDtl>> payload = KeyedPayload.<List<UpdatePlanDtl>>builder().data(updatePlanDtls).keyId(keyId).orgId(d.getPurchasingOrgId()).build();
                farArg.append(JSON.toJSONString(payload));
                r = pcwpService.updateRevolPlanDtl(payload);
                logData = payload;
            } else {//零星采购或者大宗临购
                List<ReconciliationDtl> reconciliationDtls = getReconciliationDtls(d);
                KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder().data(reconciliationDtls).keyId(keyId).orgId(d.getPurchasingOrgId()).build();
                farArg.append(JSON.toJSONString(payload));
                r = pcwpService.writeBackBillLockQuantiy(payload);
                logData = payload;
            }
            if (r.getCode() == null || r.getCode() != 200) {
                throw new BusinessException("【远程异常】" + r.getMessage());
            }
            createAndSaveInterfaceLog(keyId, logOperation, d, logData, r, 1, 1, null);
        }
        return d.getReconciliationNo();
    }

    /**
     * 供应商新增大宗临购对账
     *
     * @param d
     * @param farArg
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String materialReconciliationSTSupplierCreate(MaterialReconciliation d, StringBuilder farArg, int isNotPush) {

        if (d.getIsSubmit() == 0) {
            d.setState(0);
        } else {
            d.setState(1);
        }
        getCreateSTAmount(d);
        d.setCreateType(2);
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String materialPlanNo = createMaterialPlanNo(d.getPurchasingOrgId());
        d.setReconciliationNo(materialPlanNo);
        d.setAcceptanceId(user.getFarUserId());
        d.setAcceptanceName(user.getUserName());
        d.setOrgName(user.getEnterpriseName());
        d.setOrgId(user.getOrgId());
        d.setEnterpriseId(user.getEnterpriseId());
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        boolean save = save(d);
        if (save) {
            List<MaterialReconciliationDtl> dtl = d.getDtl();
            for (MaterialReconciliationDtl md : dtl) {
                md.setReconciliationId(d.getReconciliationId());
                if (currentUser.getIsInterior() == 1) { //外部用户-生成对账单信息
                    setProductNameAndProductName(md);
                }
                materialReconciliationDtlService.save(md);
            }
            if (isNotPush == 0) {//计划已经是否推送PCWP，isNotPush==1，计划已经推送PCWP,后续都要推送
                return d.getReconciliationNo();
            } else {//内部用户
                String keyId = d.getKeyId();
                PcwpRes<Void> r;
                Object logData;
                String logOperation = "materialReconciliationSTSupplierCreate";

                if (d.getReconciliationProductType() == 2) {//周转材料
                    List<UpdatePlanDtl> updatePlanDtls = getUpdateQuCountMForRevol(d);
                    KeyedPayload<List<UpdatePlanDtl>> payload = KeyedPayload.<List<UpdatePlanDtl>>builder().data(updatePlanDtls).keyId(keyId).orgId(d.getPurchasingOrgId()).build();
                    farArg.append(JSON.toJSONString(payload));
                    r = pcwpService.updateRevolPlanDtl(payload);
                    logData = payload;
                } else {//零星采购或者大宗临购
                    List<ReconciliationDtl> reconciliationDtls = getReconciliationDtls(d);
                    KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder().data(reconciliationDtls).keyId(keyId).orgId(d.getPurchasingOrgId()).build();
                    farArg.append(JSON.toJSONString(payload));
                    r = pcwpService.writeBackBillLockQuantiy(payload);
                    logData = payload;
                }

                // 统一的异常处理和日志记录
                if (r.getCode() == null || r.getCode() != 200) {
                    throw new BusinessException("【远程异常】" + r.getMessage());
                }
                createAndSaveInterfaceLog(keyId, logOperation, d, logData, r, 1, 1, null);
            }
        }
        return d.getReconciliationNo();
    }

    /**
     * 大宗临购修改对账
     *
     * @param d
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationSTUpdate(MaterialReconciliationUpdateDTO d) {
        String keyId = d.getKeyId();
        MaterialReconciliation materialReconciliation = getById(d.getReconciliationId());
        if (materialReconciliation == null) return;
        if (materialReconciliation.getState() == 2 || materialReconciliation.getState() == 3) {
            return;
        }
        Integer createType = materialReconciliation.getCreateType();
        if (createType == 3) {
            if (d.getIsSubmit() == 1) {
                boolean bb = ThreadLocalUtil.getCurrentUser().getRoles().contains("物资对账提交权限");
                if (bb == false) {
                    throw new BusinessException("没有权限请联系管理员！");
                }
                materialReconciliation.setState(2);
                update(materialReconciliation);
                return;
            }
        }
        getCreateSTAmountByUpdate(d);
        BeanUtils.copyProperties(d, materialReconciliation);

        if (d.getIsSubmit() == 1) {
            boolean bb = ThreadLocalUtil.getCurrentUser().getRoles().contains("物资对账提交权限");
            if (bb == false) {
                throw new BusinessException("没有权限请联系管理员！");
            }
            materialReconciliation.setState(2);
        }
        update(materialReconciliation);
        //修改对账单，记录修改的差异
        List<Map<String, Object>> chaMap = getMaps(d, materialReconciliation);
        ArrayList<MaterialReconciliationDtl> dtl = new ArrayList<>();
        for (MaterialReconciliationDtl md : d.getDtl()) {
            isWarehouseIdExist(md);
            if (StringUtils.isBlank(md.getReconciliationDtlId())) {
                materialReconciliationDtlService.save(md);
                continue;
            }
            materialReconciliationDtlService.update(md);
            dtl.add(md);
        }
        //如果数据没有改变，不调用接口 //时间不足，以后修改
        if (createType != 3) {
            List<Map<String, Object>> uniqueMaps = getUpdateQuDTOM(d, materialReconciliation);
            List<Map<String, Object>> finalMaps = chaMap.size() != 0 ? chaMap : uniqueMaps;
            // 转换为 ReconciliationDtl 列表
            List<ReconciliationDtl> reconciliationDtls = new ArrayList<>();
            for (Map<String, Object> map : finalMaps) {
                ReconciliationDtl reconciliationDtl = ReconciliationDtl.builder().dtlId((String) map.get("dtlId")).quantity((BigDecimal) map.get("quantity")).amount((BigDecimal) map.get("amount")).build();
                reconciliationDtls.add(reconciliationDtl);
            }
            KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder().data(reconciliationDtls).keyId(keyId).orgId(materialReconciliation.getPurchasingOrgId()).build();
            PcwpRes<Void> r = null;
            try {
                r = pcwpService.writeBackBillLockQuantiy(payload);
            } catch (Exception e) {
                throw new BusinessException("【远程异常】" + e.getMessage());
            }
            if (r.getCode() == null || r.getCode() != 200) {
                throw new BusinessException("【远程异常】" + r.getMessage());
            }
            createAndSaveInterfaceLog(keyId, "materialReconciliationSTUpdate", d, payload, r, 1, 1, null);
        }
    }

    /**
     * 修改大宗临购（供应商）
     *
     * @param d
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationSTSupplierUpdate(MaterialReconciliationUpdateDTO d, int isNotPush) {
        MaterialReconciliation materialReconciliation = getById(d.getReconciliationId());
        if (materialReconciliation == null) return;
        if (materialReconciliation.getState() == 2 || materialReconciliation.getState() == 3) {
            return;
        }
        //修改对账单，记录修改的差异
//        List<Map<String, Object>> chaMap = getMaps(d, materialReconciliation);
        getCreateSTAmountByUpdate(d);
        BeanUtils.copyProperties(d, materialReconciliation);
        if (d.getIsSubmit() == 1) {
            materialReconciliation.setState(1);
        }
        update(materialReconciliation);
        ArrayList<MaterialReconciliationDtl> dtl = new ArrayList<>();
        for (MaterialReconciliationDtl md : d.getDtl()) {
            if (isNotPush == 1) {
                isWarehouseIdExist(md);
            }
            if (StringUtils.isBlank(md.getReconciliationDtlId())) {
                materialReconciliationDtlService.save(md);
            } else {
                materialReconciliationDtlService.update(md);
            }
        }
        if (isNotPush == 1) {//计划推送PCWP
            // 调用远程进行新增
            String keyId = d.getKeyId();
            List<Map<String, Object>> uniqueMaps = getUpdateQuDTOM(d, materialReconciliation);
            // 转换为 ReconciliationDtl 列表
            List<ReconciliationDtl> reconciliationDtls = new ArrayList<>();
            for (Map<String, Object> map : uniqueMaps) {
                ReconciliationDtl reconciliationDtl = ReconciliationDtl.builder().dtlId((String) map.get("dtlId")).quantity((BigDecimal) map.get("quantity")).amount((BigDecimal) map.get("amount")).build();
                reconciliationDtls.add(reconciliationDtl);
            }
            KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder().data(reconciliationDtls).keyId(keyId).orgId(materialReconciliation.getPurchasingOrgId()).build();
            PcwpRes<Void> r = null;
            try {
                r = pcwpService.writeBackBillLockQuantiy(payload);
            } catch (Exception e) {
                throw new BusinessException("【远程异常】" + e.getMessage());
            }
            if (r.getCode() == null || r.getCode() != 200) {
                throw new BusinessException("【远程异常】" + r.getMessage());
            }
            createAndSaveInterfaceLog(keyId, "materialReconciliationSTSupplierUpdate", d, payload, r, 1, 1, null);
        }
    }

    /**
     * 删除大宗临购
     *
     * @param reconciliationId
     * @param keyId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationSTDelete(String reconciliationId, String keyId, int isNotPush) {
        MaterialReconciliation materialReconciliation = getById(reconciliationId);
        Integer createType = materialReconciliation.getCreateType();
        String relevanceId = materialReconciliation.getRelevanceId();
        if (isNotPush == 1) {// 如果是pcwp新增的删除需要调用清除id
            if (createType == 3) {
                PcwpRes<Boolean> r = null;
                try { //pcwpid关系请求
                    if (materialReconciliation.getBusinessType() == 2 || materialReconciliation.getBusinessType() == 6) {
                        //零星采购或者大宗临购
                        r = pcwpService.clearRelationId(relevanceId, materialReconciliation.getPurchasingOrgId());
                    }
                } catch (Exception e) {
                    throw new BusinessException("【远程异常】" + e.getMessage());
                }
                if (r.getCode() == null || r.getCode() != 200) {
                    throw new BusinessException("【远程异常】" + r.getMessage());
                }
            } else if (createType == 1 || createType == 2) {
                // 先获取再删除
                List<MaterialReconciliationDtl> dtls = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).list();
                PcwpRes<Void> r = null;
                Object logData;
                if (materialReconciliation.getBusinessType() == 7) {//周转材料
                    // 构建周转材料数据
                    List<Map<String, Object>> uniqueMaps = buildWriteBackBillLockQuantityData(materialReconciliation, dtls, reconciliationId);
                    List<UpdatePlanDtl> updatePlanDtls = new ArrayList<>();
                    for (Map<String, Object> map : uniqueMaps) {
                        UpdatePlanDtl updatePlanDtl = UpdatePlanDtl.builder().dtlId((String) map.get("dtlId")).billId(materialReconciliation.getSourceBillId()).number((BigDecimal) map.get("quantity")).amount((BigDecimal) map.get("amount")).build();
                        updatePlanDtls.add(updatePlanDtl);
                    }
                    KeyedPayload<List<UpdatePlanDtl>> payload = KeyedPayload.<List<UpdatePlanDtl>>builder().data(updatePlanDtls).keyId(keyId).orgId(materialReconciliation.getPurchasingOrgId()).build();
                    r = pcwpService.updateRevolPlanDtl(payload);
                    logData = payload;
                } else {//零星采购或者大宗临购
                    // 构建反写对账单暂扣数量的数据
                    List<Map<String, Object>> uniqueMaps = buildWriteBackBillLockQuantityData(materialReconciliation, dtls, reconciliationId);
                    // 转换为 ReconciliationDtl 列表
                    List<ReconciliationDtl> reconciliationDtls = new ArrayList<>();
                    for (Map<String, Object> map : uniqueMaps) {
                        ReconciliationDtl reconciliationDtl = ReconciliationDtl.builder().dtlId((String) map.get("dtlId")).quantity((BigDecimal) map.get("quantity")).amount((BigDecimal) map.get("amount")).build();
                        reconciliationDtls.add(reconciliationDtl);
                    }
                    KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder().data(reconciliationDtls).keyId(keyId).orgId(materialReconciliation.getPurchasingOrgId()).build();
                    r = pcwpService.writeBackBillLockQuantiy(payload);
                    logData = payload;
                }
                if (r.getCode() == null || r.getCode() != 200) {
                    throw new BusinessException("【远程异常】" + r.getMessage());
                }
                createAndSaveInterfaceLog(keyId, "materialReconciliationSTDelete", reconciliationId, logData, r, 1, 1, null);
            }
        }
        // 删除
        delete(reconciliationId);
        // 删除
        materialReconciliationDtlService.lambdaUpdate().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).remove();
    }

    /**
     * 构建反写对账单暂扣数量的数据
     *
     * @param materialReconciliation 对账单
     * @param dtls                   对账单明细列表
     * @param reconciliationId       对账单ID
     * @return 构建的数据列表
     */
    private List<Map<String, Object>> buildWriteBackBillLockQuantityData(MaterialReconciliation materialReconciliation, List<MaterialReconciliationDtl> dtls, String reconciliationId) {
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        List<String> ids = lambdaQuery().eq(MaterialReconciliation::getSourceBillId, materialReconciliation.getSourceBillId()).eq(MaterialReconciliation::getBusinessType, materialReconciliation.getBusinessType()).in(MaterialReconciliation::getState, 0, 1, 2, 4).select(MaterialReconciliation::getReconciliationId).list().stream().map(MaterialReconciliation::getReconciliationId).collect(Collectors.toList());

        for (MaterialReconciliationDtl dtl1 : dtls) {
            HashMap<String, Object> map = new HashMap<>();
            List<MaterialReconciliationDtl> dtlQuList = materialReconciliationDtlService.lambdaQuery().in(!CollectionUtils.isEmpty(ids), MaterialReconciliationDtl::getReconciliationId, ids).eq(CollectionUtils.isEmpty(ids), MaterialReconciliationDtl::getReconciliationId, reconciliationId).eq(MaterialReconciliationDtl::getReceiptBillDtlId, dtl1.getReceiptBillDtlId()).select(MaterialReconciliationDtl::getQuantity, MaterialReconciliationDtl::getAcceptanceAmount).list();

            // 计算数量
            BigDecimal addQu = new BigDecimal(0);
            BigDecimal addAmount = new BigDecimal(0);
            for (MaterialReconciliationDtl materialReconciliationDtl : dtlQuList) {
                addQu = addQu.add(materialReconciliationDtl.getQuantity());
                addAmount = addAmount.add(materialReconciliationDtl.getAcceptanceAmount());
            }
            map.put("quantity", addQu);
            map.put("amount", addAmount);
            map.put("dtlId", dtl1.getReceiptBillDtlId());
            map.put("orgId", materialReconciliation.getPurchasingOrgId());
            maps.add(map);
        }

        // 根据属性判断重复，并删除重复的 Map 对象
        return removeDuplicateByProperty(maps, "dtlId");
    }

    /**
     * 大宗临购作废单据
     *
     * @param reconciliationId
     * @param result
     * @param keyId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationSTCancellation(String reconciliationId, String result, String keyId, int isNotPush) {
        MaterialReconciliation byId = lambdaQuery().eq(MaterialReconciliation::getReconciliationId, reconciliationId).eq(MaterialReconciliation::getState, 3).one();
        if (byId.getSettleAmount().compareTo(BigDecimal.ZERO) > 0) {
            throw new BusinessException("已结算不能作废操作！");
        }
        Boolean data;
        Integer createType = byId.getCreateType();
        if (isNotPush == 1) {//对账单是否推送PCWP
            if (createType == 1 || createType == 2) {
                try {// 判断是否可以作废
                    PcwpRes<Boolean> pcwpRes;
                    if (byId.getBusinessType() == 7) {//周转材料
                        pcwpRes = pcwpService.checkOutBillCanBeInvalidatedForRevol(byId.getRelevanceId(), byId.getPurchasingOrgId());
                    } else {//零星采购或者大宗临购
                        pcwpRes = pcwpService.checkOutBillCanBeInvalidated(byId.getRelevanceId(), byId.getPurchasingOrgId());
                    }
                    if (pcwpRes.getCode() == null || pcwpRes.getCode() != 200) {
                        throw new BusinessException("【远程异常】" + pcwpRes.getMessage());
                    }
                    data = pcwpRes.getData();
                } catch (Exception e) {
                    throw new BusinessException("【远程异常】" + e.getMessage());
                }
            } else {
                data = true;
            }
            if (data) {
                if (createType == 1 || createType == 2) {
                    // 调用接口
                    List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).list();
                    PcwpRes<Void> pcwpRes = null;

                    if (byId.getBusinessType() == 7) {//周转材料
                        // 已审核数量（取负值）
                        List<ReconciliationDtl> reconciliationDtls = convertToReconciliationDtlsWithNegate(list);
                        // 转换为 UpdatePlanDtl 列表
                        List<UpdatePlanDtl> updatePlanDtls = new ArrayList<>();
                        for (ReconciliationDtl reconciliationDtl : reconciliationDtls) {
                            UpdatePlanDtl updatePlanDtl = UpdatePlanDtl.builder()
                                    .dtlId(reconciliationDtl.getDtlId())
                                    .billId(byId.getSourceBillId())
                                    .number(reconciliationDtl.getQuantity())
                                    .amount(reconciliationDtl.getAmount())
                                    .build();
                            updatePlanDtls.add(updatePlanDtl);
                        }
                        KeyedPayload<List<UpdatePlanDtl>> payload = KeyedPayload.<List<UpdatePlanDtl>>builder()
                                .data(updatePlanDtls)
                                .keyId(keyId)
                                .orgId(byId.getPurchasingOrgId())
                                .build();
                        pcwpRes = pcwpService.updateRevolPlanDtl(payload);
                    } else if (byId.getBusinessType() == 2 || byId.getBusinessType() == 6) {//零星采购或者大宗临购
                        // 已审核数量（取负值）
                        List<ReconciliationDtl> reconciliationDtls = convertToReconciliationDtlsWithNegate(list);
                        // 构建KeyedPayload请求对象
                        KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder()
                                .data(reconciliationDtls)
                                .keyId(keyId)
                                .orgId(byId.getPurchasingOrgId())
                                .build();
                        pcwpRes = pcwpService.writeBackBillQuantiy(payload);
                    }
                    // 检查响应结果
                    if (pcwpRes != null && (pcwpRes.getCode() == null || pcwpRes.getCode() != 200)) {
                        throw new BusinessException("【远程异常】" + pcwpRes.getMessage());
                    }
                } else if (createType == 3) {
                    // 只有当 isNotPush == 1 时才调用 pcwpService 清除关系ID
                    try {
                        PcwpRes<Boolean> pcwpRes = null;
                        //清除pcwpid关系
                        if (byId.getBusinessType() == 2 || byId.getBusinessType() == 6) {//零星采购或者大宗临购
                            pcwpRes = pcwpService.clearRelationId(byId.getRelevanceId(), byId.getPurchasingOrgId());
                        }
                        if (pcwpRes != null && (pcwpRes.getCode() == null || pcwpRes.getCode() != 200)) {
                            throw new BusinessException("【远程异常】" + pcwpRes.getMessage());
                        }
                    } catch (Exception e) {
                        throw new BusinessException("【远程异常】" + e.getMessage());
                    }
                }
            }
            // 成功保存日志
            createAndSaveInterfaceLog(keyId, "materialReconciliationSTCancellation", reconciliationId, null, null, 1, 1, null);
        }
        MaterialReconciliation materialReconciliation = new MaterialReconciliation();
        materialReconciliation.setReconciliationId(reconciliationId);
        materialReconciliation.setState(7);
        materialReconciliation.setNullifyReason(result);
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        materialReconciliation.setNullifyCreatorLocalId(user.getUserId());
        materialReconciliation.setNullifyCreatorId(user.getFarUserId());
        materialReconciliation.setNullifyCreator(user.getUserName());
        materialReconciliation.setNullifyCreated(new Date());
        update(materialReconciliation);
    }

    /**
     * 审核大宗临购对账单
     *
     * @param dto
     * @param keyId
     * @param farArg
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer materialReconciliationSTAuditPlan(AuditDTO dto, String keyId, StringBuilder farArg) {
        String id = dto.getId();
        MaterialReconciliation byId = lambdaQuery().eq(MaterialReconciliation::getReconciliationId, id).one();
        Integer isNotPush = byId.getIsNotPush();
        Integer isOpen = dto.getIsOpen();
        if (isOpen == 1) {//审核通过
            createApprovalAuditRecord(id);
            byId.setState(2);
            lambdaUpdate().eq(MaterialReconciliation::getReconciliationId, id).set(MaterialReconciliation::getState, 3).update();
            // 如果是pcwp新增直接返回
            if (byId.getCreateType() == 3) {
                return null;
            }
            // 只有当 isNotPush == 1 时才调用 pcwpService
            if (isNotPush != null && isNotPush == 1) {
                //  判断是否能推送验收单并推送，然后反写数量
                log.warn("判断是否可推送验收请求参数：");
                log.warn("orgId：" + byId.getPurchasingOrgId());
                log.warn("判断是否可推送验收请求参数：orgId=" + byId.getPurchasingOrgId() + "&date=" + DateUtil.getymd(LocalDate.now()));

                if (byId.getBusinessType() == 2 || byId.getBusinessType() == 6) {//零星采购或者大宗临购
                    // 先判断是否可以推送验收单
                    Boolean r11Bool = null;
                    try {
                        PcwpRes<Boolean> pcwpRes = pcwpService.isCanOperaBill(byId.getPurchasingOrgId(), DateUtil.getymd(LocalDate.now()));
                        if (pcwpRes.getCode() == null || pcwpRes.getCode() != 200) {
                            throw new BusinessException("【远程异常】判断是否可推送验收单：" + pcwpRes.getMessage());
                        }
                        r11Bool = pcwpRes.getData();
                    } catch (Exception e) {
                        throw new BusinessException("【远程异常】判断是否可推送验收单：" + e.getMessage());
                    }

                    // 如果可以推送，则推送验收单
                    if (r11Bool != null && r11Bool == true) {
                        // 通过新增草稿
                        PcwpAcceptanceRequest.PcwpAcceptanceData data = new PcwpAcceptanceRequest.PcwpAcceptanceData();
                        List<PcwpAcceptanceRequest.PcwpAcceptanceDtl> dtls = new ArrayList<>();
                        BigDecimal acceptanceQuantity = new BigDecimal(0);
                        List<MaterialReconciliationDtl> countDtlList = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, id).list();
                        for (MaterialReconciliationDtl md : countDtlList) {
                            // 保存明细
                            BigDecimal quantity = md.getQuantity();
                            acceptanceQuantity = acceptanceQuantity.add(quantity);
                            PcwpAcceptanceRequest.PcwpAcceptanceDtl dtl = new PcwpAcceptanceRequest.PcwpAcceptanceDtl();
                            dtl.setAcceptanceAmount(md.getAcceptanceNoRateAmount());
                            dtl.setAcceptanceQuantity(quantity);
                            dtl.setTradeId(md.getTradeId());
                            dtl.setOrderId(md.getOrderId());
                            dtl.setOrderNo(md.getOrderSn());
                            dtl.setMaterialClassId(md.getMaterialClassId());
                            dtl.setMaterialClassName(md.getMaterialClassName());
                            dtl.setMaterialId(md.getMaterialId());
                            dtl.setTaxAmount(md.getTaxAmount());
                            dtl.setMaterialName(md.getMaterialName());
                            // 单价取均价
                            dtl.setPrice(md.getNoRatePrice());
                            dtl.setSpec(md.getSpec());
                            dtl.setTexture(md.getTexture());
                            dtl.setUnit(md.getUnit());
                            dtl.setWarehouseId(md.getWarehouseId());
                            dtl.setWarehouseName(md.getWarehouseName());
                            dtl.setNetworkPrice(TaxCalculator.calculateNotTarRateAmount(md.getFreightPrice(), byId.getTaxRate()));
                            dtl.setFixedFee(TaxCalculator.calculateNotTarRateAmount(md.getFixationPrice(), byId.getTaxRate()));
                            dtl.setFactoryPrice(TaxCalculator.calculateNotTarRateAmount(md.getOutFactoryPrice(), byId.getTaxRate()));
                            dtl.setFreight(TaxCalculator.calculateNotTarRateAmount(md.getTransportPrice(), byId.getTaxRate()));
                            dtls.add(dtl);
                        }

                        data.setType(byId.getType());
                        data.setDtls(dtls);
                        String s = DateUtil.getyyymmddHHmmssStr(byId.getGmtCreate());
                        data.setAcceptanceDate(s);
                        data.setAcceptanceQuantity(acceptanceQuantity);
                        data.setTotalAmount(byId.getReconciliationAmount());
                        data.setTaxAmount(byId.getReconciliationAmount().subtract(byId.getReconciliationNoRateAmount()));
                        data.setAcceptanceAmount(byId.getReconciliationNoRateAmount());
                        // 不管是供应商新增还是采购员新增单据统一使用采购员的id
                        if (byId.getCreateType() != 2) {
                            User userOne = userService.lambdaQuery().eq(User::getUserId, byId.getFounderId()).select(User::getInteriorId, User::getNickName).one();
                            data.setFounderId(userOne.getInteriorId());
                            data.setFounderName(userOne.getNickName());
                            data.setAcceptancerId(userOne.getInteriorId());
                            data.setAcceptancerName(userOne.getNickName());
                        } else {
                            data.setFounderId(byId.getPurchaserId());
                            data.setFounderName(byId.getPurchaserName());
                            data.setAcceptancerId(byId.getPurchaserId());
                            data.setAcceptancerName(byId.getPurchaserName());
                        }
                        data.setBusinessType(byId.getBusinessType());
                        data.setOrgId(byId.getPurchasingOrgId());
                        data.setOrgName(byId.getPurchasingOrgName());
                        data.setOuterId(byId.getReconciliationId());
                        data.setOuterNo(byId.getReconciliationNo());
                        data.setPurchaserId(byId.getPurchaserId());
                        data.setPurchaserName(byId.getPurchaserName());
                        data.setPurchasingUnitId(byId.getPurchasingOrgId());
                        data.setPurchasingUnitName(byId.getPurchasingOrgName());
                        data.setRemarks(byId.getRemarks());
                        data.setSourceBillId(byId.getSourceBillId());
                        data.setSourceBillNo(byId.getSourceBillNo());
                        data.setSourceBillName(byId.getSourceBillName());
                        data.setSupplierId(byId.getSupplierId());
                        data.setSupplierName(byId.getSupplierName());
                        data.setTaxRate(byId.getTaxRate());

                        PcwpAcceptanceRequest pcwpRequest = new PcwpAcceptanceRequest();
                        pcwpRequest.setData(data);
                        pcwpRequest.setKeyId(keyId);

                        farArg.append(JSON.toJSONString(pcwpRequest));
                        // 推送草稿验收单
                        PcwpRes<String> pcwpRes = null;
                        try {
                            pcwpRes = pcwpService.saveAcceptance(pcwpRequest);
                            if (pcwpRes.getCode() == null || pcwpRes.getCode() != 200) {
                                throw new BusinessException("【远程异常】" + pcwpRes.getMessage());
                            }
                        } catch (Exception e) {
                            throw new BusinessException("【远程异常】" + e.getMessage());
                        }
                        // 拿到验收id
                        String billId = pcwpRes.getData();
                        lambdaUpdate().eq(MaterialReconciliation::getReconciliationId, id)
                                .set(MaterialReconciliation::getRelevanceId, billId)
                                .set(MaterialReconciliation::getIsNotPush, 1).update();
                        // 成功保存日志
                        createAndSaveInterfaceLog(keyId, "materialReconciliationSTAuditPlan", dto, pcwpRequest, pcwpRes, 1, 1, null);
                    }

                    // 反写对账单暂扣数量
                    List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, id).list();
                    // 已审核数量
                    List<ReconciliationDtl> reconciliationDtls = convertToReconciliationDtls(list);
                    KeyedPayload<List<ReconciliationDtl>> payload = KeyedPayload.<List<ReconciliationDtl>>builder().data(reconciliationDtls).keyId(keyId).orgId(byId.getPurchasingOrgId()).build();
                    PcwpRes<Void> pcwpRes2 = null;
                    try {
                        pcwpRes2 = pcwpService.writeBackBillQuantiy(payload);
                        if (pcwpRes2.getCode() == null || pcwpRes2.getCode() != 200) {
                            throw new BusinessException("【远程异常】" + pcwpRes2.getMessage());
                        }
                    } catch (Exception e) {
                        throw new BusinessException("【远程异常】" + e.getMessage());
                    }
                    createAndSaveInterfaceLog(keyId, "materialReconciliationSTAuditPlan", dto, payload, pcwpRes2, 1, 1, null);
                } else if (byId.getBusinessType() == 7) {//周转材料
                    // 先判断是否可以推送验收单
                    Boolean r11Bool = null;
                    try {
                        PcwpRes<Boolean> pcwpRes = pcwpService.isRevolCanOperaBill(byId.getPurchasingOrgId(), DateUtil.getymd(LocalDate.now()));
                        if (pcwpRes.getCode() == null || pcwpRes.getCode() != 200) {
                            throw new BusinessException("【远程异常】判断是否可推送验收单：" + pcwpRes.getMessage());
                        }
                        r11Bool = pcwpRes.getData();
                    } catch (Exception e) {
                        throw new BusinessException("【远程异常】判断是否可推送验收单：" + e.getMessage());
                    }

                    // 如果可以推送，则推送周转材料验收单
                    if (r11Bool != null && r11Bool == true) {
                        // 构建周转材料验收单数据
                        PcwpRevolAcceptanceDto acceptanceDto = new PcwpRevolAcceptanceDto();
                        List<PcwpRevolAcceptanceDto.AcceptanceDtl> acceptanceDtls = new ArrayList<>();
                        BigDecimal totalAcceptanceAmount = new BigDecimal(0);

                        List<MaterialReconciliationDtl> countDtlList = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, id).list();
                        for (MaterialReconciliationDtl md : countDtlList) {
                            // 保存明细
                            BigDecimal quantity = md.getQuantity();
                            totalAcceptanceAmount = totalAcceptanceAmount.add(md.getAcceptanceNoRateAmount());

                            PcwpRevolAcceptanceDto.AcceptanceDtl dtl = PcwpRevolAcceptanceDto.AcceptanceDtl.builder()
                                    .acceptanceAmount(md.getAcceptanceNoRateAmount())
                                    .acceptanceQuantity(quantity)
                                    .materialClassId(md.getMaterialClassId())
                                    .materialClassName(md.getMaterialClassName())
                                    .materialId(md.getMaterialId())
                                    .materialName(md.getMaterialName())
                                    .price(md.getNoRatePrice())
                                    .sourceDtlId(md.getReceiptBillDtlId())
                                    .sourceAmount(md.getAcceptanceNoRateAmount())
                                    .sourceQuantity(quantity)
                                    .spec(md.getSpec())
                                    .taxAmount(md.getTaxAmount())
                                    .texture(md.getTexture())
                                    .unit(md.getUnit())
                                    .warehouseId(md.getWarehouseId())
                                    .warehouseName(md.getWarehouseName())
                                    .build();
                            acceptanceDtls.add(dtl);
                        }

                        // 设置验收单主要信息
                        acceptanceDto.setAcceptanceDtls(acceptanceDtls);
                        acceptanceDto.setAcceptanceAmount(totalAcceptanceAmount);
                        acceptanceDto.setAcceptanceDate(DateUtil.getyyymmddHHmmssStr(byId.getGmtCreate()));
                        acceptanceDto.setBillId(byId.getReconciliationId());
                        acceptanceDto.setBillNo(byId.getReconciliationNo());
                        acceptanceDto.setBillSource(1);
                        acceptanceDto.setBusinessTypeKey(String.valueOf(byId.getBusinessType()));
                        acceptanceDto.setBusinessTypeValue("周转材料");
                        acceptanceDto.setPurchasingUnitId(byId.getPurchasingOrgId());
                        acceptanceDto.setPurchasingUnitName(byId.getPurchasingOrgName());
                        acceptanceDto.setRemarks(byId.getRemarks());
                        acceptanceDto.setRmbAmount(byId.getReconciliationAmount());
                        acceptanceDto.setSourceId(byId.getSourceBillId());
                        acceptanceDto.setSourceNo(byId.getSourceBillNo());
                        acceptanceDto.setState(1);
                        acceptanceDto.setSupplierId(byId.getSupplierId());
                        acceptanceDto.setSupplierName(byId.getSupplierName());
                        acceptanceDto.setTaxAmount(byId.getReconciliationAmount().subtract(byId.getReconciliationNoRateAmount()));
                        acceptanceDto.setTaxRate(byId.getTaxRate());
                        acceptanceDto.setTotalPriceAndTax(byId.getReconciliationAmount());

                        // 设置验收人和采购人信息
                        if (byId.getCreateType() != 2) {
                            User userOne = userService.lambdaQuery().eq(User::getUserId, byId.getFounderId()).select(User::getInteriorId, User::getNickName).one();
                            acceptanceDto.setAcceptancerId(userOne.getInteriorId());
                            acceptanceDto.setAcceptancerName(userOne.getNickName());
                            acceptanceDto.setPurchaserId(userOne.getInteriorId());
                            acceptanceDto.setPurchaserName(userOne.getNickName());
                        } else {
                            acceptanceDto.setAcceptancerId(byId.getPurchaserId());
                            acceptanceDto.setAcceptancerName(byId.getPurchaserName());
                            acceptanceDto.setPurchaserId(byId.getPurchaserId());
                            acceptanceDto.setPurchaserName(byId.getPurchaserName());
                        }

                        // 构建请求对象
                        KeyedPayload<PcwpRevolAcceptanceDto> request = KeyedPayload.<PcwpRevolAcceptanceDto>builder()
                                .data(acceptanceDto)
                                .keyId(keyId)
                                .orgId(byId.getPurchasingOrgId())
                                .build();

                        farArg.append(JSON.toJSONString(request));
                        // 推送周转材料验收单
                        PcwpRes<String> pcwpRes = null;
                        try {
                            pcwpRes = pcwpService.saveAcceptanceForRevol(request);
                            if (pcwpRes.getCode() == null || pcwpRes.getCode() != 200) {
                                throw new BusinessException("【远程异常】" + pcwpRes.getMessage());
                            }
                        } catch (Exception e) {
                            throw new BusinessException("【远程异常】" + e.getMessage());
                        }
                        // 周转材料验收单推送成功，使用keyId作为关联ID
                        String billId = pcwpRes.getData();
                        if (StringUtils.isBlank(billId)) {
                            throw new BusinessException("验收关联id不存在！");
                        }
                        // 更新对账单状态
                        lambdaUpdate().eq(MaterialReconciliation::getReconciliationId, id)
                                .set(MaterialReconciliation::getRelevanceId, billId)
                                .set(MaterialReconciliation::getIsNotPush, 1)
                                .update();
                        // 成功保存日志
                        createAndSaveInterfaceLog(keyId, "materialReconciliationSTAuditPlan", dto, request, pcwpRes, 1, 1, null);
                    }

                    // 反写周转材料计划数量
                    List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, id).list();
                    // 构建周转材料更新计划数据
                    List<UpdatePlanDtl> updatePlanDtls = new ArrayList<>();
                    for (MaterialReconciliationDtl dtl : list) {
                        UpdatePlanDtl updatePlanDtl = UpdatePlanDtl.builder()
                                .dtlId(dtl.getReceiptBillDtlId())
                                .billId(byId.getSourceBillId())
                                .number(dtl.getQuantity())
                                .amount(dtl.getAcceptanceAmount())
                                .build();
                        updatePlanDtls.add(updatePlanDtl);
                    }

                    KeyedPayload<List<UpdatePlanDtl>> payload = KeyedPayload.<List<UpdatePlanDtl>>builder()
                            .data(updatePlanDtls)
                            .keyId(keyId)
                            .orgId(byId.getPurchasingOrgId())
                            .build();

                    PcwpRes<Void> pcwpRes2 = null;
                    try {
                        pcwpRes2 = pcwpService.updateRevolPlanDtl(payload);
                        if (pcwpRes2.getCode() == null || pcwpRes2.getCode() != 200) {
                            throw new BusinessException("【远程异常】" + pcwpRes2.getMessage());
                        }
                    } catch (Exception e) {
                        throw new BusinessException("【远程异常】" + e.getMessage());
                    }
                    createAndSaveInterfaceLog(keyId, "materialReconciliationSTAuditPlan", dto, payload, pcwpRes2, 1, 1, null);
                }

            }
        } else if (isOpen == 0) {//审核未通过
            createRejectionAuditRecord(id, dto.getAuditResult());
            lambdaUpdate().eq(MaterialReconciliation::getReconciliationId, id).set(MaterialReconciliation::getState, 4).update();
        }
        return isNotPush;
    }

    /**
     * 大宗临购主动推送对账单
     *
     * @param reconciliationId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationSTPushAcceptance(String reconciliationId) {
        MaterialReconciliation byId = getById(reconciliationId);
        Boolean r11Bool = null;
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String dateString = currentDate.format(formatter);
        //  判断是否能推送验收单
        PcwpRes<Boolean> r11 = null;
        try {
            r11 = pcwpService.isCanOperaBill(byId.getPurchasingOrgId(), DateUtil.getymd(LocalDate.now()));
        } catch (Exception e) {
            throw new BusinessException("【远程异常】判断是否可推送验收单：" + e.getMessage());
        }
        if (r11.getCode() == null || r11.getCode() != 200) {
            throw new BusinessException("【远程异常】判断是否可推送验收单：" + r11.getMessage());
        } else {
            r11Bool = (Boolean) r11.getData();
        }
        log.warn("判断是否可推送验收返回：" + r11);
        if (r11Bool == null || r11Bool == false) {
            throw new BusinessException("当前为pcwp月结处理中，暂时无法推送验收单！");
        }
        HashMap<String, Object> dataMap = new HashMap<>();
        HashMap<String, Object> data = new HashMap<>();
        ArrayList<Map> dtls = new ArrayList<>();
        BigDecimal acceptanceQuantity = new BigDecimal(0);

        List<MaterialReconciliationDtl> countDtlList = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).list();
        for (MaterialReconciliationDtl md : countDtlList) {
            // 保存明细
            BigDecimal acceptanceNoRateAmount = md.getAcceptanceNoRateAmount();
            BigDecimal quantity = md.getQuantity();
            acceptanceQuantity = acceptanceQuantity.add(quantity);
            HashMap<String, Object> dtl = new HashMap<>();
            dtl.put("acceptanceAmount", acceptanceNoRateAmount);
            dtl.put("tradeId", md.getTradeId());
            dtl.put("orderId", md.getOrderId());
            dtl.put("orderNo", md.getOrderSn());
            dtl.put("acceptanceQuantity", quantity);
            dtl.put("materialClassId", md.getMaterialClassId());
            dtl.put("materialClassName", md.getMaterialClassName());
            dtl.put("materialId", md.getMaterialId());
            dtl.put("materialName", md.getMaterialName());
            // 单价取均价
            dtl.put("price", md.getNoRatePrice());
            dtl.put("spec", md.getSpec());
            dtl.put("texture", md.getTexture());
            dtl.put("unit", md.getUnit());
            dtl.put("warehouseId", md.getWarehouseId());
            dtl.put("warehouseName", md.getWarehouseName());
            dtl.put("networkPrice", TaxCalculator.calculateNotTarRateAmount(md.getFreightPrice(), byId.getTaxRate()));
            dtl.put("fixedFee", TaxCalculator.calculateNotTarRateAmount(md.getFixationPrice(), byId.getTaxRate()));
            dtl.put("factoryPrice", TaxCalculator.calculateNotTarRateAmount(md.getOutFactoryPrice(), byId.getTaxRate()));
            dtl.put("freight", TaxCalculator.calculateNotTarRateAmount(md.getTransportPrice(), byId.getTaxRate()));
            dtls.add(dtl);
        }
        data.put("type", byId.getType());
        data.put("dtls", dtls);
        String s = DateUtil.getyyymmddHHmmssStr(byId.getGmtCreate());
        ;
        data.put("acceptanceDate", s);
        data.put("acceptanceQuantity", acceptanceQuantity);
        data.put("totalAmount", byId.getReconciliationAmount());
        data.put("taxAmount", byId.getReconciliationAmount().subtract(byId.getReconciliationNoRateAmount()));
        data.put("acceptanceAmount", byId.getReconciliationNoRateAmount());
        // 不管是供应商新增还是采购员新增单据统一使用采购员的id
        if (byId.getCreateType() != 2) {
            User userOne = userService.lambdaQuery().eq(User::getUserId, byId.getFounderId()).select(User::getInteriorId, User::getNickName).one();
            data.put("founderId", userOne.getInteriorId());
            data.put("founderName", userOne.getNickName());
            data.put("acceptancerId", userOne.getInteriorId());
            data.put("acceptancerName", userOne.getNickName());
        } else {
            data.put("founderId", byId.getPurchaserId());
            data.put("founderName", byId.getPurchaserName());
            data.put("acceptancerId", byId.getPurchaserId());
            data.put("acceptancerName", byId.getPurchaserName());
        }
        data.put("businessType", byId.getBusinessType());
        data.put("orgId", byId.getPurchasingOrgId());
        data.put("orgName", byId.getPurchasingOrgName());
        data.put("outerId", byId.getReconciliationId());
        data.put("outerNo", byId.getReconciliationNo());
        data.put("purchaserId", byId.getPurchaserId());
        data.put("purchaserName", byId.getPurchaserName());
        data.put("purchasingUnitId", byId.getPurchasingOrgId());
        data.put("purchasingUnitName", byId.getPurchasingOrgName());
        data.put("remarks", byId.getRemarks());
        data.put("sourceBillId", byId.getSourceBillId());
        data.put("sourceBillNo", byId.getSourceBillNo());
        data.put("sourceBillName", byId.getSourceBillName());
        data.put("supplierId", byId.getSupplierId());
        data.put("supplierName", byId.getSupplierName());
        data.put("taxRate", byId.getTaxRate());
        dataMap.put("data", data);
        String keyId = IdWorker.getIdStr();
        dataMap.put("keyId", keyId);
        R r = null;
        try {
            r = restTemplateUtils.postPCWP2(mallConfig.prodPcwp2Url02 + SAVE_ACCEPTANCE_URL, dataMap);
        } catch (Exception e) {
            throw new BusinessException("【远程异常】" + e.getMessage());
        }
        if (r.getCode() == null || r.getCode() != 200) {
            throw new BusinessException("【远程异常】" + r.getMessage());
        }
        // 拿到验收id
        String billId = (String) r.getData();
        if (StringUtils.isBlank(billId)) {
            throw new BusinessException("验收id不存在！");
        }
        lambdaUpdate().eq(MaterialReconciliation::getReconciliationId, reconciliationId).set(MaterialReconciliation::getRelevanceId, billId).update();
    }

    /**
     * 导出大宗临购对账账单
     *
     * @param reconciliationId
     * @param response
     */
    @Override
    public void outputSTExcel(String reconciliationId, HttpServletResponse response) {
        MaterialReconciliation materialReconciliation = getById(reconciliationId);
        List<MaterialReconciliationDtl> list = materialReconciliationDtlService.lambdaQuery().eq(MaterialReconciliationDtl::getReconciliationId, reconciliationId).orderByDesc(MaterialReconciliationDtl::getOrderSn, MaterialReconciliationDtl::getQuantity, MaterialReconciliationDtl::getMaterialName, MaterialReconciliationDtl::getAcceptanceAmount).list();
        if (materialReconciliation.getBusinessType() == 6) {
            for (MaterialReconciliationDtl item : list) {
                OrderItem one1 = orderItemService.lambdaQuery().eq(OrderItem::getOrderItemId, item.getOrderDtlId()).select(OrderItem::getProductName).one();
                if (one1 != null) {
                    item.setMaterialName(one1.getProductName());
                }
            }
        }
        materialReconciliation.setDtl(list);
        // 处理date
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        String startTimeStr = dateFormat.format(materialReconciliation.getStartTime());
        String endTimeStr = dateFormat.format(materialReconciliation.getEndTime());
        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(materialReconciliation);

        BigDecimal totalQu = new BigDecimal(0);
        BigDecimal totalAmount = new BigDecimal(0);
        BigDecimal noRateTotalAmount = new BigDecimal(0);
        for (MaterialReconciliationDtl materialReconciliationDtl : list) {
            totalQu = totalQu.add(materialReconciliationDtl.getQuantity());
            totalAmount = totalAmount.add(materialReconciliationDtl.getAcceptanceAmount());
            noRateTotalAmount = noRateTotalAmount.add(materialReconciliationDtl.getAcceptanceNoRateAmount());
        }
        stringObjectMap.put("startTimeStr", startTimeStr);
        stringObjectMap.put("endTimeStr", endTimeStr);
        stringObjectMap.put("totalQuantity", totalQu);
        stringObjectMap.put("totalAmount", totalAmount);
//        BigDecimal noRateTotalAmount1 = TaxCalculator.calculateNotTarRateAmount(totalAmount, materialReconciliation.getTaxRate());
        stringObjectMap.put("noRateTotalAmount", noRateTotalAmount);
        String src = mallConfig.templateFormUrl;

        if (materialReconciliation.getType() == 1) {
            // 浮动价格
            try {
                ExcelForWebUtil.exportExcel(response, stringObjectMap, "浮动价格大宗临购对账单模板.xlsx", src, "浮动价格大宗临购对账单.xlsx");
            } catch (Exception e) {
                log.error("导出失败信息：" + e.getMessage());
                throw new BusinessException("导出失败！");
            }
        }
        if (materialReconciliation.getType() == 2) {
            // 固定价格
            try {
                ExcelForWebUtil.exportExcel(response, stringObjectMap, "固定价格大宗临购对账单模板.xlsx", src, "固定价格大宗临购对账单.xlsx");
            } catch (Exception e) {
                log.error("导出失败信息：" + e.getMessage());
                throw new BusinessException("导出失败！");
            }
        }
    }

    /**
     * 批量提交大宗临购审核
     *
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void materialReconciliationSTSubmitCheck(List<String> ids) {
        List<MaterialReconciliation> list = lambdaQuery().in(MaterialReconciliation::getReconciliationId, ids).select(MaterialReconciliation::getReconciliationId, MaterialReconciliation::getState).list();
        if (!CollectionUtils.isEmpty(list)) {
            for (MaterialReconciliation materialReconciliation : list) {
                Integer state = materialReconciliation.getState();
                if (state == 0 || state == 1 || state == 4) {
                    // 推送TT代
                    ttRabbitMQConnectionTest.testConnection();
//                    String EMPLOYEE_NUMBER = "036529";
//                    String USER_ID = "391E2FB8-F295-4045-8CDC-340AD3DE6700";
//                    // 1. 创建待办消息体
//                    ToDoMessageBody todo = TTToDoRabbitMQUtil.createTodoBody(
//                            "对账单编号"+materialReconciliation.getReconciliationNo(), // 待办唯一ID
//                            EMPLOYEE_NUMBER,                                 // 员工号：036529
//                            USER_ID,                                        // TT用户ID
//                            "大宗临购对账单审核",                               // 标题
//                            "对账单审核",                     // 描述信息
//                            ""                              // 跳转URL
//                    );
//
//                    // 2. 推送TT代办
//                    TTToDoRabbitMQUtil.sendSingleToDo(todo);
                    materialReconciliation.setState(2);
                    update(materialReconciliation);
                }
            }
        }
    }

    /**
     * 创建并保存接口日志
     *
     * @param secretKey      秘钥唯一key
     * @param methodName     方法名
     * @param localArguments 本地方法请求参数
     * @param farArguments   请求远程接口参数
     * @param result         返回结果
     * @param isSuccess      是否成功（0否1是）
     * @param logType        日志类型（1请求远程2请求远程回滚3请求本地4请求本地回滚）
     * @param updateBefore   修改前的数据
     */
    private void createAndSaveInterfaceLog(String secretKey, String methodName, Object localArguments, Object farArguments, Object result, Integer isSuccess, Integer logType, Object updateBefore) {
        createAndSaveInterfaceLog(secretKey, methodName, localArguments, farArguments, result, isSuccess, logType, updateBefore, null);
    }

    /**
     * 创建并保存接口日志（带业务标识）
     *
     * @param secretKey      秘钥唯一key
     * @param methodName     方法名
     * @param localArguments 本地方法请求参数
     * @param farArguments   请求远程接口参数
     * @param result         返回结果
     * @param isSuccess      是否成功（0否1是）
     * @param logType        日志类型（1请求远程2请求远程回滚3请求本地4请求本地回滚）
     * @param updateBefore   修改前的数据
     * @param businessFlag   业务标识
     */
    private void createAndSaveInterfaceLog(String secretKey, String methodName, Object localArguments, Object farArguments, Object result, Integer isSuccess, Integer logType, Object updateBefore, String businessFlag) {
        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(secretKey);
        iLog.setClassPackage(MaterialReconciliationServiceImpl.class.getName());
        iLog.setMethodName(methodName);

        if (localArguments != null) {
            iLog.setLocalArguments(localArguments instanceof String ? (String) localArguments : JSON.toJSONString(localArguments));
        }
        if (farArguments != null) {
            iLog.setFarArguments(farArguments instanceof String ? (String) farArguments : JSON.toJSONString(farArguments));
        }
        if (result != null) {
            iLog.setResult(result instanceof String ? (String) result : JSON.toJSONString(result));
        }
        if (updateBefore != null) {
            iLog.setUpdateBefore(updateBefore instanceof String ? (String) updateBefore : JSON.toJSONString(updateBefore));
        }
        if (businessFlag != null) {
            iLog.setBusinessFlag(businessFlag);
        }
        iLog.setIsSuccess(isSuccess);
        iLog.setLogType(logType);
        interfaceLogsService.create(iLog);
    }

    /**
     * 创建审核通过记录
     *
     * @param relevanceId 关联ID
     */
    private void createApprovalAuditRecord(String relevanceId) {
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setRelevanceType(5);
        auditRecord.setRelevanceId(relevanceId);
        auditRecord.setResultType(1);
        auditRecord.setAuditType(1);
        auditRecord.setAuditResult("【同意】");
        auditRecordService.create(auditRecord);
    }

    /**
     * 创建审核拒绝记录
     *
     * @param relevanceId 关联ID
     * @param auditResult 拒绝原因
     */
    private void createRejectionAuditRecord(String relevanceId, String auditResult) {
        AuditRecord auditRecord = new AuditRecord();
        auditRecord.setRelevanceType(5);
        auditRecord.setRelevanceId(relevanceId);
        auditRecord.setResultType(2);
        auditRecord.setAuditType(1);
        auditRecord.setAuditResult("【拒绝】" + auditResult);
        auditRecordService.create(auditRecord);
    }

    @Override
    public PageUtils materialReconciliationLedgerSupplierBill(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper) {
        // 通过订单id关联shopId
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String shopId = (String) jsonObject.getInnerMap().get("shopId");
        if (StringUtils.isNotEmpty(shopId)) {
            //materialReconciliationLambdaQueryWrapper.eq("o.shop_id", shopId);
            jsonObject.getInnerMap().put("shopId", shopId);
        } else {
            //materialReconciliationLambdaQueryWrapper.eq("o.shop_id", user.getShopId());
            jsonObject.getInnerMap().put("shopId", user.getShopId());
        }
        int count = baseMapper.listLedgerCount(jsonObject.getInnerMap());
        pageUtils.pageDispose(jsonObject, count);
        Page<ReconciliationLedgerListVo> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ReconciliationLedgerListVo> vos = baseMapper.ledgerList(pages, jsonObject);
        BigDecimal CountAmount = baseMapper.selCountAmount(jsonObject);
        BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(jsonObject);
        if (vos.size() > 0) {
            vos.get(0).setCountAmount(CountAmount);
            vos.get(0).setCountNoRateAmount(countNoRateAmount);
        }
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    @Override
    public void materialReconciliationLedgerSupplierBillExcel(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper, HttpServletResponse response) {
        // 通过订单id关联shopId
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String shopId = (String) jsonObject.getInnerMap().get("shopId");
        if (StringUtils.isNotEmpty(shopId)) {
            //materialReconciliationLambdaQueryWrapper.eq("o.shop_id", shopId);
            jsonObject.getInnerMap().put("shopId", shopId);
        } else {
            //materialReconciliationLambdaQueryWrapper.eq("o.shop_id", user.getShopId());
            jsonObject.getInnerMap().put("shopId", user.getShopId());
        }
        List<ReconciliationLedgerListVo> vos = baseMapper.ledgerList(jsonObject);
        for (ReconciliationLedgerListVo vo : vos) {
            vo.setReconciliationTypeStr(getProductTypeName(Integer.parseInt(vo.getReconciliationProductType())));
            vo.setStartTimeStr(getDateStr(vo.getStartTime()));
            vo.setEndTimeStr(getDateStr(vo.getEndTime()));
            vo.setStateStr(getStateName(vo.getState()));
        }
        BigDecimal CountAmount = baseMapper.selCountAmount(jsonObject);
        BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(jsonObject);
        if (vos.size() > 0) {
            vos.get(0).setCountAmount(CountAmount);
            vos.get(0).setCountNoRateAmount(countNoRateAmount);
        }
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", vos);
        dataMap.put("countAmount", vos.get(0).getCountAmount());
        dataMap.put("countNoRateAmount", vos.get(0).getCountNoRateAmount());
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "供应商物资对账统计台账模板.xlsx", src, "供应商物资对账统计台账.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }

    private String getProductTypeName(Integer productType) {
        if (productType == null) {
            return "未知类型";
        }
        switch (productType) {
            case 0:
                return "零星采购";
            case 1:
                return "大宗临购";
            case 2:
                return "周材材料";
            default:
                return "未知类型";
        }
    }

    private String getDateStr(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    private String getStateName(Integer state) {
        if (state == null) {
            return "未知状态";
        }
        switch (state) {
            case 0:
                return "买方已审核";
            case 1:
                return "卖方已审核";
            case 2:
                return "对账已完成";
            case 3:
                return "对账已作废";
            default:
                return "未知状态";
        }
    }

    private String getNoRate(BigDecimal amount, BigDecimal taxRate) {
        if (amount == null || taxRate == null) {
            return null;
        } else {
            // 将税率百分比转换为小数形式（3 -> 0.03）
            BigDecimal taxRateDecimal = taxRate.divide(BigDecimal.valueOf(100), 6, BigDecimal.ROUND_HALF_UP);
            // 计算不含税金额：含税金额 / (1 + 税率)
            BigDecimal divisor = BigDecimal.ONE.add(taxRateDecimal);
            BigDecimal noRateAmount = amount.divide(divisor, 2, BigDecimal.ROUND_HALF_UP);
            return String.valueOf(noRateAmount);
        }
    }
}
