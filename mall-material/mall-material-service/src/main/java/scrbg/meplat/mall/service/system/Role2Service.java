package scrbg.meplat.mall.service.system;

import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.dto.system.SysRole2DTO;
import scrbg.meplat.mall.entity.system.SysJg;
import scrbg.meplat.mall.entity.system.SysRole2;

import java.util.List;

public interface Role2Service {
    PageUtils<SysRole2> getRoleList(SysRole2DTO role);
    boolean addRole(SysRole2DTO role);
    boolean updateRole(SysRole2DTO role);
    boolean deleteRole(String roleId);
    SysRole2 getRoleDetail(String roleId);
    boolean setRoleMenus(String roleId, List<String> menuIds);
    List<SysJg> getAllJglist();
    List<SysRole2> getRoleListAll(SysRole2DTO role);
}
