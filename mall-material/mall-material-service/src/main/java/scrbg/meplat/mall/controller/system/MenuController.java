package scrbg.meplat.mall.controller.system;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.dto.system.SysMenu2DTO;
import scrbg.meplat.mall.entity.system.SysMenu2;
import scrbg.meplat.mall.entity.system.SysSyslist;
import scrbg.meplat.mall.service.system.MenuService;
import scrbg.meplat.mall.service.system.SysSyslistService;
import scrbg.meplat.mall.util.DateUtil;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/bgmanage/system/menu")
public class MenuController {
    @Autowired
    private SysSyslistService sysSyslistService;

    @Autowired
    private MenuService menuService;


    @GetMapping("/syslist")
    public List<SysSyslist> getSysSyslistNames() {
        return sysSyslistService.getAllSysSyslist();
    }

    @GetMapping("/tree")
    public R<List<SysMenu2>> getMenuTree(@RequestBody(required=false) Map<String, Object> params) {
        return R.success(menuService.getMenuTree(params));
    }

    /**
     * 分页查询菜单列表
     *
     * @param params 查询参数（包含分页参数和筛选条件）
     * @return 分页菜单数据
     *
     */
    @PostMapping("/list")
    public R<PageUtils<SysMenu2>> getMenuList(@RequestBody Map<String, Object> params) {
        return R.success(menuService.getMenuList(params));
    }

    /**
     * 新增菜单
     *
     * @param menu 菜单实体
     * @return 操作结果
     *
     */
    @PostMapping("/add")
    public R addMenu(@RequestBody SysMenu2DTO menu) {
        return menuService.addMenu(menu) ? R.success() : R.failed("新增菜单失败");
    }

    /**
     * 新增下级菜单
     *
     * @param menu 子菜单实体（需包含parentMenuId）
     * @return 操作结果
     *
     */
    @PostMapping("/addChild")
    public R addChildMenu(@RequestBody SysMenu2DTO menu) {
        return R.failed("新增菜单失败");
    }

    /**
     * 更新菜单信息
     *
     * @param menu 菜单实体（需包含menuId）
     * @return 操作结果
     *
     */
    @PostMapping("/update")
    public R updateMenu(@RequestBody SysMenu2DTO menu) {
        return menuService.updateMenu(menu) ? R.success() : R.failed("更新菜单失败");
    }

    /**
     * 删除菜单
     *
     * @param menuId 菜单ID
     * @return 操作结果
     *
     */
    @PostMapping("/delete/{menuId}")
    public R deleteMenu(@PathVariable String menuId) {
        UserLogin userLogin = ThreadLocalUtil.getCurrentUser();
        SysMenu2DTO menu = new SysMenu2DTO();
        menu.setGmtModified(DateUtil.getyyyymmddHHmmss(new Date()));
        menu.setModifyId(userLogin.getUserId());
        menu.setModifyName(userLogin.getUserName());
        menu.setMenuId(menuId);
        return menuService.deleteMenu(menu) ? R.success() : R.failed("删除菜单失败");
    }

    /**
     * 获取菜单详情
     *
     * @param menuId 菜单ID
     * @return 菜单详情
     */
    @GetMapping("/detail/{menuId}")
    public R<SysMenu2> getMenuDetail(@PathVariable String menuId) {
        return R.success(menuService.getMenuDetail(menuId));
    }

    /**
     * 检查菜单编码是否已存在
     *
     * @param code 菜单编码
     * @param menuId 当前菜单ID（用于排除自身）
     * @return 是否存在（true-已存在 false-不存在）
     */
    @GetMapping("/checkCode")
    public R<Boolean> checkCodeExists(
            @RequestParam String code,
            @RequestParam(required = false) String menuId) {
        return R.success(menuService.checkCodeExists(code, menuId));
    }

}
