package scrbg.meplat.mall.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;

import scrbg.meplat.mall.dto.plan.PlanFileDto;
import scrbg.meplat.mall.dto.product.material.lcProduct.ProductCardZone;
import scrbg.meplat.mall.entity.ShoppingCart;
import scrbg.meplat.mall.vo.product.website.WCartInfoVO;
import scrbg.meplat.mall.vo.user.userCenter.IsSynthesizeTemporaryVO;

/**
 * @描述：购物车 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface ShoppingCartService extends IService<ShoppingCart> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ShoppingCart> queryWrapper);

    void create(ShoppingCart shoppingCart);

    void update(ShoppingCart shoppingCart);

    ShoppingCart getById(String id);

    void delete(String id);


    /**
     * 清空购物车
     * @param productType
     */
    void emptyCart(Integer productType);

    /**
     * 修改购物车数量
     * @param id
     * @param changeNum
     */
    void changeCartNum(String id, BigDecimal changeNum);

    /**
     * 添加购物车
     * @param productId
     * @param cartNum
     */
    void addCart(String productId, BigDecimal cartNum, String zoneId, String zoneAddr, Integer paymentPeriod,String regionPriceId);

    /**
     * 根据商品类型获取购物车列表
     * @param productType
     */
    List<WCartInfoVO> listCart(Integer productType);

    /**
     * 主键集合批量真实删除
     * @param ids
     */
    void removeRealByIds(List<String> ids);

    /**
     * 根据店铺id删除所有购物车
     * @param shopId
     */
    void removeRealByShopId(String shopId);

    /**
     * 获取当前用户的购物车数量
     * @return
     */
    int getCartNum();

    /**
     * 修改购物车租赁时长
     * @param id
     * @param changeNum
     */
    void leaseCountCart(String id, BigDecimal changeNum);

    /**
     * 推送商品到计划
     *
     * @param cardId
     * @param farArg
     */
    void pushCardProductsArrive(List<String> cardId, String idStr, StringBuilder farArg);

    /**
     * 推送计划
     * @param planDto
     */
    void pushPlan(PlanFileDto planDto);

    /**
     * 检查推送计划情况
     *
     * @param map
     * @return
     */
    Map checkSubmitPlanProductCondition(Map map);


    /**   PCWP1
     * 推送商品到计划回滚
     * @param idStr
     */
    void pushCardProductsArriveRollBack(String idStr);

    /**
     * 修改购物车选中状态
     * @param id
     * @param checked
     */
    void updateChecked(String id, Integer checked);

    /**
     * 检查购物车商品是否可创建大宗临购
     *
     * @param ids
     * @return
     */
    IsSynthesizeTemporaryVO isSynthesizeTemporary(List<String> ids);



    void addCartZone(ProductCardZone productCardZone);

    BigDecimal getSellPrice(ShoppingCart cart);
}
