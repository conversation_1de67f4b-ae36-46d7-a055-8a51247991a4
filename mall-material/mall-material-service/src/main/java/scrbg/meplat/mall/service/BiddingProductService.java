package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.BiddingProduct;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.BiddingProduct;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * @描述：竞价商品信息 服务类
 * @作者: ye
 * @日期: 2023-07-11
 */
public interface BiddingProductService extends IService<BiddingProduct> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingProduct> queryWrapper);

        void create(BiddingProduct biddingProduct);

        void update(BiddingProduct biddingProduct);

        BiddingProduct getById(String id);

        void delete(String id);

        /**
         * 根据订单Id查询物资清单详情
         * @param bidingId
         * @return
         */

        List<BiddingProduct> selectBidingPurchaseByBidingId(String bidingId);

        List<BiddingProduct> getBiddingProductByStSn(String stSn);

        void updateBiddingProductPrice(String id, BigDecimal price, Integer state);
}
