package scrbg.meplat.mall.entity;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@ApiModel(value = "上下架日志")
@Data
@TableName("product_shelf_log")
public class ProductShelfLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "日志id")
    @ExcelIgnore
    private String id;

    @ApiModelProperty(value = "商品id")
    @ExcelIgnore
    private String productId;

    @ApiModelProperty(value = "商品名称")
    @ExcelProperty("商品名称")
    private String productName;

    @ApiModelProperty(value = "商品类型：0物资 （所有商品都是物资，只有下单才会根据分类自动生成不同的订单）最新改动：商品类型：0 低值易耗品 1大宗临购 2、周转材料")
    @ExcelIgnore
    private Integer productType;

    @ApiModelProperty(value = "店铺id")
    @ExcelIgnore
    private String shopId;

    @ApiModelProperty(value = "操作类型：0删除 1上架 2下架")
    @ExcelIgnore
    private Integer operationType;

    @ExcelProperty("操作类型")
    @TableField(exist = false)
    private String operationTypeStr;

    @ApiModelProperty(value = "操作时间")
    @ExcelProperty("操作时间")
    private Date operationTime;

    @ApiModelProperty(value = "库存")
    @ExcelProperty("库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "排序")
    @ExcelProperty("排序")
    private Integer sort;


    public static ProductShelfLog init(String productId, String productName,Integer productType,
                                       String shopId, Integer operationType,
                                       BigDecimal stock,Integer sort) {
        ProductShelfLog productShelfLog = new ProductShelfLog();
        productShelfLog.setProductId(productId);
        productShelfLog.setProductName(productName);
        productShelfLog.setProductType(productType);
        productShelfLog.setShopId(shopId);
        productShelfLog.setOperationTime(new Date());
        productShelfLog.setOperationType(operationType);
        productShelfLog.setStock(stock);
        productShelfLog.setSort(sort);
        return productShelfLog;
    }

    public static List<ProductShelfLog> init(List<Product> products,int operationType) {
        return products.stream().map(product->{
            return init(product.getProductId(),
                    product.getProductName(),product.getProductType(),
                    product.getShopId(),operationType,product.getStock(),
                    product.getSort());
        }).collect(Collectors.toList());
    }

}
