package scrbg.meplat.mall.util;//package scrbg.meplat.mall.util;
//
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import scrbg.meplat.mall.entity.system.SysUser;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JwtToken生成的工具类
 *
 */
@Component("jwtUtil")
public class JwtTokenUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(JwtTokenUtil.class);
    private static final String CLAIM_KEY_USERNAME = "sub";

    private static final String CLAIM_KEY_USERNUMBER = "userNumber";

    private static final String CLAIM_KEY_IS_INTERIOR = "isInterior";


    private static final String CLAIM_KEY_INTERIORID = "interiorId";



    private static final String CLAIM_KEY_CREATED = "created";
    @Value("${jwt.secret}")
    public String secret;
    @Value("${jwt.expiration}")
    public Long expiration;

    /**
     * 根据负责生成JWT的token
     */
    private String generateToken(Map<String, Object> claims) {
        return Jwts.builder()
                .setClaims(claims)
                .setExpiration(generateExpirationDate())
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    /**
     * 从token中获取JWT中的负载
     */
    private Claims getClaimsFromToken(String token) {
        Claims claims = null;
        try {
            claims = Jwts.parser()
                    .setSigningKey(secret)
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            LOGGER.info("JWT格式验证失败:{}",token);
        }
        return claims;
    }

    /**
     * 生成token的过期时间
     */
    private Date generateExpirationDate() {
        return new Date(System.currentTimeMillis() + expiration * 1000);
    }

    /**
     * 从token中获取登录用户名
     */
    public String getUserNameFromToken(String token) {
        String username;
        try {
            Claims claims = getClaimsFromToken(token);
            username =  claims.getSubject();
        } catch (Exception e) {
            username = null;
        }
        return username;
    }

    /**
     * 验证token是否还有效
     *
     * @param token       客户端传入的token
     * @param phone        手机号
     */
    //public boolean validateToken(String token, UserDetails userDetails) {
    //    String username = getUserNameFromToken(token);
    //    return username.equals(userDetails.getUsername()) && !isTokenExpired(token);
    //}
    public boolean validateToken(String token, String phone) {
        String username = getUserNameFromToken(token);
        return username.equals(phone) && !isTokenExpired(token);
    }
    public SysUser getUserInfoFromToken(String token){
        if(StringUtils.isBlank(token)){
            return null;
        }
        SysUser sysUser = new SysUser();
        try {
            if(isTokenExpired(token)){
                return null;
            }
            Claims claims = getClaimsFromToken(token);
            String userNumber =claims.get(CLAIM_KEY_USERNUMBER,String.class);
            Integer isInterior =claims.get(CLAIM_KEY_IS_INTERIOR,Integer.class);
            String interiorId =claims.get(CLAIM_KEY_INTERIORID,String.class);
            sysUser.setUserNumber(userNumber);
            sysUser.setIsInternalUser(isInterior);
            sysUser.setInteriorId(interiorId);
        } catch (Exception e) {
            sysUser = null;
        }
        return sysUser;
    }
    /**
     * 判断token是否已经失效
     */
    private boolean isTokenExpired(String token) {
        Date expiredDate = getExpiredDateFromToken(token);
        return expiredDate.before(new Date());
    }

    /**
     * 从token中获取过期时间
     */
    private Date getExpiredDateFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getExpiration();
    }

    /**
     * 根据手机号生成token
     */
    public String generateToken(String phone) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_KEY_USERNAME, phone);
        claims.put(CLAIM_KEY_CREATED, new Date());
        return generateToken(claims);
    }
    /**
     *  根据用户编号生成token
     */
    public String generateToken(String userNumber,Integer isInterior,String interiorId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_KEY_USERNUMBER, userNumber);
        claims.put(CLAIM_KEY_IS_INTERIOR, isInterior);
        claims.put(CLAIM_KEY_INTERIORID, interiorId);
        claims.put(CLAIM_KEY_CREATED, new Date());
        return generateToken(claims);
    }

    /**
     * 判断token是否可以被刷新
     */
    public boolean canRefresh(String token) {
        return !isTokenExpired(token);
    }

    /**
     * 刷新token
     */
    public String refreshToken(String token) {
        Claims claims = getClaimsFromToken(token);
        claims.put(CLAIM_KEY_CREATED, new Date());
        return generateToken(claims);
    }

    public static void main(String[] args) {
        JwtTokenUtil jwtTokenUtil = new JwtTokenUtil();
        String s = jwtTokenUtil.generateToken("123456789");
        System.out.println(s);
        boolean b = jwtTokenUtil.canRefresh(s);
        System.out.println(b);
        String s1 = jwtTokenUtil.refreshToken(s);
        System.out.println(s1);
    }
}
