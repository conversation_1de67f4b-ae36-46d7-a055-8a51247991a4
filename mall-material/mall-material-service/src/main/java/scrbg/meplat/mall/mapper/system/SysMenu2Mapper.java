package scrbg.meplat.mall.mapper.system;


import com.scrbg.common.utils.PageUtils;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.dto.system.SysMenu2DTO;
import scrbg.meplat.mall.entity.system.SysMenu2;

import java.util.List;
import java.util.Map;

@Mapper
@Repository
public interface SysMenu2Mapper {
    List<SysMenu2> getMenuTree(Map<String, Object> params);
    PageUtils<SysMenu2> getMenuList(Map<String, Object> params);
    boolean addMenu(SysMenu2DTO menu);
    boolean updateMenu(SysMenu2DTO menu);
    boolean deleteMenu(SysMenu2DTO menu);
    SysMenu2 getMenuDetail(String menuId);
    int getPerms(String menuId, String perms);
    List<SysMenu2> getMenuList2(SysMenu2DTO menu);
}
