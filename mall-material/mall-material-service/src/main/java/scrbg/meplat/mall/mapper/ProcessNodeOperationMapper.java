package scrbg.meplat.mall.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import scrbg.meplat.mall.entity.AuditRecords;
import scrbg.meplat.mall.entity.ProcessNodeOperation;

import java.util.List;

/**
 * 流程节点操作表 Mapper 接口
 * <AUTHOR>
 * @date: 2025年6月20日 上午9:47:33
 */
@Mapper
@Repository
public interface ProcessNodeOperationMapper extends BaseMapper<ProcessNodeOperation> {

    List<AuditRecords> findListByInstanceId(@Param("processInstanceId") String processInstanceId);


    List<ProcessNodeOperation> findListByBusinessKey(@Param("businessKey")String businessKey);

}