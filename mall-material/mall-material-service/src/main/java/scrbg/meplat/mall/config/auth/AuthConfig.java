package scrbg.meplat.mall.config.auth;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 权限配置类
 * 用于控制是否启用权限验证
 */
@Component
@ConfigurationProperties(prefix = "mall.auth")
public class AuthConfig {
    
    /**
     * 是否启用权限验证
     * true: 启用权限验证（默认）
     * false: 禁用权限验证，允许无权限访问
     */
    private boolean enabled = true;
    
    /**
     * 是否启用登录验证
     * true: 启用登录验证（默认）
     * false: 禁用登录验证
     */
    private boolean loginEnabled = true;
    
    /**
     * 是否启用角色权限验证
     * true: 启用角色权限验证（默认）
     * false: 禁用角色权限验证
     */
    private boolean roleEnabled = true;
    
    /**
     * 是否启用Token拦截器
     * true: 启用Token拦截器（默认）
     * false: 禁用Token拦截器
     */
    private boolean tokenInterceptorEnabled = true;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isLoginEnabled() {
        return loginEnabled;
    }

    public void setLoginEnabled(boolean loginEnabled) {
        this.loginEnabled = loginEnabled;
    }

    public boolean isRoleEnabled() {
        return roleEnabled;
    }

    public void setRoleEnabled(boolean roleEnabled) {
        this.roleEnabled = roleEnabled;
    }

    public boolean isTokenInterceptorEnabled() {
        return tokenInterceptorEnabled;
    }

    public void setTokenInterceptorEnabled(boolean tokenInterceptorEnabled) {
        this.tokenInterceptorEnabled = tokenInterceptorEnabled;
    }
}
