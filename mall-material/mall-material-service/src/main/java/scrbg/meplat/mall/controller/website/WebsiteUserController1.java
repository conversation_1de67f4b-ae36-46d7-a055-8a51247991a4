package scrbg.meplat.mall.controller.website;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.SysContr;
import scrbg.meplat.mall.entity.system.SysUser;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.service.system.SysUserService;
import scrbg.meplat.mall.util.AESUtil;
import scrbg.meplat.mall.util.code.RandomValidateCodeUtil;
import scrbg.meplat.mall.vo.platform.RegisterPcwpFileVo;
import scrbg.meplat.mall.vo.user.LoginVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @描述：用户控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/w/user")
@ApiSort(value = 99)
@Api(tags = "用户")
@Log4j2
@ConditionalOnProperty(name = "neo.userManage.enable", havingValue = "true")
public class WebsiteUserController1 {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    public EnterpriseInfoService enterpriseInfoService;
//    @Autowired
//    public UserService sysUserService;
    @Autowired
    SysUserService sysUserService;

    @Autowired
    OrdersService ordersService;
    @Autowired
    OrderItemService orderItemService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ProductService productService;

    @Autowired
    FileService fileService;

    //    @Value("${token.privateKey}")
//    private String privateKey;
//
//    @Value("${token.yangToken}")
//    private Long yangToken;

    @Autowired
    SysContrService sysContrService;

    @Autowired
    StationMessageService stationMessageService;


    @ApiOperation("sjlfjsf")
    @GetMapping("/sfsfssfsdfs")
    public void sdfsdfsdf() {
        SysContr byId = sysContrService.getById("1");
        LocalDateTime myDate = byId.getMyDate();
        System.out.println(myDate);
    }

    @ApiOperation("测试导出pdf")
    @ApiOperationSupport(order = 1, author = "叶子")
    @GetMapping("/testPdf")
    public void testPdf(String orderId) throws Exception {
        ordersService.closeOrder2(orderId);

        // 废弃
//        String pdf = "D:\\atest\\test1.pdf";
//        String filePath = "D:\\atest\\test1.xlsx";
//        FileInputStream fis = new FileInputStream(filePath);
//        XSSFWorkbook xssfWorkbook = new XSSFWorkbook(fis);
//
//        //将数据生成workbook
//        OutputStream osOut = response.getOutputStream();
//        ExcelConvertPdf.excelConvertPdf(xssfWorkbook,osOut,response);
//        osOut.close();
//        osOut.flush();



//        String pathOfXls = "D:\\atest\\test1.xlsx";
//        String pathOfXls2 = "D:\\atest\\test2.xlsx";
//        String pathOfPdf = "D:\\atest\\test1.pdf";
//        FileInputStream fis = new FileInputStream(pathOfXls);
//        FileInputStream fis2 = new FileInputStream(pathOfXls2);
//        List<ExcelObject> objects = new ArrayList<ExcelObject>();
//        objects.add(new ExcelObject("",fis));
//        objects.add(new ExcelObject("",fis2));
//        FileOutputStream fos = new FileOutputStream(pathOfPdf);
//        Excel2Pdf pdf = new Excel2Pdf(objects, fos);
//        pdf.convert();

        // 导出word
        // 指定文件名（包含中文）
//        String fileName = URLEncoder.encode("叶哥.docx", StandardCharsets.UTF_8.toString());
//        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
//        OutputStream osOut = response.getOutputStream();
//        HashMap<String, Object> map = new HashMap<>();
//        map.put("name","叶哥");
//        WordUtil.downloadWord(osOut,"D:\\atest\\test.docx",map);

//        InputStream fin = new FileInputStream("C:\\aMySoft\\装备运营平台入驻合同.docx");
//        WordToPdfUtil.convertDocxToPdf(fin,response,"测试合同导出pdf",null);


//        ArrayList<Map> ms1 = new ArrayList<>();
//        ArrayList<Map> ms2 = new ArrayList<>();
//        ArrayList<Map> ms3 = new ArrayList<>();
//
//        Map<String, Object> map1 = new HashMap<>();
//        map1.put("className","低值易耗品");
//        HashMap<Object, Object> map2 = new HashMap<>();
//        map2.put("className","机电类");
//        HashMap<Object, Object> map22 = new HashMap<>();
//        map22.put("className","机电类2");
//        ms2.add(map2);
//        ms2.add(map22);
//        HashMap<Object, Object> map3 = new HashMap<>();
//        map3.put("className","手动工具");
//        map3.put("materialName","扳手/斜口钳/尖嘴钳/扁嘴钳/电工钳/断线钳/管子钳/链条管子钳/台虎钳/桌虎钳/手虎钳/一字螺钉旋具/十字螺钉旋具/游标卡尺/千分");
//        ms3.add(map3);
//        map2.put("items",ms3);
//        map1.put("items",ms2);
//
//
//        HashMap<String, Object> objectHashMap = new HashMap<>();
//        ms1.add(map1);
//        objectHashMap.put("dataList",ms1);
//        String s = JSON.toJSONString(objectHashMap);
//        System.out.println("数据：" + s);
//        try {
//            ExcelForWebUtil.testSaveExcel("C:\\aMySoft\\test.xlsx" , objectHashMap,"C:\\aMySoft\\dsafadsf.xlsx");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }


    }


    @PostMapping("/updatePassSendCode")
    @ApiOperation(value = "未登录时修改密码发送验证码")
    public R updatePassSendCode(String phone,String privateKeyId,HttpServletRequest request) {
        //sysUserService.noUpdatePassSendCode(phone,privateKeyId,request);
        sysUserService.noUpdatePassSendCode(phone,privateKeyId,request);
        return R.success();
    }

    @PostMapping("/checkUpdatePassCode")
    @ApiOperation(value = "未登录时检验修改密码的验证码")
    public R checkUpdatePassCode(String phone, String code) {
        //sysUserService.checkUpdatePassCode(phone, code);
        sysUserService.checkUpdatePassCode(phone,code);
        return R.success();
    }



    @Autowired
    MallConfig mallConfig;
    @Autowired
    PlatformYearFeeService platformYearFeeService;
    /**
     * 工具方法，查询用户信息
     */
    @Value("${spring.profiles.active}")
    private String env;

    @GetMapping("/findUserInfo")
    public List<Object> parseUserInfo(String keywords) {
        // 判断环境是否为开发环境
        if (env == null || !env.equalsIgnoreCase("dev")) {
            return Collections.emptyList();
        }

        if (StringUtils.isBlank(keywords)) {
            throw new BusinessException("err");
        }

        String finalKeywords = keywords.trim();
        List<UserInfoDTO> userInfoList = new ArrayList<>();

        // 查询企业信息
        List<EnterpriseInfo> enterpriseInfos = enterpriseInfoService.lambdaQuery()
                .isNull(EnterpriseInfo::getInteriorId)
                .and(wa -> {
                    wa.like(EnterpriseInfo::getEnterpriseName, finalKeywords)
                            .or()
                            .like(EnterpriseInfo::getSocialCreditCode, finalKeywords);
                }).list();

        // 根据企业信息查询管理员信息
        if (!CollectionUtils.isEmpty(enterpriseInfos)) {
            for (EnterpriseInfo enterpriseInfo : enterpriseInfos) {
                //User user = sysUserService.lambdaQuery().eq(User::getEnterpriseId, enterpriseInfo.getEnterpriseId()).one();
                SysUser user = sysUserService.lambdaQuery().eq(SysUser::getEnterpriseId,enterpriseInfo.getEnterpriseId()).one();
                if ( user != null && user.getIsInternalUser() == 0) {
                    String enterpriseName = enterpriseInfo.getEnterpriseName();
                    String userName = user.getUserMobile(); // 获取用户名称
                    String socialCreditCode = enterpriseInfo.getSocialCreditCode();
                    String userMobile = user.getUserMobile();
                    String decryptPwd = AESUtil.decrypt(user.getPassword());

                    // 将信息添加到DTO对象中
                    UserInfoDTO userInfoDTO = new UserInfoDTO();
                    userInfoDTO.setEnterpriseName(enterpriseName);
                    userInfoDTO.setUserName(userName);
                    userInfoDTO.setUserMobile(userMobile);
                    userInfoDTO.setSocialCreditCode(socialCreditCode);
                    userInfoDTO.setPassword(decryptPwd);
                    userInfoList.add(userInfoDTO);
                }

            }
        }

        // 返回包含用户信息的列表
        return Collections.singletonList(userInfoList);
    }
    @GetMapping("/findPassword")
    public String parseUserInfo2(String keywords,String code) {

        // 判断环境是否为开发环境
//        if (env == null || !env.equalsIgnoreCase("dev")) {
//            return null;
//        }
        if (code.equals("**************************.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA")){
            String decryptPwd = AESUtil.decrypt(keywords);
            return "密码:= "+decryptPwd;
        }else {
            return null;
        }

    }

    public static class UserInfoDTO {
        private String enterpriseName;
        private String userName;
        private String account;
        private String userMobile;
        private String socialCreditCode;
        private String password;

        // Getters and Setters

        public String getEnterpriseName() {
            return enterpriseName;
        }

        public void setEnterpriseName(String enterpriseName) {
            this.enterpriseName = enterpriseName;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getAccount() {
            return account;
        }

        public void setAccount(String account) {
            this.account = account;
        }

        public String getUserMobile() {
            return userMobile;
        }

        public void setUserMobile(String userMobile) {
            this.userMobile = userMobile;
        }

        public String getSocialCreditCode() {
            return socialCreditCode;
        }

        public void setSocialCreditCode(String socialCreditCode) {
            this.socialCreditCode = socialCreditCode;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }





    @ApiOperation("用户登录接口")
    @ApiOperationSupport(order = 10, author = "叶子")
    @ApiImplicitParams({
            @ApiImplicitParam(dataType = "string", name = "account", value = "用户登录账号", required = true),
            @ApiImplicitParam(dataType = "string", name = "password", value = "用户登录密码", required = true),
    })
    @PostMapping("/login")
    public R<LoginVO> login(@RequestParam("account") String account, @RequestParam("password") String password, HttpServletRequest request) {

        LoginVO vo = null;
        try {
            // 如果当前错误次数大于等于8次拒绝登录
            sysUserService.handlePwdExpire(account);
            SysUser userPwd = sysUserService.lambdaQuery().eq(SysUser::getUserMobile, account).one();
            if (userPwd!=null &&userPwd.getLockedState()  == 1){
                if (userPwd.getLockCause().contains("30")){
                    throw new BusinessException("超过30天未登录，请使用手机验证码登录并修改密码！！");
                }else {
                    throw new BusinessException("密码超过今日最大次数，用户已锁定，请明日再试或使用手机验证码登录！！");
                }
            }
            //判断pcwp_personinfos表中是否有数据

            //vo = sysUserService.login(account, password, 0, request);
            vo = sysUserService.login(account, password, 0, request);
        } catch (Exception e) {
            // 密码错误异常捕捉
            if (e.getMessage().equals("密码错误") || e.getMessage().equals("用户名或密码错误！") ) {
                SysUser one = sysUserService.lambdaQuery().eq(SysUser::getAccount, account)
                        .eq(SysUser::getAttrOne,1)
                        .select(SysUser::getAttrOne, SysUser::getUserId).one();
                if(one != null) {
                    sysUserService.lambdaUpdate().eq(SysUser::getUserId, one.getUserId())
                            .set(SysUser::getAttrOne,0).update();
                    throw new BusinessException(500113,"尊敬的用户，由于您账号的密码强不够，为了安全考虑，平台已修改您的密码，原密码不能登录，请联系管理员。谢谢！");
                }
                // 保存
//                sysUserService.handlePwdError(account);
                sysUserService.handlePwdError(account);
            }
            // 统一异常抛出
            if (e.getMessage().contains("错误")){
                throw new BusinessException("用户名或密码错误");
            }
            throw new BusinessException(e.getMessage());
        }
        // 登录成功之后，密码错误次数重置
//        sysUserService.handleuccessLogin(account);
        sysUserService.handleuccessLogin(account);
        // 首次登录处理
        //sysUserService.handleFirstLogin(vo);
        sysUserService.handleFirstLogin(vo);
        // 添加菜单
//        sysUserService.addUserMenu(vo);
        sysUserService.addUserMenu(vo);
        if(mallConfig.isPlatformFee == 1) {
            Map<String, String> map = new HashMap<>();
            try {
                platformYearFeeService.checkYearMonthIsOut(vo);
            }catch (BusinessException e) {
                map.put("checkShopCode", String.valueOf(e.getCode()));
                map.put("checkShopMessage",e.getMessage());
            }
            try {
                platformYearFeeService.checkYearStandardIsOut(vo);
            }catch (BusinessException e) {
                map.put("checkBidCode", String.valueOf(e.getCode()));
                map.put("checkBidMessage",e.getMessage());
            }
            vo.setDiaLogMap(map);

        }

        //登录成功，如果是供应商，需验证资料是否过期，过期需要发送站内信息
        //stationMessageService.checkSupplierInfo(vo.getIsSupplier(),vo.getLocalOrgId());

        return R.success(vo);
    }


    @ApiOperation("内部用户登录接口")
    @ApiImplicitParams({
            @ApiImplicitParam(dataType = "string", name = "account", value = "用户登录账号", required = true),
            @ApiImplicitParam(dataType = "string", name = "password", value = "用户登录密码", required = true),
    })
    @PostMapping("/interiorLogin")
    public R<LoginVO> interiorLogin(@RequestParam("account") String account, @RequestParam("password") String password, HttpServletRequest request) {

//        LoginVO vo = sysUserService.interiorLogin(account, password, 0,request);
        LoginVO vo = sysUserService.interiorLogin(account, password, 0,request);
        sysUserService.handleFirstLogin(vo);
        sysUserService.addUserMenu(vo);
        return R.success(vo);
    }

    @ApiOperation("供应商登陆")
    @ApiImplicitParams({
            @ApiImplicitParam(dataType = "string", name = "account", value = "用户登录账号", required = true),
            @ApiImplicitParam(dataType = "string", name = "password", value = "用户登录密码", required = true),
    })
    @PostMapping("/supplierLogin")
    public R<LoginVO> supplierLogin(@RequestParam("account") String account, @RequestParam("password") String password, HttpServletRequest request) {

        LoginVO vo = null;
        try {
            vo = sysUserService.supplierLogin(account, password, 0, request);
            //
        } catch (Exception e) {
            // 密码错误异常捕捉
            if (e.getMessage().equals("密码错误") || e.getMessage().equals("用户名或密码错误！") ) {
                // 保存密码错误次数
                sysUserService.handlePwdError(account);
            }
            throw new BusinessException(e.getMessage());
        }
        sysUserService.handleuccessLogin(account);
        sysUserService.handleFirstLogin(vo);
        sysUserService.addUserMenu(vo);
        return R.success(vo);
    }

    @PostMapping("/supplierPhoneLogin")
    @ApiOperation("供应商手机号登陆")
    @ApiImplicitParams({
            @ApiImplicitParam(dataType = "string", name = "phone", value = "用户登录账号", required = true),
            @ApiImplicitParam(dataType = "string", name = "code", value = "验证码", required = true),
    })
    public R<LoginVO> supplierPhoneLogin(@RequestParam("phone") String phone, @RequestParam("code") String code,HttpServletRequest request) {

        LoginVO vo = sysUserService.supplierPhoneLogin(phone, code, 0,request);
        sysUserService.handleFirstLogin(vo);
        sysUserService.addUserMenu(vo);
        if(mallConfig.isPlatformFee == 1) {
            Map<String, String> map = new HashMap<>();
            try {
                platformYearFeeService.checkYearMonthIsOut(vo);
            }catch (BusinessException e) {
                map.put("checkShopCode", String.valueOf(e.getCode()));
                map.put("checkShopMessage",e.getMessage());
            }
            try {
                platformYearFeeService.checkYearStandardIsOut(vo);
            }catch (BusinessException e) {
                map.put("checkBidCode", String.valueOf(e.getCode()));
                map.put("checkBidMessage",e.getMessage());
            }
            vo.setDiaLogMap(map);
        }
        return R.success(vo);
    }

    @ApiOperation("tt登陆")
    @ApiImplicitParams({
            @ApiImplicitParam(dataType = "string", name = "a", value = "账号", required = true),
            @ApiImplicitParam(dataType = "string", name = "p", value = "临时密码", required = true),
    })
    @PostMapping("/ttLogin")
    public R ttLogin(@RequestParam("a") String a, @RequestParam("p") String p, HttpServletRequest request) {

        LoginVO vo = sysUserService.ttLogin(a, p, 0,request);
        sysUserService.handleFirstLogin(vo);
        sysUserService.addUserMenu(vo);
        return R.success(vo);
    }

    @PostMapping("/phoneLogin")
    @ApiOperation("手机号登陆")
    @ApiImplicitParams({
            @ApiImplicitParam(dataType = "string", name = "phone", value = "用户登录账号", required = true),
            @ApiImplicitParam(dataType = "string", name = "code", value = "验证码", required = true),
    })
    public R<LoginVO> phoneLogin(@RequestParam("phone") String phone, @RequestParam("code") String code, HttpServletRequest request) {

        LoginVO vo = sysUserService.phoneLogin(phone, code, 0,request);
        sysUserService.handleFirstLogin(vo);
        sysUserService.addUserMenu(vo);
        return R.success(vo);
    }

    @ApiOperation("token登陆")
    @ApiImplicitParams({
            @ApiImplicitParam(dataType = "string", name = "token", value = "token令牌", required = true),
            @ApiImplicitParam(dataType = "Integer", name = "mallType", value = "商城类型", required = false),
    })
    @GetMapping("/tokenLogin")
    public R<LoginVO> tokenLogin(@RequestParam("token") String token,HttpServletRequest request) {

//        LoginVO vo = sysUserService.tokenLogin(token, 0,request);
//        sysUserService.handleFirstLogin(vo);
//        sysUserService.addUserMenu(vo);
        LoginVO vo = sysUserService.tokenLogin(token, 0,request);
        sysUserService.handleFirstLogin(vo);
        sysUserService.addUserMenu(vo);
        return R.success(vo);
    }
    @Value("${app.verify-code: true}")
    private boolean verifyCode;
    @PostMapping("/registerEnterprise")
    @ApiOperation(value = "企业用户注册、个体户注册（外部）")
    public R registerEnterprise(@RequestBody EnterpriseInfo enterpriseInfo) {
        if (verifyCode) {
            String rCode = stringRedisTemplate.opsForValue().get("register:verify:" + enterpriseInfo.getVerifyId());
            if (rCode == null) {
                throw new BusinessException("图形验证码已失效，请刷新！");
            }
            if (!rCode.equalsIgnoreCase(enterpriseInfo.getVerifyInput())) {
                throw new BusinessException("图形验证码错误！");
            }
        }
        return enterpriseInfoService.enterpriseRegistration(enterpriseInfo);
    }

    @GetMapping("/selectIsPcwpUserByCode")
    @ApiOperation(value = "判断用户是否为pcwp供应商,返回对应的入库资料")
    public R<RegisterPcwpFileVo> selectIsPcwpUserByCode(String socialCreditCode, String programaKey) {
        RegisterPcwpFileVo pcwpFileVo = enterpriseInfoService.selectIsPcwpUserByCode(socialCreditCode, programaKey);
        return R.success(pcwpFileVo);
    }

    @PostMapping("/registerPerson")
    @ApiOperation(value = "个人用户注册（外部）")
    public R registerPerson(@RequestBody SysUser user) {
        if (verifyCode) {
            String rCode = stringRedisTemplate.opsForValue().get("register:verify:" + user.getVerifyId());
            if (rCode == null) {
                throw new BusinessException("图形验证码已失效，请刷新！");
            }
            if (!rCode.equalsIgnoreCase(user.getVerifyInput())) {
                throw new BusinessException("图形验证码错误！");
            }
        }
//        return sysUserService.registration(user);
        return sysUserService.registration(user);
    }


    @PostMapping("/indexPutPhoneCode")
    @ApiOperation(value = "登陆发送手机验证码")
    public R loginSendCode(String phone,String privateKeyId,HttpServletRequest request) {
        sysUserService.loginSendCode(phone,privateKeyId,request);
        sysUserService.loginSendCode(phone,privateKeyId,request);
        return R.success();
    }

    @PostMapping("/registerSendCode")
    @ApiOperation(value = "注册发送手机验证码")
    public R registerSendCode(String phone,String privateKeyId) {
//        sysUserService.registerSendCode(phone,privateKeyId);
        sysUserService.registerSendCode(phone,privateKeyId);
        return R.success();
    }

    @GetMapping("/getPrivateKeyId")
    @ApiOperation(value = "注册发送前秘钥id")
    public R getPrivateKeyId(String phone) {
        String idStr = IdWorker.getIdStr();
        stringRedisTemplate.opsForValue().set("register:privateId" + phone, idStr , 30, TimeUnit.SECONDS);
        return R.success(idStr);
    }


    @GetMapping("/getIsSupplier")
    @ApiOperation(value = "根据信用代码查询是否是供应商")
    public R getIsSupplier(String socialCreditCode, Integer mallType) {
//        boolean b = sysUserService.getIsSupplier(socialCreditCode, mallType);
        boolean b  = sysUserService.getIsSupplier(socialCreditCode, mallType);
        return R.success(b);
    }


    @GetMapping("/getSupplierInfo")
    @ApiOperation(value = "根据信用代码查询供应商信息")
    public R getSupplierInfo(String socialCreditCode, Integer mallType) {
//        EnterpriseInfo enterpriseInfo = sysUserService.getSupplierInfo(socialCreditCode, mallType);
        EnterpriseInfo enterpriseInfo = sysUserService.getSupplierInfo(socialCreditCode, mallType);
        return R.success(enterpriseInfo);
    }

    @PostMapping("/updateBatchUserState")
    @ApiOperation(value = "批量修改状态")
    public R updateBatchUserState(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
//        sysUserService.updateBatchUserState(jsonObject, request);
        sysUserService.updateBatchUserState(jsonObject,request);
        return R.success();
    }

    @PostMapping("/getUserListByids")
    @ApiOperation(value = "批量查询用户数据")
    public R<List<SysUser>> getUserListByids(@RequestBody List<String> ids) {
//        List<User> list = sysUserService.getUserListByids(ids);
        List<SysUser> list = sysUserService.getUserListByids(ids);
        return R.success(list);
    }

    @GetMapping("/findSupperByCreditCodeAndType")
    @ApiOperation(value = "根据社会信用代码(外部供应商)或机构判断用户是否是供应商")
    public Boolean findSupperByCreditCodeAndType(String creditCode, Integer supplierType) {
        Boolean flag = enterpriseInfoService.findSupperByCreditCodeAndType(creditCode, supplierType);
        return flag;
    }


    @GetMapping(value = "/getSendCodeVerify")
    @ApiOperation(value = "生成图形验证码（发送验证码）")
    public void getSendCodeVerify(HttpServletRequest request, HttpServletResponse response) {
        try {
            //设置相应类型,告诉浏览器输出的内容为图片
            response.setContentType("image/jpeg");
            //设置响应头信息，告诉浏览器不要缓存此内容
            response.setHeader("Pragma", "No-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setDateHeader("Expire", 0);
            String idStr = IdWorker.getIdStr();
            response.setHeader("verifyId",idStr);
            RandomValidateCodeUtil randomValidateCode = new RandomValidateCodeUtil();
            randomValidateCode.createCode();
            String code = randomValidateCode.getCode();
            stringRedisTemplate.opsForValue().set("sendCode:verify:" + idStr, code, 180, TimeUnit.SECONDS);
            randomValidateCode.outCode(response);
        } catch (Exception e) {
            log.error("获取图形验证码失败>>>>   ", e);
        }
    }

    /**
     * 校验验证码
     */
    @GetMapping(value = "/checkSendCodeVerify")
    @ApiOperation(value = "校验验证码")
    public R checkSendCodeVerify(String verifyInput,String id) {
        String rCode = stringRedisTemplate.opsForValue().get("sendCode:verify:" + id);
        if (rCode == null) {
            throw new BusinessException("验证码已失效，请刷新！");
        }
        if (!rCode.equalsIgnoreCase(verifyInput)) {
            throw new BusinessException("图形验证码错误！");
        }
        return R.success();
    }

    @GetMapping(value = "/checkPwdState")
    @ApiOperation(value = "校验密码次数")
    public R checkPwdState(String phone) {
        Map locking = sysUserService.getLockingByPhone(phone);
        return R.success(locking);
    }
    @GetMapping(value = "/checkExpiring")
    @ApiOperation(value = "是否需要提示修改密码")
    public R checkExpiring(String phone) {
        Map locking = sysUserService.handleExpireing(phone);
        return R.success(locking);
    }
//    @GetMapping(value = "/checsfaasdfkVerify")
//    @ApiOperation(value = "测试1111")
//    public R checsfaasdfkVerify() {
//        ArrayList<OrderShipmentsQtyIsOkDTO> vos = new ArrayList<>();
//        OrderShipmentsQtyIsOkDTO or = new OrderShipmentsQtyIsOkDTO();
//        or.setOrderItemId("1723956603462455298");
//        or.setQty(new BigDecimal(11));
//        vos.add(or);
//        orderItemService.orderShipmentsQtyIsOkLG(vos);
//        return R.success();
//    }


//    @GetMapping(value = "/checsfaasdf222Verify")
//    @ApiOperation(value = "测2222")
//    public R checsfaasdfkVerif222y() {
//        ArrayList<OrderShipmentsQtyIsOkDTO> vos = new ArrayList<>();
//        OrderShipmentsQtyIsOkDTO or = new OrderShipmentsQtyIsOkDTO();
//        or.setOrderItemId("1723958221427806210");
//        or.setQty(new BigDecimal(11));
//        vos.add(or);
//        orderItemService.orderShipmentsQtyIsOkYG(vos);
//        return R.success();
//    }

    /**
     * 注册生成验证码
     */
    @GetMapping(value = "/getRegisterVerify")
    @ApiOperation(value = "注册生成图形验证码")
    public void getRegisterVerify(HttpServletResponse response) {
        try {
            //设置相应类型,告诉浏览器输出的内容为图片
            response.setContentType("image/jpeg");
            //设置响应头信息，告诉浏览器不要缓存此内容
            response.setHeader("Pragma", "No-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setDateHeader("Expire", 0);
            String idStr = IdWorker.getIdStr();
            response.setHeader("verifyId",idStr);
            RandomValidateCodeUtil randomValidateCode = new RandomValidateCodeUtil();
            randomValidateCode.createCode();
            String code = randomValidateCode.getCode();
            stringRedisTemplate.opsForValue().set("register:verify:" + idStr, code, 60, TimeUnit.SECONDS);
            randomValidateCode.outCode(response);
        } catch (Exception e) {
            log.error("获取图形验证码失败>>>>   ", e);
        }
    }


    @PostMapping("/updatePassword")
    @ApiOperation(value = "修改密码")
    public R updatePassword(String userPhone,String newPassword) {
        sysUserService.updatePassword(userPhone,newPassword);
        return R.success();
    }

}
