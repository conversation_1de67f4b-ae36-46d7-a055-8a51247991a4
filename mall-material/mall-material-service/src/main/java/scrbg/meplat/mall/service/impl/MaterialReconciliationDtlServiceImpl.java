package scrbg.meplat.mall.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.InvoiceDtl;
import scrbg.meplat.mall.entity.MaterialReconciliation;
import scrbg.meplat.mall.entity.MaterialReconciliationDtl;
import scrbg.meplat.mall.entity.PcwpOrg;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.MaterialReconciliationDtlMapper;
import scrbg.meplat.mall.service.MaterialReconciliationDtlService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.service.MaterialReconciliationService;
import scrbg.meplat.mall.service.PcwpOrgService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.util.pageUtils;
import scrbg.meplat.mall.vo.platform.ListShipByAffirmListVO;
import scrbg.meplat.mall.vo.platform.ReconciliationLedgerListVo;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述：物资验收明细 服务类
 * @作者: ye
 * @日期: 2023-07-26
 */
@Service
public class MaterialReconciliationDtlServiceImpl extends ServiceImpl<MaterialReconciliationDtlMapper, MaterialReconciliationDtl> implements MaterialReconciliationDtlService{

    @Autowired
    public MaterialReconciliationService materialReconciliationService;

    @Autowired
    PcwpOrgService pcwpOrgService;

    @Autowired
    private MallConfig mallConfig;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliationDtl> queryWrapper) {
        IPage<MaterialReconciliationDtl> page = this.page(
        new Query<MaterialReconciliationDtl>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(MaterialReconciliationDtl materialReconciliationDtl) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(materialReconciliationDtl);
    }

    @Override
    public void update(MaterialReconciliationDtl materialReconciliationDtl) {
        super.updateById(materialReconciliationDtl);
    }


    @Override
    public MaterialReconciliationDtl getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }


    @Override
    public PageUtils ListByBillIds(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliationDtl> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        List<String> reconciliationIds = (List<String>) innerMap.get("reconciliationIds");
        queryWrapper.in(MaterialReconciliationDtl::getReconciliationId,reconciliationIds);
        IPage<MaterialReconciliationDtl> page = this.page(
                new Query<MaterialReconciliationDtl>().getPage(jsonObject),
                queryWrapper
        );
        List<MaterialReconciliationDtl> records = page.getRecords();
        ArrayList<MaterialReconciliationDtl> materialReconciliationDtls = new ArrayList<>();
        if (records!=null&&records.size()>0){
            Map<String, List<MaterialReconciliationDtl>> reconciliation = records.stream().collect(Collectors.groupingBy(MaterialReconciliationDtl::getReconciliationId));
            reconciliation.forEach((reconciliationId, list) -> {
                MaterialReconciliation info = materialReconciliationService.getById(reconciliationId);
                for (MaterialReconciliationDtl reconciliationDtl : list) {
                    reconciliationDtl.setReconciliationNo(info.getReconciliationNo());
                    reconciliationDtl.setTaxRate(info.getTaxRate());
                    materialReconciliationDtls.add(reconciliationDtl);
                }
            });
        }
        return new PageUtils(page.setRecords(materialReconciliationDtls));
    }

    @Override
    public PageUtils materialReconciliationLedger(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String sortCode = (String) innerMap.get("sortCode");
        PcwpOrg bySortCode = pcwpOrgService.getBySortCode(sortCode);
        if(bySortCode != null){
            jsonObject.put("sortCode",bySortCode.getSortcode());
            jsonObject.put("orgId",sortCode);
        }
        int count = baseMapper.listLedgerCount(jsonObject.getInnerMap());
        pageUtils.pageDispose(jsonObject, count);
        Page<ReconciliationLedgerListVo> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ReconciliationLedgerListVo> vos = baseMapper.ledgerList(pages, jsonObject);
        BigDecimal CountAmount = baseMapper.selCountAmount(jsonObject);
        BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(jsonObject);
         if (vos.size() > 0) {
             vos.get(0).setCountAmount(CountAmount);
             vos.get(0).setCountNoRateAmount(countNoRateAmount);
         }
        pages.setRecords(vos);
        return new PageUtils(pages);
    }
    @Override
    public void materialReconciliationLedgerExcel(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper, HttpServletResponse response) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String sortCode = (String) innerMap.get("sortCode");
        PcwpOrg bySortCode = pcwpOrgService.getBySortCode(sortCode);
        if(bySortCode != null){
            jsonObject.put("sortCode",bySortCode.getSortcode());
            jsonObject.put("orgId",sortCode);
        }
        List<ReconciliationLedgerListVo> vos = baseMapper.ledgerListExcel(jsonObject);
        if(io.seata.common.util.CollectionUtils.isNotEmpty( vos)){
            BigDecimal bigDecimal = new BigDecimal(0);
            for (ReconciliationLedgerListVo vo : vos) {
                bigDecimal = bigDecimal.add(vo.getAcceptanceAmount());
            }
            vos.get(0).setCountAmount(bigDecimal);
        }
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", vos);
        dataMap.put("countAmountTotal", vos.get(0).getCountAmount());
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "平台物资对账统计台账模板.xlsx", src, "平台物资对账台账统计.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }

    @Override
    public void materialReconciliationLedgerSupplierExcel(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper, HttpServletResponse response) {
        // 通过订单id关联shopId
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String shopId = (String) jsonObject.getInnerMap().get("shopId");
        if (StringUtils.isNotEmpty(shopId)) {
            //materialReconciliationLambdaQueryWrapper.eq("o.shop_id", shopId);
            jsonObject.getInnerMap().put("shopId", shopId);
        } else {
            //materialReconciliationLambdaQueryWrapper.eq("o.shop_id", user.getShopId());
            jsonObject.getInnerMap().put("shopId", user.getShopId());
        }
        List<ReconciliationLedgerListVo> vos = baseMapper.ledgerList(jsonObject);
        for (ReconciliationLedgerListVo vo : vos){
            vo.setReconciliationTypeStr(getProductTypeName(Integer.parseInt(vo.getReconciliationProductType())));
            vo.setStartTimeStr(getDateStr(vo.getStartTime()));
            vo.setEndTimeStr(getDateStr(vo.getEndTime()));
            vo.setStateStr(getStateName(vo.getState()));
        }
        BigDecimal CountAmount = baseMapper.selCountAmount(jsonObject);
        BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(jsonObject);
        if (vos.size() > 0) {
            vos.get(0).setCountAmount(CountAmount);
            vos.get(0).setCountNoRateAmount(countNoRateAmount);
        }
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", vos);
        dataMap.put("countAmountTotal", vos.get(0).getCountAmount());
        dataMap.put("countNoRateAmount", vos.get(0).getCountNoRateAmount());
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "供应商物资对账统计台账模板（物资维度）.xlsx", src, "平台物资对账台账统计（物资维度）.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }
    private String getProductTypeName(Integer productType) {
        if (productType == null) {
            return "未知类型";
        }
        switch (productType) {
            case 0:
                return "零星采购";
            case 1:
                return "大宗临购";
            case 2:
                return "周材材料";
            default:
                return "未知类型";
        }
    }
    private String getDateStr(Date date) {
        if(date == null){
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }
    private String getStateName(Integer state){
        if (state == null) {
            return "未知状态";
        }
        switch (state) {
            case 0:
                return "买方已审核";
            case 1:
                return "卖方已审核";
            case 2:
                return "对账已完成";
            case 3:
                return "对账已作废";
            default:
                return "未知状态";
        }
    }
    private String getNoRate(BigDecimal amount,BigDecimal taxRate) {
        if (amount == null || taxRate == null){
            return null;
        }else {
            // 将税率百分比转换为小数形式（3 -> 0.03）
            BigDecimal taxRateDecimal = taxRate.divide(BigDecimal.valueOf(100), 6, BigDecimal.ROUND_HALF_UP);
            // 计算不含税金额：含税金额 / (1 + 税率)
            BigDecimal divisor = BigDecimal.ONE.add(taxRateDecimal);
            BigDecimal noRateAmount = amount.divide(divisor, 2, BigDecimal.ROUND_HALF_UP);
            return String.valueOf(noRateAmount);
        }
    }
    @Override
    public PageUtils materialReconciliationOrgLedger(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String productType = (String) innerMap.get("productType");
        String sortCode = (String) innerMap.get("sortCode");
        List<PcwpOrg> orgs = new ArrayList<>();
        if(org.springframework.util.StringUtils.isEmpty(sortCode)) {
            orgs = pcwpOrgService.getTopOrg();// 四川路桥
            if(orgs.isEmpty()){
                throw new BusinessException(500, "机构错误");
            }
        }else {
            PcwpOrg byId = pcwpOrgService.getBySortCode(sortCode);
            if(byId == null) {
                throw new BusinessException(500, "请选择正确的机构");
            }
            orgs = pcwpOrgService.getChildren(byId.getId());
        }
        // 获取分页参数
        int currentPage = (Integer) jsonObject.get("page");
        int pageSize = (Integer) jsonObject.get("limit");

        // 计算分页起始位置
        int startIndex = (currentPage - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, orgs.size());
        Page<ReconciliationLedgerListVo> pages = new Page<>(currentPage, pageSize);
        pages.setTotal(orgs.size());
        List<ReconciliationLedgerListVo> vos = new ArrayList<>();
        // 只处理当前页的数据
        for (int i = startIndex; i < endIndex; i++) {
            PcwpOrg pcwpOrg = orgs.get(i);
            ReconciliationLedgerListVo vo = new ReconciliationLedgerListVo();
            vo.setOrglayertypenumber(pcwpOrg.getOrglayertypenumber());
            vo.setSortCode(pcwpOrg.getId());
            vo.setPurchasingOrgId(pcwpOrg.getId());
            vo.setPurchasingOrgName(pcwpOrg.getName());
            vo.setReconciliationProductType(productType);

            // 创建新的JSONObject用于当前查询，避免参数污染
            JSONObject orgJsonObject = new JSONObject();
            orgJsonObject.putAll(innerMap); // 复制原始参数
            orgJsonObject.put("sortCode", pcwpOrg.getSortcode());
            // 处理可能为null的BigDecimal值
            BigDecimal countAmount = baseMapper.selCountAmount(orgJsonObject);
            BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(orgJsonObject);
            vo.setAcceptanceAmount(countAmount != null ? countAmount : BigDecimal.ZERO);
            vo.setAcceptanceNoRateAmount(countNoRateAmount != null ? countNoRateAmount : BigDecimal.ZERO);
            vos.add(vo);
        }
        PcwpOrg bySortCode = pcwpOrgService.getBySortCode(sortCode);
        if(bySortCode != null){
            jsonObject.put("sortCode", bySortCode.getSortcode());
        }
        BigDecimal countAmount = baseMapper.selCountAmount(jsonObject);
        BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(jsonObject);
        if (vos.size() > 0) { //获取总额
            vos.get(0).setCountAmount(countAmount != null ? countAmount : BigDecimal.ZERO);
            vos.get(0).setCountNoRateAmount(countNoRateAmount != null ? countNoRateAmount : BigDecimal.ZERO);
        }
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    @Override
    public PageUtils materialReconciliationLedgerSupplier(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper) {
        // 通过订单id关联shopId
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String shopId = (String) jsonObject.getInnerMap().get("shopId");
        if (StringUtils.isNotEmpty(shopId)) {
            jsonObject.getInnerMap().put("shopId", shopId);
        } else {
            jsonObject.getInnerMap().put("shopId", user.getShopId());
        }
        int count = baseMapper.listLedgerCount(jsonObject.getInnerMap());
        pageUtils.pageDispose(jsonObject, count);
        Page<ReconciliationLedgerListVo> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ReconciliationLedgerListVo> vos = baseMapper.ledgerList(pages, jsonObject);
        BigDecimal CountAmount = baseMapper.selCountAmount(jsonObject);
        BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(jsonObject);
        if (vos.size() > 0) {
            vos.get(0).setCountAmount(CountAmount);
            vos.get(0).setCountNoRateAmount(countNoRateAmount);
        }
        pages.setRecords(vos);
        return new PageUtils(pages);
    }
}
