package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.ShopMapper;
import scrbg.meplat.mall.mapper.StationMessageMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;
import scrbg.meplat.mall.vo.product.material.PlatformMaterialListVO;
import scrbg.meplat.mall.vo.product.material.StationMessageVo;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static scrbg.meplat.mall.common.constant.CommonConstants.BACK_OFFICE_ADMINISTRATOR_NAME;
import static scrbg.meplat.mall.common.constant.CommonConstants.SYSTEM_NAME;

/**
 * @描述：站点消息 服务类
 * @作者: y
 * @日期: 2022-11-24
 */
@Slf4j
@Service
public class StationMessageServiceImpl extends ServiceImpl<StationMessageMapper, StationMessage> implements StationMessageService {


    @Autowired
    FileService fileService;
    @Autowired
    UserService userService;
    @Autowired
    public MallConfig mallConfig;
    @Autowired
    public StationMessageReceiveService stationMessageReceiveService;

    @Autowired
    public StationMessageMapper stationMessageMapper;
    @Autowired
    public ShopService shopService;

    @Autowired
    public ShopMapper shopMapper;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    PlatformYearFeeService platformYearFeeService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<StationMessage> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        wrapper.eq(StationMessage::getMallType, mallConfig.mallType);
        wrapper.eq(StationMessage::getSendId, BACK_OFFICE_ADMINISTRATOR_NAME);
        Integer messageType = (Integer) innerMap.get("messageType");
        if (messageType != null) {
            wrapper.eq(StationMessage::getMessageType, messageType);
        }
        String title = (String) innerMap.get("title");
        if (!StringUtils.isEmpty(title)) {
            wrapper.like(StationMessage::getTitle, title);
        }
        String keywords = (String) innerMap.get("keywords");
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.like(StationMessage::getTitle, keywords);

        }
        String startDate = (String) innerMap.get("startDate");
        if (!StringUtils.isEmpty(startDate)) {
            wrapper.gt(StationMessage::getSendDate, startDate);
        }
        String endDate = (String) innerMap.get("endDate");
        if (!StringUtils.isEmpty(endDate)) {
            wrapper.lt(StationMessage::getSendDate, endDate);
        }
        Integer allRead = (Integer) innerMap.get("allRead");
        if (!StringUtils.isEmpty(allRead)) {
            wrapper.eq(StationMessage::getAllRead, allRead);
        }
        wrapper.orderByDesc(StationMessage::getSendDate);
        IPage<StationMessage> page = this.page(
                new Query<StationMessage>().getPage(jsonObject),
                wrapper
        );
        return new PageUtils(page);
    }

    @Override
    public PageUtils queryPageList(JSONObject jsonObject, QueryWrapper<StationMessage> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        Integer receiveType = (Integer) innerMap.get("receiveType");
        if (receiveType == 0) {
            wrapper.eq("r.receive_id", currentUser.getShopId());
        } else if (receiveType == 1) {
            wrapper.eq("r.receive_id", currentUser.getUserId());
        }else {
            wrapper.eq("r.receive_id", currentUser.getEnterpriseId());
        }
        if (!StringUtils.isEmpty(receiveType)) {
            wrapper.eq("r.receive_type", receiveType);
        }
        wrapper.eq("r.mall_type", mallConfig.mallType);
        String title = (String) innerMap.get("title");
        if (!StringUtils.isEmpty(title)) {
            wrapper.like("s.title", title);
        }
        String startDate = (String) innerMap.get("startDate");
        if (!StringUtils.isEmpty(startDate)) {
            wrapper.gt("r.gmt_create", startDate);
        }
        String endDate = (String) innerMap.get("endDate");
        if (!StringUtils.isEmpty(endDate)) {
            wrapper.lt("r.gmt_create", endDate);
        }
        String sendName = (String) innerMap.get("sendName");
        if (!StringUtils.isEmpty(sendName)) {
            wrapper.eq("r.send_name", sendName);
        }
        String sendCode = (String) innerMap.get("sendCode");
        if (!StringUtils.isEmpty(sendCode)) {
            wrapper.eq("s.send_code", sendCode);
        }
        Integer isRead = (Integer) innerMap.get("isRead");
        if (!StringUtils.isEmpty(isRead)) {
            wrapper.eq("r.is_read", isRead);
        }

        String keywords = (String) innerMap.get("keywords");
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.and(i -> i.like("s.title", keywords).or().like("s.send_name", keywords));
        }
        wrapper.orderByDesc("r.gmt_create");
        wrapper.eq("r.is_delete", 0);
        IPage<StationMessageVo> pages = new Query<StationMessageVo>().getPage(jsonObject);
        List<StationMessageVo> list = stationMessageMapper.findByCondition(pages, wrapper);
        pages.setRecords(list);
        return new PageUtils(pages);

    }

    /**
     * 查询供应商收件（用的是企业id）
     *
     * @param
     * @param
     * @return
     */
    @Override
    public PageUtils queryEnterprisePageList(JSONObject jsonObject, QueryWrapper<StationMessage> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        Integer receiveType = (Integer) innerMap.get("receiveType");
        String shopId = currentUser.getShopId();
        List<Shop> id = shopMapper.findId(currentUser.getShopId());
        wrapper.eq("r.receive_id", id.get(0).getEnterpriseId());
        wrapper.eq("r.mall_type", mallConfig.mallType);
        String title = (String) innerMap.get("title");
        if (!StringUtils.isEmpty(title)) {
            wrapper.like("s.title", title);
        }
        String startDate = (String) innerMap.get("startDate");
        if (!StringUtils.isEmpty(startDate)) {
            wrapper.gt("r.gmt_create", startDate);
        }
        String endDate = (String) innerMap.get("endDate");
        if (!StringUtils.isEmpty(endDate)) {
            wrapper.lt("r.gmt_create", endDate);
        }
        String sendName = (String) innerMap.get("sendName");
        if (!StringUtils.isEmpty(sendName)) {
            wrapper.eq("r.send_name", sendName);
        }
        String sendCode = (String) innerMap.get("sendCode");
        if (!StringUtils.isEmpty(sendCode)) {
            wrapper.eq("s.send_code", sendCode);
        }
        Integer isRead = (Integer) innerMap.get("isRead");
        if (!StringUtils.isEmpty(isRead)) {
            wrapper.eq("r.is_read", isRead);
        }
        String keywords = (String) innerMap.get("keywords");
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.and(i -> i.like("s.title", keywords).or().like("s.send_name", keywords));
        }
        wrapper.orderByDesc("r.gmt_create");
        wrapper.eq("r.is_delete", 0);
        IPage<StationMessageVo> pages = new Query<StationMessageVo>().getPage(jsonObject);
        List<StationMessageVo> list = stationMessageMapper.findByCondition(pages, wrapper);
        pages.setRecords(list);
        if (pages.getTotal() < pages.getPages() * pages.getCurrent()) {
            // 如果总记录数小于当前页的记录数量，切换到第一页
            pages.setCurrent(1);
        }
        return new PageUtils(pages);
    }

    /**
     * 检查供应商信息并发送相关到期提醒
     *
     * @param isSupplier  是否为供应商(1-是，0-否)
     * @param enterpriseId 企业ID
     */
    @Override
    public void checkSupplierInfo(Integer isSupplier, String enterpriseId) {
        // 非供应商直接返回，减少嵌套层级
        if (isSupplier == null || isSupplier != 1) {
            return;
        }

        // 参数校验，避免空指针
        if (StringUtils.isEmpty(enterpriseId)) {
            log.warn("检查供应商信息时企业ID为空");
            return;
        }

        Date currentTime = new Date();
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getById(enterpriseId);

        if (enterpriseInfo == null) {
            log.warn("未找到ID为{}的企业信息", enterpriseId);
            return;
        }

        // 发送营业执照到期提醒
        sendLicenseExpirationReminder(enterpriseInfo, currentTime);

        // 发送缴费到期提醒
        sendPaymentExpirationReminder(enterpriseInfo, currentTime);
    }

    /**
     * 发送营业执照到期提醒
     *
     * @param enterpriseInfo 企业信息
     * @param currentTime 当前时间
     */
    private void sendLicenseExpirationReminder(EnterpriseInfo enterpriseInfo, Date currentTime) {
        Date licenseTerm = enterpriseInfo.getLicenseTerm();
        // 营业执照有效期不为空且已过期
        if (licenseTerm != null && licenseTerm.before(currentTime)) {
            StationMessageReceiveVO message = createMessageVO(
                    enterpriseInfo.getEnterpriseId(),
                    "营业执照到期提醒",
                    String.format("尊敬的用户：您好！您的店铺营业执照即将到期。为了确保您的服务不受影响，并继续享受我们提供的优质保障，请您尽快处理。 您的营业执照到期时间：%s",
                            formatDate(licenseTerm))
            );
            this.createBatch(message);
        }
    }

    /**
     * 发送缴费到期提醒
     *
     * @param enterpriseInfo 企业信息
     * @param currentTime 当前时间
     */
    private void sendPaymentExpirationReminder(EnterpriseInfo enterpriseInfo, Date currentTime) {
        // 查询平台年度费用信息并校验
        PlatformYearFee platformYearFee = platformYearFeeService.getByEnterpriseId(enterpriseInfo.getEnterpriseId());
        if (platformYearFee == null) {
            log.warn("未查询到企业[{}]的平台年度费用信息", enterpriseInfo.getEnterpriseId());
            return;
        }

        LocalDate serveEndTime = platformYearFee.getServeEndTime();
        LocalDate currentLocalDate = currentTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 检查服务是否已到期（仅在到期时发送提醒）
        if (serveEndTime == null || serveEndTime.isBefore(currentLocalDate)) {
            StationMessageReceiveVO message = createMessageVO(
                    enterpriseInfo.getEnterpriseId(),
                    "店铺服务年费续费提醒",
                    String.format("尊敬的用户：您好！您的店铺服务年费即将到期。为了确保您的服务不受影响，并继续享受我们提供的优质保障，请您尽快续费。 您的店铺服务年费到期时间：%s",
                            formatLocalDate(serveEndTime))
            );
            this.createBatch(message);
        }
    }
    private String formatLocalDate(LocalDate date) {
        if (date == null ) return "";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return date.format(formatter);
    }

    /**
     * 创建消息VO对象
     *
     * @param enterpriseId ID
     * @param title 消息标题
     * @param content 消息内容
     * @return 消息VO对象
     */
    private StationMessageReceiveVO createMessageVO(String enterpriseId, String title, String content) {
        StationMessageReceiveVO message = new StationMessageReceiveVO();
        message.setEnterpriseIdList((ArrayList<String>)Collections.singletonList(enterpriseId));
        message.setTitle(title);
        message.setContent(content);
        return message;
    }

    /**
     * 格式化日期显示
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    private String formatDate(Date date) {
        if (date == null ) return "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    /**
     * 查询系统发件信息列表
     *
     * @param
     * @param
     * @return
     */
    public PageUtils queryPageListSystem(JSONObject jsonObject, QueryWrapper<StationMessage> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
//        Integer receiveType = (Integer) innerMap.get("receiveType");
        Integer receiveType = 0;
        if (receiveType == 0) {
            wrapper.eq("s.send_id", SYSTEM_NAME);
        } else if (receiveType == 1) {
            wrapper.eq("r.receive_id", currentUser.getUserId());
        }

        if (!StringUtils.isEmpty(receiveType)) {
        }
        String title = (String) innerMap.get("title");
        if (!StringUtils.isEmpty(title)) {
            wrapper.like("s.title", title);
        }
        String startDate = (String) innerMap.get("startDate");
        if (!StringUtils.isEmpty(startDate)) {
            wrapper.gt("s.send_date", startDate);
        }
        String endDate = (String) innerMap.get("endDate");
        if (!StringUtils.isEmpty(endDate)) {
            wrapper.lt("s.send_date", endDate);
        }
        String sendName = (String) innerMap.get("sendName");
        if (!StringUtils.isEmpty(sendName)) {
            wrapper.eq("r.send_name", sendName);
        }
        String sendCode = (String) innerMap.get("sendCode");
        if (!StringUtils.isEmpty(sendCode)) {
            wrapper.eq("s.send_code", sendCode);
        }
        Integer isRead = (Integer) innerMap.get("isRead");
        if (!StringUtils.isEmpty(isRead)) {
            wrapper.eq("r.is_read", isRead);
        }

        String keywords = (String) innerMap.get("keywords");
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.and(i -> i.like("s.title", keywords).or().like("s.send_name", keywords));
        }
        wrapper.orderByDesc("s.send_date");
        wrapper.eq("s.is_delete", 0);
        IPage<StationMessageVo> pages = new Query<StationMessageVo>().getPage(jsonObject);
        List<StationMessageVo> list = stationMessageMapper.findByCondition(pages, wrapper);
        pages.setRecords(list);
        if (pages.getTotal() < pages.getPages() * pages.getCurrent()) {
            // 如果总记录数小于当前页的记录数量，切换到第一页
            pages.setCurrent(1);
        }
        return new PageUtils(pages);
    }


    @Override
    public void create(StationMessage stationMessage) {

    }

    @Override
    public void update(StationMessage stationMessage) {
        super.updateById(stationMessage);
    }

    @Override
    public StationMessage getById(String id) {
        return null;
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createBatch(StationMessageReceiveVO vo) {
        //保存发件人信息(后台发件箱共享）
        UserLogin userLogin = ThreadLocalUtil.getCurrentUser();
        StationMessage stationMessage = new StationMessage();
        stationMessage.setSendId(BACK_OFFICE_ADMINISTRATOR_NAME);
        stationMessage.setSendCode(userLogin.getUserNumber());
        stationMessage.setSendName("物资采购平台");
        stationMessage.setMessageType(1);
        stationMessage.setTitle(vo.getTitle());
        stationMessage.setContent(vo.getContent());
        stationMessage.setSendDate(new Date());
        stationMessage.setAllRead(0);
        stationMessage.setRemind(vo.getRemind());
        super.save(stationMessage);
        if (vo.getFiles() != null && vo.getFiles().size() > 0) {
            fileService.deleteBatchFileByRelevanceIdAndType(stationMessage.getStationMessageId(), 7);
            for (File file : vo.getFiles()) {
                file.setMallType(mallConfig.mallType);
                file.setRelevanceType(7);
                file.setRelevanceId(stationMessage.getStationMessageId());
                fileService.create(file);
            }
        }
        vo.setStationMessageId(stationMessage.getStationMessageId());
        //保存收件人信息
        stationMessageReceiveService.addBath(vo);

//        if (vo.getReceiveType()==1){
//            List<User> userList = userService.listByIds(receiveList);
//            for (User userinfo : userList) {
//                StationMessageReceive stationMessageReceive = new StationMessageReceive();
//                stationMessageReceive.setReceiveId(userinfo.getUserId());
//                stationMessageReceive.setReceiveName(userinfo.getNickName());
//                stationMessageReceive.setReceiveType(vo.getReceiveType());
//                stationMessageReceive.setIsRead(0);
//                stationMessageReceive.setStationMessageReceiveId(userinfo.getRealName());
//                stationMessageReceive.setFounderId(userinfo.getUserId());
//                stationMessageReceive.setReceiveCode(userinfo.getUserNumber());
//                stationMessageReceive.setStationMessageId(stationMessage.getStationMessageId());
//                stationMessageReceiveService.save(stationMessageReceive);
//            }
//
//        }else {
//            List<Shop> shops = shopService.listByIds(receiveList);
//            for (Shop shop : shops) {
//                StationMessageReceive stationMessageReceive = new StationMessageReceive();
//                stationMessageReceive.setReceiveId(shop.getShopId());
//                stationMessageReceive.setReceiveName(shop.getShopName());
//                stationMessageReceive.setReceiveType(vo.getReceiveType());
//                stationMessageReceive.setIsRead(0);
//                stationMessageReceive.setFounderId(user.getUserId());
//                stationMessageReceive.setReceiveCode(user.getUserNumber());
//                stationMessageReceive.setStationMessageId(stationMessage.getStationMessageId());
//                stationMessageReceiveService.save(stationMessageReceive);
//            }

        //   }


    }

    //        ArrayList<String> receiveList = vo.getReceiveList();
//        ArrayList<StationMessageReceive> stationMessageReceives = new ArrayList<>();
//        for (String userId : receiveList) {
//            StationMessageReceive stationMessageReceive = new StationMessageReceive();
//
//
//
//
//        }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSystemBatch(StationMessageReceiveVO vo) {
        //保存发件人信息(后台发件箱共享）
        StationMessage stationMessage = new StationMessage();
        stationMessage.setSendId(BACK_OFFICE_ADMINISTRATOR_NAME);
        stationMessage.setSendCode("111111");
        stationMessage.setSendName("物资采购平台");
        stationMessage.setMessageType(1);
        stationMessage.setTitle(vo.getTitle());
        stationMessage.setContent(vo.getContent());
        stationMessage.setSendDate(new Date());
        stationMessage.setAllRead(0);
        stationMessage.setRemind(vo.getRemind());
        super.save(stationMessage);
        vo.setStationMessageId(stationMessage.getStationMessageId());
        //保存收件人信息
        stationMessageReceiveService.addBath(vo);
    }
}
