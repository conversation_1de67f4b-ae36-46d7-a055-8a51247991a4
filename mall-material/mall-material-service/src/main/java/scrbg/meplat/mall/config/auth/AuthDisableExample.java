package scrbg.meplat.mall.config.auth;

import org.springframework.stereotype.Component;

/**
 * 权限禁用配置示例
 * 展示如何禁用项目的权限验证功能
 */
@Component
public class AuthDisableExample {

    /**
     * 方案1：通过配置文件禁用权限（推荐）
     * 
     * 在 application.yml 中添加以下配置：
     * 
     * mall:
     *   auth:
     *     enabled: false              # 完全禁用权限验证
     *     loginEnabled: false         # 禁用登录验证
     *     roleEnabled: false          # 禁用角色权限验证
     *     tokenInterceptorEnabled: false # 禁用Token拦截器
     * 
     * 配置说明：
     * - enabled: false - 完全禁用所有权限验证
     * - loginEnabled: false - 只禁用登录验证，保留角色权限验证
     * - roleEnabled: false - 只禁用角色权限验证，保留登录验证
     * - tokenInterceptorEnabled: false - 禁用Token拦截器
     */
    public void configurationExample() {
        System.out.println("=== 配置文件方式禁用权限 ===");
        System.out.println("优点：");
        System.out.println("1. 可以通过配置文件灵活控制");
        System.out.println("2. 不需要修改代码");
        System.out.println("3. 可以分别控制不同类型的权限验证");
        System.out.println("4. 便于在不同环境中使用不同配置");
    }

    /**
     * 方案2：注释拦截器配置（临时方案）
     * 
     * 在 ServerConfigurerAdapter.java 中注释掉拦截器配置：
     * 
     * @Override
     * public void addInterceptors(InterceptorRegistry registry) {
     *     // 注释掉以下代码即可禁用Token验证
     *     /*
     *     registry.addInterceptor(checkTokenInterceptor)
     *             .addPathPatterns("/**")
     *             .excludePathPatterns("/doc.html","/webjars/**","/swagger-resources","/error","/favicon.ico");
     *     *\/
     * }
     */
    public void interceptorExample() {
        System.out.println("=== 拦截器注释方式禁用权限 ===");
        System.out.println("优点：");
        System.out.println("1. 简单直接");
        System.out.println("2. 完全禁用Token验证");
        System.out.println("缺点：");
        System.out.println("1. 需要修改代码");
        System.out.println("2. 不够灵活");
    }

    /**
     * 方案3：注释AOP配置（针对注解权限）
     * 
     * 在 IncludeAuthAOP.java 中注释掉权限验证逻辑：
     * 
     * @Around("@annotation(scrbg.meplat.mall.config.auth.IsLogin)")
     * public Object isLoginM(ProceedingJoinPoint pjp) throws Throwable {
     *     // 直接放行，不进行权限验证
     *     return pjp.proceed();
     * }
     */
    public void aopExample() {
        System.out.println("=== AOP注释方式禁用权限 ===");
        System.out.println("适用场景：");
        System.out.println("1. 禁用 @IsLogin 注解验证");
        System.out.println("2. 禁用 @IsRole 注解验证");
        System.out.println("3. 保留Token拦截器，只禁用注解权限");
    }

    /**
     * 当前项目的权限架构分析
     */
    public void authArchitectureAnalysis() {
        System.out.println("=== 项目权限架构分析 ===");
        System.out.println("1. Token拦截器 (CheckTokenInterceptor):");
        System.out.println("   - 验证请求头中的token");
        System.out.println("   - 调用PCWP系统验证token有效性");
        System.out.println("   - 将用户信息存储到ThreadLocal");
        System.out.println("");
        
        System.out.println("2. 登录验证AOP (@IsLogin):");
        System.out.println("   - 检查ThreadLocal中是否有用户信息");
        System.out.println("   - 用于方法级别的登录验证");
        System.out.println("");
        
        System.out.println("3. 角色权限验证AOP (@IsRole):");
        System.out.println("   - 检查用户是否具有指定角色");
        System.out.println("   - 用于方法级别的角色权限验证");
        System.out.println("");
        
        System.out.println("4. 权限验证AOP (@RequestPermission):");
        System.out.println("   - 检查用户是否具有指定权限");
        System.out.println("   - 用于细粒度的权限控制");
    }

    /**
     * 推荐的禁用权限方案
     */
    public void recommendedSolution() {
        System.out.println("=== 推荐方案 ===");
        System.out.println("开发测试环境：");
        System.out.println("1. 使用配置文件方式：mall.auth.enabled=false");
        System.out.println("2. 或者注释拦截器配置");
        System.out.println("");
        
        System.out.println("生产环境：");
        System.out.println("1. 保持权限验证开启：mall.auth.enabled=true");
        System.out.println("2. 根据需要配置具体的权限验证项");
        System.out.println("");
        
        System.out.println("当前配置状态：");
        System.out.println("- 拦截器已被注释，Token验证已禁用");
        System.out.println("- AOP权限验证可通过配置文件控制");
        System.out.println("- 配置文件中已设置 mall.auth.enabled=false");
    }

    /**
     * 恢复权限验证的方法
     */
    public void restoreAuthExample() {
        System.out.println("=== 恢复权限验证 ===");
        System.out.println("1. 修改配置文件：");
        System.out.println("   mall.auth.enabled: true");
        System.out.println("   mall.auth.loginEnabled: true");
        System.out.println("   mall.auth.roleEnabled: true");
        System.out.println("");
        
        System.out.println("2. 取消注释拦截器配置：");
        System.out.println("   在 ServerConfigurerAdapter.java 中取消注释拦截器配置");
        System.out.println("");
        
        System.out.println("3. 重启应用即可恢复权限验证");
    }
}
