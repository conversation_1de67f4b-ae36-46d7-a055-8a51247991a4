package scrbg.meplat.mall.mapper.system;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface RoleMenu2Mapper {
    int deleteByRoleId(String roleId, String modifyTime, String modifyUserId, String modifyUserName);
    int batchInsertRoleMenus(String roleId, List<String> menuIds, String gmtCreate, String founderId, String founderName);
    List<String> selectMenuIdsByRoleId(String roleId);
}
