package scrbg.meplat.mall.service.system.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.system.SysSyslist;
import scrbg.meplat.mall.mapper.system.SysSyslistMapper;
import scrbg.meplat.mall.service.system.SysSyslistService;

import java.util.Collections;
import java.util.List;

@Service
public class SysSyslistServiceImpl implements SysSyslistService {

    @Autowired
    private SysSyslistMapper sysSyslistMapper;

    @Override
    public List<SysSyslist> getAllSysSyslist() {
        return sysSyslistMapper.selectAll();
    }
}
