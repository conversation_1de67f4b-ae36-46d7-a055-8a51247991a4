package scrbg.meplat.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.dto.TheFirstLoginInfo;
import scrbg.meplat.mall.entity.UserAddress;
import scrbg.meplat.mall.service.PurchaseService;
import scrbg.meplat.mall.service.UserAddressService;
import scrbg.meplat.mall.util.ThreadLocalUtil;

import java.util.List;

@Log4j
@Service
public class PurchaseServiceImpl implements PurchaseService {

    @Autowired
    public UserAddressService userAddressService;

    @Override
    public void theFirstLogin(TheFirstLoginInfo info) {

        //新增收货地址
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        UserAddress userAddress = new UserAddress();
        BeanUtils.copyProperties(info,userAddress);
        userAddress.setUserId(userId);
        userAddress.setEnterpriseId(enterpriseId);
        userAddressService.create(userAddress, new LambdaQueryWrapper<>());

        //设置默认地址
        QueryWrapper<UserAddress> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("gmt_create");
        queryWrapper.eq("enterprise_id",enterpriseId);
        queryWrapper.eq("user_id",userId);
        List<UserAddress> userAddresses = userAddressService.list(queryWrapper);
        if (userAddresses.size()>0){
            UserAddress address = userAddresses.get(0);
            address.setUserId(userId);
            userAddressService.setDefaultAddress(userAddress, new LambdaQueryWrapper<>());
        }

    }
}
