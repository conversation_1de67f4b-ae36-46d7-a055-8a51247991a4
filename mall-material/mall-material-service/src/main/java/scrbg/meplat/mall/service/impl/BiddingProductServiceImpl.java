package scrbg.meplat.mall.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import scrbg.meplat.mall.entity.BiddingProduct;
import scrbg.meplat.mall.entity.SynthesizeTemporary;
import scrbg.meplat.mall.entity.SynthesizeTemporaryDtl;
import scrbg.meplat.mall.mapper.BiddingProductMapper;
import scrbg.meplat.mall.service.BiddingProductService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.service.SynthesizeTemporaryDtlService;
import scrbg.meplat.mall.service.SynthesizeTemporaryService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @描述：竞价商品信息 服务类
 * @作者: ye
 * @日期: 2023-07-11
 */
@Service
public class BiddingProductServiceImpl extends ServiceImpl<BiddingProductMapper, BiddingProduct> implements BiddingProductService{

    @Autowired
    private SynthesizeTemporaryService synthesizeTemporaryService;
    @Autowired
    private SynthesizeTemporaryDtlService synthesizeTemporaryDtlService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingProduct> queryWrapper) {
        IPage<BiddingProduct> page = this.page(
        new Query<BiddingProduct>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(BiddingProduct biddingProduct) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingProduct);
    }

    @Override
    public void update(BiddingProduct biddingProduct) {
        super.updateById(biddingProduct);
    }


    @Override
    public BiddingProduct getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public List<BiddingProduct> selectBidingPurchaseByBidingId(String bidingId) {
        LambdaQueryWrapper<BiddingProduct> q = new LambdaQueryWrapper<>();
        q.eq(BiddingProduct::getBiddingId,bidingId);
        List<BiddingProduct> list = list(q);
        return list;

    }

    @Override
    public List<BiddingProduct> getBiddingProductByStSn(String stSn) {
        SynthesizeTemporary one = synthesizeTemporaryService.lambdaQuery().eq(SynthesizeTemporary::getSynthesizeTemporarySn, stSn).one();//大宗清单
        String stId = one.getSynthesizeTemporaryId();
        List<BiddingProduct> vo = new ArrayList<>();
        List<SynthesizeTemporaryDtl> list = synthesizeTemporaryDtlService.lambdaQuery().eq(SynthesizeTemporaryDtl::getSynthesizeTemporaryId, stId).list();//大宗清单商品明细
        for(SynthesizeTemporaryDtl sTDtl : list){
            //String synthesizeTemporaryDtlId = sTDtl.getSynthesizeTemporaryDtlId();
            //List<BiddingProduct> bp = lambdaQuery().eq(BiddingProduct::getSynthesizeTemporaryDtlId, synthesizeTemporaryDtlId).list();
            List<BiddingProduct> bp = lambdaQuery().eq(BiddingProduct::getProductSn, sTDtl.getProductSn()).list();
            vo.addAll(bp);
        }
        return vo;
    }

    @Override
    public void updateBiddingProductPrice(String id, BigDecimal price, Integer state) {
        if(state == 1){
            lambdaUpdate().eq(BiddingProduct::getBiddingProductId,id).set(BiddingProduct::getNewPrice,price)
                    .set(BiddingProduct::getIsUpdatePrice,1).set(BiddingProduct::getPriceTime,new Date()).update();
        }
        if(state == 2){
            lambdaUpdate().eq(BiddingProduct::getBiddingProductId,id)
                    .set(BiddingProduct::getIsUpdatePrice,2).update();
        }
    }
}
