package scrbg.meplat.mall.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.dto.user.OrgAndSon;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2022-12-21 14:36
 */
@Data
public class LoginVO {

    /**
     * 登陆返回
     */
    @ApiModelProperty(value = "本地用户id")
    private String userId;

    @ApiModelProperty(value = "手机号码")
    private String userMobile;

    @ApiModelProperty(value = "远程用户id")
    private String farUserId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "原始用户名称")
    private String originalUserName;
    @ApiModelProperty(value = "用户编号")
    private String userNumber;

    @ApiModelProperty(value = "远程token")
    private String token;

    @ApiModelProperty(value = "当前组织统一社会信用代码")
    private String socialCreditCode;


    /**
     * 权限列表
     */

    @ApiModelProperty(value = "是否有电商管理权限（0否1是）")
    private Integer isShpAuthority;

    @ApiModelProperty(value = "是否有二手设备回收运营中心权限（0否1是）")
    private Integer isDeviceRecycle;

    @ApiModelProperty(value = "是否有物流运营中心权限（0否1是）")
    private Integer isMaterialFlow;

    @ApiModelProperty(value = "是否是外部用户（0否1是）")
    private Integer isExternal;

    @ApiModelProperty(value = "是否是内部用户（0否1是）")
    private Integer isInterior;

    @ApiModelProperty(value = "是否有下单权限（0否1是）")
    private Integer isSubmitOrder;

    @ApiModelProperty(value = "是否有月供审核权限（0否1是）")
    private Integer isMonthPlanAudit;

    @ApiModelProperty(value = "所有角色列表（pcwp角色）")
    private List<String> roles;


    @ApiModelProperty(value = "是否为pcwp内部供应商（外部供应商）：0否1是")
    private Integer isPcwp;
    /**
     * 本地id、机构id
     */

    @ApiModelProperty(value = "当前本地组织id")
    private String LocalOrgId;
    /**
     * 调用远程查询
     */
    @ApiModelProperty(value = "组织列表")
    private List<OrganizationVO> organizationVOS;

    @ApiModelProperty(value = "当前组织名称")
    private String orgName;

    @ApiModelProperty(value = "当前组织id")
    private String orgId;

    @ApiModelProperty(value = "当前组织number")
    private String orgNumber;

    @ApiModelProperty(value = "当前机构id以及子机构id（远程）")
    private List<String> orgIds;

    @ApiModelProperty(value = "当前机构以及子机构（对象）")
    private List<OrgAndSon> orgAndSon;



    @ApiModelProperty(value = "是否是供应商（0否1是）")
    private Integer isSupplier;

    @ApiModelProperty(value = "当前组织完整信息（用户前端接口请求头使用）")
    private Map orgInfo;


    /**
     * 判断
     */
//    @ApiModelProperty(value = "是否普通用户（0否1是）")
//    private Integer isOrdinary;

    @ApiModelProperty(value = "是否运营平台管理者（0否1是）")
    private Integer isPlatformAdmin;

//    @ApiModelProperty(value = "是否店铺管理者（0否1是）")
//    private Integer isShopManage;

    @ApiModelProperty(value = "店铺id")
    private String shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;
    @ApiModelProperty(value = "店铺类型 0：个体户  1：企业  2：个人")
    private Integer shopType;

    @ApiModelProperty(value = "企业类型：0：个体户  1：企业  2：个人")
    private Integer enterpriseType;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "是否有招标权限（0否1是）")
    private Integer isTender;

    @ApiModelProperty(value = "是否有纪检权限（0否1是）")
    private Integer isCheck;

    @ApiModelProperty(value = "其他服务权限")
    private Map<String, Integer> isOtherAuth;


    @ApiModelProperty(value = "是否首次登录 1 是 0 否")
    private Integer firstLogin;


    @ApiModelProperty(value = "角色菜单")
    private List<MallRole> mallRoles;


    @ApiModelProperty(value = "角色菜单大类")
    private Map<String,Object> mapSysMenu;


    @ApiModelProperty(value = "登陆店铺/招标到期提示map")
    private Map<String,String> diaLogMap;
    @ApiModelProperty(value = "是否登陆提示招标")
    private Integer isShowBid;

    @ApiModelProperty(value = "拥有的菜单集合")
    private List<String> menus;

    @ApiModelProperty(value = "注册状态（0.注册 1.平台初审 2.申请开店 3.合同签约及缴费 4.平台复审 5.完成）")
    private String zcstate;

    @ApiModelProperty(value = "企业id")
    private String enterpriseId;


    private Set<String> permissions;//角色业务操作权限
}
