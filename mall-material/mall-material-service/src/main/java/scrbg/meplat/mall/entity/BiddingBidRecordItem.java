package scrbg.meplat.mall.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：竞价记录
 * @作者: ye
 * @日期: 2023-07-19
 */
@ApiModel(value = "竞价记录")
@Data
@TableName("bidding_bid_record_item")
public class BiddingBidRecordItem extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "竞价记录明细id")
    private String bidRecordItemId;

    @ApiModelProperty(value = "竞价记录id")

    private String bidRecordId;


    @ApiModelProperty(value = "竞价采购商品id")

    private String biddingProductId;


    @ApiModelProperty(value = "竞价采购id")

    private String biddingId;


    @ApiModelProperty(value = "不含税到场单价")

    private BigDecimal bidPrice;


    @ApiModelProperty(value = "税率")

    private BigDecimal taxRate;


    @ApiModelProperty(value = "含税到场单价")

    private BigDecimal bidRatePrice;


    @ApiModelProperty(value = "含税总金额")

    private BigDecimal bidRateAmount;


    @ApiModelProperty(value = "不含税总金额")

    private BigDecimal bidAmount;


    @ApiModelProperty(value = "竞价时间")

    private Date bidTime;


    @ApiModelProperty(value = "驳回原因")

    private String rejectReason;


    @ApiModelProperty(value = "状态")

    private Integer state;


    @ApiModelProperty(value = "网价（浮动价格使用）")

    private BigDecimal netPrice;

    @ApiModelProperty(value = "固定费用（浮动价格使用）")

    private BigDecimal fixationPrice;

    @ApiModelProperty(value = "出厂价（固定价格使用）")

    private BigDecimal outFactoryPrice;

    @ApiModelProperty(value = "运杂费（固定价格使用）")

    private BigDecimal transportPrice;


    private BigDecimal monthlyDifference;

    /**
     * 含税拟销售单价
     */
    private  BigDecimal unitPriceIncludingTax;

    /**
     * 含税拟销售金额
     */
    private BigDecimal taxInclusiveAmount;

    @ApiModelProperty(value = "修改后的竞价价格")
    private BigDecimal newPrice;

    @ApiModelProperty(value = "价格是否修改 1已修改 0未修改 2修改审批完成")
    private Integer isUpdatePrice;

    @ApiModelProperty(value = "价格更新时间")
    private Date priceTime;


}