package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.order.OrderShipmentsQtyIsOkDTO;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.OrderShipDtlMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.TaxCalculator;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.util.pageUtils;
import scrbg.meplat.mall.vo.platform.ListShipByAffirmListVO;
import scrbg.meplat.mall.vo.platform.ShopListShipByAffirmListVO;
import scrbg.meplat.mall.vo.ship.MaterialShipDtlVo;
import scrbg.meplat.mall.vo.ship.ReconcilableMaterialVO;
import scrbg.meplat.mall.dto.ship.ReconcilableMaterialDTO;
import scrbg.meplat.mall.vo.shopManage.reportForms.PlatformProductFromVo;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-05-22
 */
@Service
public class OrderShipDtlServiceImpl extends ServiceImpl<OrderShipDtlMapper, OrderShipDtl> implements OrderShipDtlService {
    @Autowired
    OrderShipService orderShipService;
    @Autowired
    OrderItemService orderItemService;

    @Autowired
    OrdersService ordersService;
    @Autowired
    OrderReturnItemService orderReturnItemService;

    @Autowired
    MallConfig mallConfig;

    @Autowired
    PcwpOrgService pcwpOrgService;


    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderShipDtl> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String billId = (String) innerMap.get("billId");
        String keywords = (String) innerMap.get("keywords");
        if (keywords != null) {
            q.like(OrderShipDtl::getProductName, keywords);
        }
        if (billId != null) {
            q.eq(OrderShipDtl::getBillId, billId);
        }

        IPage<OrderShipDtl> page = this.page(
                new Query<OrderShipDtl>().getPage(jsonObject),
                q
        );
        List<OrderShipDtl> records = page.getRecords();
        if (records != null && records.size() > 0) {
            for (OrderShipDtl record : records) {
                OrderItem byId = orderItemService.getById(record.getOrderItemId());
                if (byId == null) {
                    throw new BusinessException(304, "订单不存在");
                }
                BigDecimal buyCounts = byId.getBuyCounts();
                if (byId.getProductType() == 12 || byId.getProductType() == 13) {
                    buyCounts = byId.getBuyCounts().add(byId.getBuyCounts().multiply(BigDecimal.valueOf(0.1)));
                    record.setRelevanceName(byId.getProductType() == 13 ? byId.getRelevanceName() : byId.getProductName());
                } else {
                    record.setRelevanceName(byId.getProductName());
                }
                BigDecimal maxShipCounts = buyCounts.subtract(byId.getReturnCounts()).subtract(byId.getShipCounts().subtract(record.getShipCounts())).add(byId.getPcwpReturn());
                record.setMaxShipCounts(maxShipCounts);
                if (byId.getBuyCounts().compareTo(byId.getShipCounts()) < 0 && byId.getShipCounts().compareTo(buyCounts) < 0) {
                    record.setUnShipCounts(BigDecimal.valueOf(0));
                } else {
                    record.setUnShipCounts(byId.getBuyCounts().subtract(byId.getShipCounts()));
                }
            }

        }
        return new PageUtils(page);
    }

    @Override
    public void create(OrderShipDtl orderShipDtl) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(orderShipDtl);
    }

    @Override
    public void update(OrderShipDtl orderShipDtl) {
        super.updateById(orderShipDtl);
    }


    @Override
    public OrderShipDtl getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public PageUtils purchaseShipDtl(JSONObject jsonObject, LambdaQueryWrapper<OrderShipDtl> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String billId = (String) innerMap.get("billId");
        if (billId != null) {
            q.eq(OrderShipDtl::getBillId, billId);
        }
        IPage<OrderShipDtl> page = this.page(
                new Query<OrderShipDtl>().getPage(jsonObject),
                q
        );
        List<OrderShipDtl> records = page.getRecords();
        if (records != null && records.size() > 0) {
            for (OrderShipDtl record : records) {
                OrderItem byId = orderItemService.getById(record.getOrderItemId());
                if (byId == null) {
                    throw new BusinessException(304, "订单不存在");

                }
                record.setUnShipCounts(byId.getBuyCounts().subtract(byId.getShipCounts()));
                record.setMaxShipCounts(record.getTotalSum().subtract(record.getUnShipCounts()));
            }
        }
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShipCountsByDtlIds(List<OrderShipDtl> dtls) {
        //发货单现在金额
        BigDecimal nowTotalAmoney = BigDecimal.valueOf(0.0);
        BigDecimal nowNoRateAmoney = BigDecimal.valueOf(0.0);
        //1.查询发货单情况
        OrderShip orderShip = orderShipService.getById(dtls.get(0).getBillId());
        if (orderShip.getType() == 2) {
            throw new BusinessException(500, "供应商已经收货，不能修改发货单");
        }
        ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
        //2 判断收货单类型 1大宗月供合同  2 零星采购计划，6 大宗临购
        if (orderShip.getSourceType() == 1) {
            //判断是否订单项超量
            ArrayList<OrderShipmentsQtyIsOkDTO> list = new ArrayList<>();
            for (OrderShipDtl dtl : dtls) {
                //订单项
                OrderShipmentsQtyIsOkDTO orderShipmentsQtyIsOkDTO = new OrderShipmentsQtyIsOkDTO();
                //获取原来的发货项数据
                OrderShipDtl one = getById(dtl.getDtlId());
                OrderItem byId = orderItemService.getById(one.getOrderItemId());
                if (byId.getParentOrderItemId() != null) {
                    orderShipmentsQtyIsOkDTO.setOrderItemId(byId.getParentOrderItemId());
                } else {
                    orderShipmentsQtyIsOkDTO.setOrderItemId(one.getOrderItemId());
                }
                orderShipmentsQtyIsOkDTO.setDtlId(dtl.getDtlId());
                orderShipmentsQtyIsOkDTO.setUpdate(true);
                orderShipmentsQtyIsOkDTO.setQty(dtl.getShipCounts());
                list.add(orderShipmentsQtyIsOkDTO);
            }
            orderItemService.orderShipmentsQtyIsOkYG(list);

        } else if (orderShip.getSourceType() == 6) {
            ArrayList<OrderShipmentsQtyIsOkDTO> list = new ArrayList<>();
            for (OrderShipDtl dtl : dtls) {
                OrderShipmentsQtyIsOkDTO orderShipmentsQtyIsOkDTO = new OrderShipmentsQtyIsOkDTO();
                OrderItem byId = orderItemService.getById(dtl.getOrderItemId());
                if (byId.getParentOrderItemId() != null) {
                    orderShipmentsQtyIsOkDTO.setOrderItemId(byId.getParentOrderItemId());
                } else {
                    orderShipmentsQtyIsOkDTO.setOrderItemId(dtl.getOrderItemId());
                }
                orderShipmentsQtyIsOkDTO.setQty(dtl.getShipCounts());
                orderShipmentsQtyIsOkDTO.setDtlId(dtl.getDtlId());
                orderShipmentsQtyIsOkDTO.setUpdate(true);
                list.add(orderShipmentsQtyIsOkDTO);
            }
            orderItemService.orderShipmentsQtyIsOkLG(list);
        }


        ArrayList<OrderShipmentsQtyIsOkDTO> okDtlList = new ArrayList<>();
        for (OrderShipDtl dtl : dtls) {
            OrderShipDtl oldDtl = getById(dtl.getDtlId());
            oldDtl.setShipNum(dtl.getShipCounts());
            if (orderShip.getType() == 2) {
                throw new BusinessException(301, "发货单已经确认，不能更改数量");
            } else {
                OrderShipmentsQtyIsOkDTO okDTO = new OrderShipmentsQtyIsOkDTO();
                //查询订单的购买数量
                OrderItem orderItem = orderItemService.getById(oldDtl.getOrderItemId());
                if (orderItem.getParentOrderItemId() != null) {
                    okDTO.setOrderItemId(orderItem.getParentOrderItemId());
                } else {
                    okDTO.setOrderItemId(orderItem.getOrderItemId());
                }
                okDTO.setQty(dtl.getShipCounts().subtract(oldDtl.getShipCounts()));
                okDtlList.add(okDTO);
                //查询商城退货数量
                BigDecimal returnCount = orderReturnItemService.getDataByOrderItmId(orderItem.getOrderItemId(), 0);
                BigDecimal buyCounts = orderItem.getBuyCounts();
                if (orderShip.getSourceType() == 1) {
                    buyCounts = buyCounts.add(buyCounts.multiply(BigDecimal.valueOf(0.1)));

                } else if (orderShip.getSourceType() == 6) {
                    buyCounts = buyCounts.add(buyCounts.multiply(BigDecimal.valueOf(0.1)));
                }


                //订单现在可以发货数量
                BigDecimal shipCount = buyCounts.subtract(returnCount).add(oldDtl.getShipCounts()).subtract(orderItem.getShipCounts());

                if (dtl.getShipCounts().compareTo(shipCount) <= 0) {
                    //订单现在发货数量
                    BigDecimal nowShipCount = orderItem.getShipCounts().subtract(oldDtl.getShipCounts()).add(dtl.getShipCounts());
                    orderItem.setShipCounts(nowShipCount);
                    //现在发货单项发货数量
                    oldDtl.setShipCounts(dtl.getShipCounts());
                    //大宗没有金额
                    if (orderShip.getSourceType() != 1) {
                        //更新发货单项金额
//                        BigDecimal amount = oldDtl.getTotalAmount();
//                        BigDecimal noRateAmount = oldDtl.getNoRateAmount();
                        //orderItem已经改变发货数量
                        updateAmount(orderItem, oldDtl, oldDtl.getShipCounts());
                        nowTotalAmoney = nowTotalAmoney.add(oldDtl.getTotalAmount());
                        nowNoRateAmoney = nowNoRateAmoney.add(oldDtl.getNoRateAmount());
                    }
                    orderItemService.updateById(orderItem);
                    shipDtls.add(oldDtl);

                } else {
                    throw new BusinessException(500, "修改数量大于发货数量，不能修改");
                }

            }

        }
        if (shipDtls.size() > 0) {
            updateBatchById(shipDtls);
        }
//        if (orderShip.getSourceType() == 1) {
//            orderItemService.orderShipmentsQtyIsOkYG(okDtlList);
//        } else if (orderShip.getSourceType() == 6) {
//            orderItemService. orderShipmentsQtyIsOkLG(okDtlList);
//        }
        orderShip.setNoRateAmount(orderShip.getNoRateAmount().add(nowNoRateAmoney));
        orderShip.setRateAmount(orderShip.getRateAmount().add(nowTotalAmoney));
        orderShipService.update(orderShip);
    }

    private BigDecimal getShipNum(OrderItem byId) {
        //超量
        BigDecimal add = byId.getBuyCounts().add(byId.getBuyCounts().multiply(BigDecimal.valueOf(0.1)));
        //可发货数量=超量-发货数量-商城退货数量+pcwp退货数量
        return add.subtract(byId.getShipCounts()).add(byId.getPcwpReturn()).subtract(byId.getReturnCounts());
    }

    @Override
    public List<OrderShipDtl> getDataByOrderShipId(String billId) {
        LambdaQueryWrapper<OrderShipDtl> wrapper = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<OrderShipDtl> eq = wrapper.eq(OrderShipDtl::getBillId, billId);
        List<OrderShipDtl> list = list(eq);
        for (OrderShipDtl orderShipDtl : list) {
            OrderItem byId = orderItemService.getById(orderShipDtl.getOrderItemId());
            if (byId.getProductType() != 12) {
                orderShipDtl.setRelevanceName(byId.getRelevanceName());
            } else {
                orderShipDtl.setRelevanceName(byId.getProductName());
            }
        }
        return list;
    }

    @Override
    public List<OrderShipDtl> getListByOrderId(String orderId) {
        LambdaQueryWrapper<OrderShipDtl> q = new LambdaQueryWrapper<>();
        q.eq(OrderShipDtl::getOrderId, orderId);
        List<OrderShipDtl> list = list(q);
        return list;
    }

    /**
     * 查询物资交易量信息
     *
     * @param jsonObject
     * @return
     */
    @Override
    public PageUtils listByAffirmList(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String endFinishDate = (String) innerMap.get("endFinishDate");
        String startFinishDate = (String) innerMap.get("startFinishDate");
        String orderSn = (String) innerMap.get("orderSn");
        String shopName = (String) innerMap.get("shopName");
        String productName = (String) innerMap.get("productName");
        Integer productType = (Integer) innerMap.get("productType");
        String sortCode = (String) innerMap.get("sortCode");
        PcwpOrg bySortCode = pcwpOrgService.getBySortCode(sortCode);
        if(bySortCode != null){
            jsonObject.put("sortCode",bySortCode.getSortcode());
            jsonObject.put("orgId",sortCode);
        }
        int count = baseMapper.listByAffirmListCount(jsonObject.getInnerMap());
        pageUtils.pageDispose(jsonObject, count);
        Page<ListShipByAffirmListVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ListShipByAffirmListVO> vos = baseMapper.listByAffirmList(pages, jsonObject);
        BigDecimal CountAmount = baseMapper.selCountAmount(jsonObject);
        BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(jsonObject);
//        if (io.seata.common.util.CollectionUtils.isNotEmpty(vos)) {
//            BigDecimal bigDecimal = new BigDecimal(0);
//            for (ListShipByAffirmListVO vo : vos) {
//                bigDecimal = bigDecimal.add(vo.getAmount());
//            }
//            vos.get(0).setCountAmount(bigDecimal);
//        }
        if (vos.size() > 0) {
            vos.get(0).setCountAmount(CountAmount);
            vos.get(0).setCountNoRateAmount(countNoRateAmount);
        }
        pages.setRecords(vos);
        return new PageUtils(pages);
    }
    @Override
    public PageUtils listShipByAffirmOrgList(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String endFinishDate = (String) innerMap.get("endFinishDate");
        String startFinishDate = (String) innerMap.get("startFinishDate");
        String orderSn = (String) innerMap.get("orderSn");
        String shopName = (String) innerMap.get("shopName");
        String productName = (String) innerMap.get("productName");
        Integer productType = (Integer) innerMap.get("productType");
        String sortCode = (String) innerMap.get("sortCode");
        List<PcwpOrg> orgs = new ArrayList<>();
        if(StringUtils.isEmpty(sortCode)) {
            orgs = pcwpOrgService.getTopOrg();// 四川路桥
            if(orgs.isEmpty()){
                throw new BusinessException(500, "机构错误");
            }
        }else {
            PcwpOrg byId = pcwpOrgService.getBySortCode(sortCode);
            if(byId == null) {
                throw new BusinessException(500, "请选择正确的机构");
            }
            orgs = pcwpOrgService.getChildren(byId.getId());
        }
        // 获取分页参数
        int currentPage = (Integer) jsonObject.get("page");
        int pageSize = (Integer) jsonObject.get("limit");

        // 计算分页起始位置
        int startIndex = (currentPage - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, orgs.size());
        Page<ListShipByAffirmListVO> pages = new Page<>(currentPage, pageSize);
        pages.setTotal(orgs.size());
        List<ListShipByAffirmListVO> vos = new ArrayList<>();
        // 只处理当前页的数据
        for (int i = startIndex; i < endIndex; i++) {
            PcwpOrg pcwpOrg = orgs.get(i);
            ListShipByAffirmListVO listShipByAffirmListVO = new ListShipByAffirmListVO();
            listShipByAffirmListVO.setOrglayertypenumber(pcwpOrg.getOrglayertypenumber());
            listShipByAffirmListVO.setSortCode(pcwpOrg.getId());
            listShipByAffirmListVO.setNumber(BigDecimal.ZERO);
            listShipByAffirmListVO.setReturnCounts(BigDecimal.ZERO);
            listShipByAffirmListVO.setEnterpriseName(pcwpOrg.getName());
            listShipByAffirmListVO.setProductType(productType);


            // 创建新的JSONObject用于当前查询，避免参数污染
            JSONObject orgJsonObject = new JSONObject();
            orgJsonObject.putAll(innerMap); // 复制原始参数
            orgJsonObject.put("sortCode", pcwpOrg.getSortcode());

            // 处理可能为null的BigDecimal值
            BigDecimal countAmount = baseMapper.selCountAmount(orgJsonObject);
            BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(orgJsonObject);
            listShipByAffirmListVO.setAmount(countAmount != null ? countAmount : BigDecimal.ZERO);
            listShipByAffirmListVO.setNoRateAmount(countNoRateAmount != null ? countNoRateAmount : BigDecimal.ZERO);

            vos.add(listShipByAffirmListVO);
        }
        PcwpOrg bySortCode = pcwpOrgService.getBySortCode(sortCode);
        if (bySortCode != null) {
            jsonObject.put("sortCode", bySortCode.getSortcode());
        }
        BigDecimal countAmount = baseMapper.selCountAmount(jsonObject);
        BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(jsonObject);
        if (vos.size() > 0) { //获取总额
            vos.get(0).setCountAmount(countAmount != null ? countAmount : BigDecimal.ZERO);
            vos.get(0).setCountNoRateAmount(countNoRateAmount != null ? countNoRateAmount : BigDecimal.ZERO);
        }
        pages.setRecords(vos);
        //PcwpOrg parentPcwpOrg = pcwpOrgService.getBySortCode(sortCode);
        //pages.setCountId(parentPcwpOrg == null ? "" : parentPcwpOrg.getParentid());
        return new PageUtils(pages);

        /*PcwpOrg bySortCode = pcwpOrgService.getBySortCode(sortCode);
        int count = baseMapper.listByAffirmListCount(jsonObject.getInnerMap());
        pageUtils.pageDispose(jsonObject, count);
        Page<ListShipByAffirmListVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ListShipByAffirmListVO> vos = baseMapper.listByAffirmList(pages, jsonObject);
        BigDecimal CountAmount = baseMapper.selCountAmount(jsonObject);
        BigDecimal countNoRateAmount = baseMapper.selCountNoRateAmount(jsonObject);
//        if (io.seata.common.util.CollectionUtils.isNotEmpty(vos)) {
//            BigDecimal bigDecimal = new BigDecimal(0);
//            for (ListShipByAffirmListVO vo : vos) {
//                bigDecimal = bigDecimal.add(vo.getAmount());
//            }
//            vos.get(0).setCountAmount(bigDecimal);
//        }
        if (vos.size() > 0) {
            vos.get(0).setCountAmount(CountAmount);
            vos.get(0).setCountNoRateAmount(countNoRateAmount);
        }
        pages.setRecords(vos);
        return new PageUtils(pages);*/
    }
    /**
     * 导出数据平台
     *
     * @param jsonObject
     * @param response
     */
    @Override
    public void platformOutputExcel(JSONObject jsonObject, HttpServletResponse response) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String sortCode = (String) innerMap.get("sortCode");
        PcwpOrg bySortCode = pcwpOrgService.getBySortCode(sortCode);
        if(bySortCode != null){
            jsonObject.put("sortCode",bySortCode.getSortcode());
            jsonObject.put("orgId",sortCode);
        }
        List<ListShipByAffirmListVO> vos = baseMapper.platformOutputExcel(jsonObject.getInnerMap());
        if (io.seata.common.util.CollectionUtils.isNotEmpty(vos)) {
            BigDecimal bigDecimal = new BigDecimal(0);
            for (ListShipByAffirmListVO vo : vos) {
                bigDecimal = bigDecimal.add(vo.getAmount());
            }
            vos.get(0).setCountAmount(bigDecimal);
        }
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", vos);
        dataMap.put("countAmountTotal", vos.get(0).getCountAmount());
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "平台交易量信息模板.xlsx", src, "平台交易量信息.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }

    /**
     * 查询物资交易量信息（供应商）
     *
     * @param jsonObject
     * @return
     */
    @Override
    public PageUtils shopListByAffirmList(JSONObject jsonObject) {
        jsonObject.put("enterpriseId", ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        int count = baseMapper.shopListByAffirmListCount(jsonObject.getInnerMap());
        pageUtils.pageDispose(jsonObject, count);
        Page<ShopListShipByAffirmListVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ShopListShipByAffirmListVO> vos = baseMapper.shopListByAffirmList(pages, jsonObject.getInnerMap());
        if (io.seata.common.util.CollectionUtils.isNotEmpty(vos)) {
            BigDecimal bigDecimal = new BigDecimal(0);
            for (ShopListShipByAffirmListVO vo : vos) {
                bigDecimal = bigDecimal.add(vo.getAmount());
            }
            vos.get(0).setCountAmount(bigDecimal);
        }
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    /**
     * 导出数据店铺
     *
     * @param jsonObject
     * @param response
     */
    @Override
    public void shopManageOutputExcel(JSONObject jsonObject, HttpServletResponse response) {
        jsonObject.put("enterpriseId", ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        List<ShopListShipByAffirmListVO> vos = baseMapper.shopManageOutputExcel(jsonObject.getInnerMap());
        BigDecimal bigDecimal = new BigDecimal(0);
        if (io.seata.common.util.CollectionUtils.isNotEmpty(vos)) {
            for (ShopListShipByAffirmListVO vo : vos) {
                bigDecimal = bigDecimal.add(vo.getAmount());
            }
            vos.get(0).setCountAmount(bigDecimal);
        }
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", vos);
        dataMap.put("countAmountTotal", bigDecimal);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "供应商交易量信息模板.xlsx", src, "供应商交易量信息.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }


    @Override
    public void exportDataByBillId(String id) {

    }

    @Override
    public List<OrderShipDtl> getTwoOrderShipByBillid(String BillId) {
        List<OrderShipDtl> dtl = baseMapper.getTwoOrderShipByBillid(BillId);
        return dtl;
    }

//    @Override
//    public List<OrderShipDtl> getdtlListByShipIdAndProductId(String billId, List<String> productIdList) {
//        List<OrderShipDtl> list = list(new LambdaQueryWrapper<OrderShipDtl>()
//                .eq(OrderShipDtl::getBillId, billId)
//                .in(OrderShipDtl::getProductId, productIdList));
//        return list;
//    }
//
//    @Override
//    public List<OrderShipDtl> getdtlListByShipIdAnddtlId(String billId, List<String> productIdList) {
//        List<OrderShipDtl> list = list(new LambdaQueryWrapper<OrderShipDtl>()
//                .eq(OrderShipDtl::getBillId, billId)
//                .in(OrderShipDtl::getDtlId, productIdList));
//        return list;
//    }


    @Override
    public OrderShipDtl getDtlListByShipIdAndProductId(String billId, String productIdList) {
        OrderShipDtl dtl = getOne(new LambdaQueryWrapper<OrderShipDtl>()
                .eq(OrderShipDtl::getBillId, billId)
                .in(OrderShipDtl::getProductId, productIdList));
        return dtl;
    }


    @Override
    public List<OrderShipDtl> getListByIds(String[] ids) {
        LambdaQueryWrapper<OrderShipDtl> q = new LambdaQueryWrapper<>();
        q.in(OrderShipDtl::getDtlId, ids).orderByDesc(OrderShipDtl::getReceiveTime);
        List<OrderShipDtl> list = list(q);
        return list;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderShipDtl getDataByIdAndIsReconciliation(String sourceDtlId, int i) {
        LambdaQueryWrapper<OrderShipDtl> eq = new LambdaQueryWrapper<OrderShipDtl>()
                .eq(OrderShipDtl::getDtlId, sourceDtlId)
                .eq(OrderShipDtl::getIsReconciliation, i);
        return getOne(eq);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAmount(OrderItem orderItem, OrderShipDtl dtl, BigDecimal count) {
        if (orderItem.getParentOrderItemId() != null) {

            //含税金额
            OrderItem main0rderItem = orderItemService.getById(orderItem.getParentOrderItemId());
            dtl.setProductPrice(main0rderItem.getProductPrice());
            dtl.setTotalAmount(main0rderItem.getProductPrice().multiply(count));

            dtl.setOtherTotalAmount(orderItem.getProductPrice().multiply(count));
            dtl.setOtherProductPrice(orderItem.getProductPrice());

            //计算税率
            BigDecimal notRatePrice = TaxCalculator.calculateNotTarRateAmount(main0rderItem.getProductPrice(), main0rderItem.getTaxRate());
            BigDecimal notRateAmount = TaxCalculator.noTarRateItemAmount(dtl.getTotalAmount(), notRatePrice, count, main0rderItem.getTaxRate());
            BigDecimal otherNoRatePrice = TaxCalculator.calculateNotTarRateAmount(orderItem.getProductPrice(), orderItem.getTaxRate());

            BigDecimal otherNoRateAmount = TaxCalculator.noTarRateItemAmount(dtl.getOtherTotalAmount(), otherNoRatePrice, count, orderItem.getTaxRate());

            //不含税继而
            dtl.setTaxRate(main0rderItem.getTaxRate());
            dtl.setOtherTaxRate(orderItem.getTaxRate());
            dtl.setNoRatePrice(notRatePrice);
            dtl.setNoRateAmount(notRateAmount);
            dtl.setOtherNoRateAmount(otherNoRateAmount);
            dtl.setOtherNoRatePrice(otherNoRatePrice);
            //更新订单商品发货数量
            main0rderItem.setShipCounts(orderItem.getShipCounts());
            if (main0rderItem.getConfirmCounts() != null) {
                main0rderItem.setConfirmCounts(orderItem.getConfirmCounts());
            }
            orderItemService.updateById(main0rderItem);
        } else {
            OrderItem son = orderItemService.getOrderItemByParentId(orderItem.getOrderItemId());
            if (son != null) {
                //二级供应商商品价格和商品总价 含税 + 不含税
                dtl.setOtherProductPrice(son.getProductPrice());
                dtl.setOtherTotalAmount(son.getProductPrice().multiply(count));
                BigDecimal otherNoRatePrice = TaxCalculator.calculateNotTarRateAmount(dtl.getOtherProductPrice(), son.getTaxRate());
                BigDecimal otherNoRateAmount = TaxCalculator.noTarRateItemAmount(dtl.getOtherTotalAmount(), otherNoRatePrice, count, son.getTaxRate());
                dtl.setOtherNoRatePrice(otherNoRatePrice);
                dtl.setOtherNoRateAmount(otherNoRateAmount);

                son.setShipCounts(orderItem.getShipCounts());
                if (son.getConfirmCounts() != null) {
                    son.setConfirmCounts(orderItem.getConfirmCounts());
                }
                orderItemService.updateById(son);
            }
            //自营店商品价格和商品总价
            dtl.setProductPrice(orderItem.getProductPrice());
            dtl.setTotalAmount(orderItem.getProductPrice().multiply(count));
            BigDecimal notRatePrice = TaxCalculator.calculateNotTarRateAmount(orderItem.getProductPrice(), orderItem.getTaxRate());
            BigDecimal notRateAmount = TaxCalculator.noTarRateItemAmount(dtl.getTotalAmount(), notRatePrice, count, orderItem.getTaxRate());
            dtl.setNoRatePrice(notRatePrice);
            dtl.setTaxRate(orderItem.getTaxRate());
            dtl.setNoRateAmount(notRateAmount);
        }
        orderItemService.updateById(orderItem);
    }

    @Override
    public PageUtils getShopManageMaterial(JSONObject jsonObject, QueryWrapper<PlatformProductFromVo> wrapper) {
//        UserLogin user = ThreadLocalUtil.getCurrentUser();
//        jsonObject.getInnerMap().put("shopId",user.getShopId());
//        Page<TransactionProductVo> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
//
//       List<TransactionProductVo> vos= orderItemService.selectTransactionProduct(pages,wrapper);
//        List<ListShipByAffirmListVO> vos = baseMapper.listByAffirmList(pages, jsonObject);
//        if (io.seata.common.util.CollectionUtils.isNotEmpty(vos)) {
//            BigDecimal bigDecimal = new BigDecimal(0);
//            for (ListShipByAffirmListVO vo : vos) {
//                bigDecimal = bigDecimal.add(vo.getAmount());
//            }
//            vos.get(0).setCountAmount(bigDecimal);
//        }
//        pages.setRecords(vos);
//        return new PageUtils(pages);
//    }
        return null;
    }


    @Override
    public BigDecimal getCountByOrderItemIdAndType(String orderItemId, int type) {
        BigDecimal count = baseMapper.getCountByOrderItemIdAndType(orderItemId, type);
        if (count == null) {
            return new BigDecimal(0);
        }

        return count;
    }

    /**
     * 发货/收货总量：如果没有收货数量：发货量减去PCWP退货量（如果有收货数量：收货数量减去PCWP退货量）
     *
     * @param orderItemIds
     * @return
     */
    @Override
    public BigDecimal getShipCounts(List<String> orderItemIds) {
//         发货/收货总量：如果没有收货数量：发货量减去PCWP退货量（如果有收货数量：收货数量减去PCWP退货量）
//        BigDecimal qty = new BigDecimal(0);
//        for (String orderItemId : orderItemIds) {
//            List<OrderShipDtl> dtls = lambdaQuery()
//                    .eq(OrderShipDtl::getOrderItemId, orderItemId)
//                    .select(OrderShipDtl::getBillId, OrderShipDtl::getShipCounts, OrderShipDtl::getShipNum).list();
//            if (CollectionUtils.isEmpty(dtls)) {
//                OrderItem one = orderItemService.lambdaQuery().eq(OrderItem::getParentOrderItemId, orderItemId)
//                        .select(OrderItem::getOrderId, OrderItem::getOrderItemId).one();
//                if (one != null) {
//                    dtls = lambdaQuery()
//                            .eq(OrderShipDtl::getOrderItemId, one.getOrderItemId())
//                            .select(OrderShipDtl::getBillId, OrderShipDtl::getShipCounts, OrderShipDtl::getShipNum).list();
//                }
//            }
//            if (!CollectionUtils.isEmpty(dtls)) {
//                BigDecimal syQty = new BigDecimal(0);
//                for (OrderShipDtl dtl : dtls) {
//                    OrderShip orderShip = orderShipService.lambdaQuery().eq(OrderShip::getBillId, dtl.getBillId())
//                            .select(OrderShip::getBillId, OrderShip::getType).one();
//                    // 收货
//                    if (orderShip.getType() == 2) {
//                        syQty = syQty.add(dtl.getShipNum());
//                    }
//                    // 发货
//                    if (orderShip.getType() == 1 || orderShip.getType() == 0) {
//                        qty = qty.add(dtl.getShipCounts());
//                    }
//                }
//                // PCWP退货数量
//                BigDecimal retQty = orderReturnItemService.getDataByOrderItmIdAndState(orderItemId, 1, 3);
//                BigDecimal sub = syQty.subtract(retQty);
//                qty = qty.add(sub);
//            }
//        }
//        return qty;
        return null;
    }

    /**
     * 汇总订单暂估数量（订单数量需要减去商城退货数量），当订单下单数大于发货数，数量及为订单数量。当订单下单数量小于等于发货数，就需要查看每一笔发货单状态，
     * 当发货单已发货使用确认收货数量，不然则使用发货数量。把订单的发货数量相加。如果有pcwp退货数量则需要使用收货数量减去pcwp退货数量
     *
     * @param orderItemIds
     * @return
     */
    @Override
    public BigDecimal getShipCounts2(List<String> orderItemIds) {
        // 发货/收货总量：如果没有收货数量：发货量减去PCWP退货量（如果有收货数量：收货数量减去PCWP退货量）
        BigDecimal qty = new BigDecimal(0);
        for (String orderItemId : orderItemIds) {
            OrderItem orderItem = orderItemService.lambdaQuery().eq(OrderItem::getOrderItemId, orderItemId).one();
            List<OrderShipDtl> dtls = lambdaQuery()
                    .eq(OrderShipDtl::getOrderItemId, orderItem.getOrderItemId())
                    .select(OrderShipDtl::getBillId, OrderShipDtl::getShipCounts, OrderShipDtl::getShipNum).list();
            if (CollectionUtils.isEmpty(dtls)) {
                OrderItem orderItemTwo = orderItemService.lambdaQuery().eq(OrderItem::getParentOrderItemId, orderItemId)
                        .select(OrderItem::getOrderId, OrderItem::getOrderItemId, OrderItem::getBuyCounts).one();
                if (orderItemTwo != null) {
                    dtls = lambdaQuery()
                            .eq(OrderShipDtl::getOrderItemId, orderItemTwo.getOrderItemId())
                            .select(OrderShipDtl::getBillId, OrderShipDtl::getShipCounts, OrderShipDtl::getShipNum).list();
                }
            }
            Orders orders = ordersService.lambdaQuery()
                    .eq(Orders::getOrderId, orderItem.getOrderId())
                    .select(Orders::getOrderId, Orders::getState).one();
            if (orders.getState() == 10) {
                // 如果已完成直接使用收货数量
                qty = qty.add(orderItem.getConfirmCounts().subtract(orderItem.getPcwpReturn()));
            } else {
                if (!CollectionUtils.isEmpty(dtls)) {
                    BigDecimal mReturnCounts = orderItem.getReturnCounts();
                    BigDecimal pcwpRetuen = orderItem.getPcwpReturn();
                    //购买数量-商城退货数量《=发货数量-pcwp退货数量 为待收货状态 ，其他为代发货状态

                    //  订单数量-商城退货>发货数量-pcwp退货数量时，为未全部发货
                    if (orderItem.getBuyCounts().subtract(mReturnCounts).compareTo(orderItem.getShipCounts().subtract(pcwpRetuen)) > 0) {
                        // 未发货使用订单有效数量
                        qty = qty.add(orderItem.getBuyCounts().subtract(mReturnCounts));
                    } else {
                        // 已经发货完成
                        BigDecimal syQty = new BigDecimal(0);
                        for (OrderShipDtl dtl : dtls) {
                            OrderShip orderShip = orderShipService.lambdaQuery().eq(OrderShip::getBillId, dtl.getBillId())
                                    .select(OrderShip::getBillId, OrderShip::getType).one();
                            // 收货
                            if (orderShip.getType() == 2) {
                                syQty = syQty.add(dtl.getShipNum());
                            } else {
                                qty = qty.add(dtl.getShipCounts());
                            }
                        }
                        // PCWP退货数量
                        BigDecimal retQty = orderItem.getPcwpReturn();
                        BigDecimal sub = syQty.subtract(retQty);
                        qty = qty.add(sub);
                    }
                } else {
                    BigDecimal mReturnCounts = orderItem.getReturnCounts();
                    qty = qty.add(qty.subtract(mReturnCounts));
                }
            }
        }
        return qty;
    }


    @Override
    public List<MaterialShipDtlVo> getMaterialShipDzDtlsByOrderShipId(String billId) {
        List<MaterialShipDtlVo> dtlVos = baseMapper.getMaterialShipDzDtlsByOrderShipId(billId);
        return dtlVos;
    }

    @Override
    public PageUtils wxqueryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderShipDtl> q) {
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String billId = (String) innerMap.get("billId");
        String keywords = (String) innerMap.get("keywords");

        q.eq(OrderShipDtl::getBillId, billId);
        if (keywords != null) {
            q.like(OrderShipDtl::getProductName, keywords);
        }
        if (billId != null) {
            q.eq(OrderShipDtl::getBillId, billId);
        }

        IPage<OrderShipDtl> page = this.page(
                new Query<OrderShipDtl>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    @Override
    public BigDecimal getShipCountsIsThis(String orderItemId, BigDecimal thisQty) {
        BigDecimal qty = new BigDecimal(0);
        OrderItem one = orderItemService.lambdaQuery().eq(OrderItem::getOrderItemId, orderItemId).one();
        List<OrderShipDtl> dtls = lambdaQuery()
                .eq(OrderShipDtl::getOrderItemId, orderItemId)
                .select(OrderShipDtl::getBillId, OrderShipDtl::getShipCounts, OrderShipDtl::getShipNum).list();
        if (CollectionUtils.isEmpty(dtls)) {
            OrderItem oneOrder = orderItemService.lambdaQuery().eq(OrderItem::getParentOrderItemId, orderItemId)
                    .select(OrderItem::getOrderId, OrderItem::getOrderItemId).one();
            if (oneOrder != null) {
                dtls = lambdaQuery()
                        .eq(OrderShipDtl::getOrderItemId, oneOrder.getOrderItemId())
                        .select(OrderShipDtl::getBillId, OrderShipDtl::getShipCounts, OrderShipDtl::getShipNum).list();
            }
        }
        //购买数量-商城退货数量《=发货数量-pcwp退货数量 为待收货状态 ，其他为代发货状态
        BigDecimal mReturnCounts = one.getReturnCounts();
        BigDecimal pcwpRetuen = one.getPcwpReturn();
        if (!CollectionUtils.isEmpty(dtls)) {
            //  订单数量-商城退货>发货数量-pcwp退货数量时，为未全部发货
            if (one.getBuyCounts().subtract(mReturnCounts).compareTo(one.getShipCounts().add(thisQty).subtract(pcwpRetuen)) > 0) {
                // 未全发货使用订单有效数量
                qty = qty.add(one.getBuyCounts().subtract(mReturnCounts));
            } else {
                // 已经发货完成
                BigDecimal syQty = new BigDecimal(0);
                for (OrderShipDtl dtl : dtls) {
                    OrderShip orderShip = orderShipService.lambdaQuery().eq(OrderShip::getBillId, dtl.getBillId())
                            .select(OrderShip::getBillId, OrderShip::getType).one();
                    // 收货
                    if (orderShip.getType() == 2) {
                        syQty = syQty.add(dtl.getShipNum());
                    } else {
                        qty = qty.add(dtl.getShipCounts());
                    }
                }
                // PCWP退货数量
                BigDecimal retQty = one.getPcwpReturn();
                BigDecimal sub = syQty.subtract(retQty);
                qty = qty.add(sub);
                qty = qty.add(thisQty);
            }
        } else {
            //订单数量-商城退货>发货数量-pcwp退货数量时，为未全部发货
            if (one.getBuyCounts().subtract(mReturnCounts).compareTo(thisQty.subtract(pcwpRetuen)) > 0) {
                // 未发货使用订单有效数量
                qty = qty.add(one.getBuyCounts().subtract(mReturnCounts));
            } else {
                qty = qty.add(thisQty);
            }
        }
        return qty;
    }


    @Override
    public BigDecimal getDataByOrderItmIdAndType(String orderItemId) {
        QueryWrapper<OrderShipDtl> shipCountsq = new QueryWrapper<>();
        shipCountsq.eq("os.is_delete", 0).eq("osd.order_item_id", orderItemId);
        BigDecimal shipCount = baseMapper.selectSumShipNum(shipCountsq);

        if (shipCount == null) {
            return new BigDecimal(0);
        }
        return shipCount;
    }


    @Override
    public List<MaterialShipDtlVo> getMaterialShipDtlsByOrderShipId(String billId) {
        List<MaterialShipDtlVo> dtlVos = baseMapper.findAllMaterialShipDtlByOrderShipId(billId);
        return dtlVos;
    }


    @Override
    public void updateShipCounts(List<String> ids) {
        List<OrderShipDtl> dtls = baseMapper.selectBatchIds(ids);
        //获取订单信息。所有的发货单项id都属于同一个订单，一个订单（OrderItem）可以生成多个发货单（OrderShip），一个订单项（OrderItem）可以关联多个发货单项（OrderShipDtl），发货单项只能关联一个订单项
        String orderId = dtls.get(0).getOrderId();

        //统一收集订单项数据，便于更改，减少数据库访问次数
        ArrayList<OrderItem> list = new ArrayList<>();
        //统一收集订单数据，便于更改，减少数据库访问次数
        ArrayList<Orders> orderIds = new ArrayList<>();
        Orders byId = ordersService.getById(orderId);
        if (byId == null) {
            return; // 如果获取订单信息为空，直接返回，防止后续空指针异常
        }
        boolean flag = false;

        if (byId.getOrderClass() == 1) {

            //判断商品类型 大宗月供商品还需要修改其他的数据
            //遍历发货项数据，如果发货数量大于收货数量，得出差值=发货数量-收货数量，修改发货数量=收货数量，查询发货项关联的订单项数据，修改发货数量=发货数量-差值
            for (OrderShipDtl dtl : dtls) {
                BigDecimal subtract = dtl.getShipCounts().subtract(dtl.getShipNum());
                if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                    dtl.setShipCounts(dtl.getShipNum());
                    OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
                    orderItem.setShipCounts(orderItem.getShipCounts().subtract(subtract));
                    list.add(orderItem);
                    flag = true;
                }
            }
        } else {
            // 二级订单
            for (OrderShipDtl dtl : dtls) {
                //
                BigDecimal subtract = dtl.getShipCounts().subtract(dtl.getShipNum());
                if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                    dtl.setShipCounts(dtl.getShipNum());
                    //获取二级订单的订单项
                    OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
                    //获取二级订单项的子订单项
                    OrderItem son = orderItemService.getOrderItemByParentId(orderItem.getOrderItemId());
                    son.setShipCounts(son.getShipCounts().subtract(subtract));
                    orderItem.setShipCounts(orderItem.getShipCounts().subtract(subtract));
                    list.add(son);
                    list.add(orderItem);
                    //根据二级订单项找到对应的二级订单，修改二级订单状态
                    Orders sonOrders = ordersService.getById(son.getOrderId());
                    orderIds.add(sonOrders);
                    flag = true;
                }
            }
        }
        if (flag) {
            byId.setState(8);
            orderIds.add(byId);
            //修复所有订单项发货数量
            orderItemService.updateBatchById(list);
            //修改订单状态
            ordersService.updateBatchById(orderIds);
            //修改发货单项的发货数量
            updateBatchById(dtls);
        }

    }

    /**
     * 查询一级大宗临购可对账商品物资列表
     *
     * @param dto 查询参数
     * @return 分页结果
     */
    @Override
    public PageUtils getReconcilableMaterialList(ReconcilableMaterialDTO dto) {
        // 获取总数
        int count = baseMapper.getReconcilableMaterialListCount(dto);

        // 如果总数为0，直接返回空结果
        if (count == 0) {
            Page<ReconcilableMaterialVO> emptyPages = new Page<>(dto.getPage(), dto.getLimit());
            emptyPages.setTotal(0);
            emptyPages.setRecords(new ArrayList<>());
            return new PageUtils(emptyPages);
        }

        // 分页查询
        Page<ReconcilableMaterialVO> pages = new Page<>(dto.getPage(), dto.getLimit());
        List<ReconcilableMaterialVO> list = baseMapper.getReconcilableMaterialList(dto);
        pages.setRecords(list);
        pages.setTotal(count);
        return new PageUtils(pages);
    }
}
