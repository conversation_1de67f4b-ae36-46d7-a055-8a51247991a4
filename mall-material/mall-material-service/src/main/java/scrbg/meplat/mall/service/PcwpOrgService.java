package scrbg.meplat.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import scrbg.meplat.mall.entity.PcwpOrg;
import scrbg.meplat.mall.entity.pcwpmq.PcwpOrginfos;

import java.util.List;

public interface PcwpOrgService extends IService<PcwpOrg> {
    List<PcwpOrg> getTree(String name);
    PcwpOrg getBySortCode(String sortCode);

    PcwpOrg getByOrgNumber(String orgNumber);
    // 根据排序码获取所有下级项目的排序码，递归查询
    List<String> getSortCodeList(String sortCode);
    // 获取子机构
    List<PcwpOrg> getChildren(String id);

    String getPcwpOrgId(String orgNumber);
    // 获取顶级机构 (四川路桥)
    List<PcwpOrg> getTopOrg();


}
