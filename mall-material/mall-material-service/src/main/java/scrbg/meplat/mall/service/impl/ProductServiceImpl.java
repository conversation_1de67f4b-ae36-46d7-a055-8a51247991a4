package scrbg.meplat.mall.service.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.product.*;
import scrbg.meplat.mall.dto.product.material.*;
import scrbg.meplat.mall.dto.product.material.lcProduct.CuterCreateLcMaterialDTO;

import scrbg.meplat.mall.dto.thirdapi.MaterialDtlDTO;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.entity.excelTemplate.*;
import scrbg.meplat.mall.enums.CodeEnum;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.enums.file.FileEnum;
import scrbg.meplat.mall.enums.product.OrderEnum;
import scrbg.meplat.mall.enums.product.ProductEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.*;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;
import scrbg.meplat.mall.vo.floor.website.WFloorGoodsVO;
import scrbg.meplat.mall.vo.platform.MaterialVo;
import scrbg.meplat.mall.vo.product.*;
import scrbg.meplat.mall.vo.product.device.CopyDeviceSkuVO;

import scrbg.meplat.mall.vo.product.material.CategoryClassIdAndClassNameVO;
import scrbg.meplat.mall.vo.product.material.MaterInfoVO;
import scrbg.meplat.mall.vo.product.material.PlatformMaterialListVO;

import scrbg.meplat.mall.vo.product.website.IndexMaterialVO;
import scrbg.meplat.mall.vo.product.website.ProductDetailDealRecordVO;

import scrbg.meplat.mall.vo.product.website.material.WMaterialVO;
import scrbg.meplat.mall.vo.product.website.material.WMaterialnfoVO;

import scrbg.meplat.mall.vo.shopManage.reportForms.PlatformProductFromVo;
import scrbg.meplat.mall.vo.shopManage.reportForms.ProductFromVo;
import scrbg.meplat.mall.vo.w.CheckIsPutawayVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.concurrent.TimeUnit.*;
import static scrbg.meplat.mall.common.constant.CommonConstants.BACK_OFFICE_ADMINISTRATOR_NAME;
import static scrbg.meplat.mall.common.constant.CommonConstants.SYSTEM_NAME;

import com.fasterxml.jackson.core.type.TypeReference;

/**
 * @描述：店铺商品信息 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Log4j2
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Autowired
    ProductMapper productMapper;
    @Autowired
    ShopBusinessService shopBusinessService;
    @Autowired
    UserAddressService userAddressService;

    @Autowired
    SystemParamService systemParamService;
    @Autowired
    private PrivateKeySupplierService privateKeySupplierService;
    @Autowired
    private ProductService productService;

    @Autowired
    private ErrorInfoService errorInfoService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    RestTemplateUtils restTemplateUtils;
    @Autowired
    ShopMapper shopMapper;
    @Autowired
    FloorGoodsMapper floorGoodsMapper;
    @Autowired
    FileService fileService;

    @Autowired
    ProductSkuMapper productSkuMapper;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    UserService userService;

    @Autowired
    ShopService shopService;

    @Autowired
    ShoppingCartMapper shoppingCartMapper;

    @Autowired
    ProductSkuService productSkuService;

    @Autowired
    ProductCategoryService productCategoryService;

    @Autowired
    BrandService brandService;

    @Autowired
    OrderItemMapper orderItemMapper;
    @Autowired
    OrderItemService orderItemService;

    @Autowired
    public MallConfig mallConfig;
    @Autowired
    public ProductCollectService productCollectService;

    @Autowired
    ProductAttributeValueService productAttributeValueService;

    @Autowired
    ProductAttributeValueMapper productAttributeValueMapper;
    @Autowired
    FileRecordDeleteService fileRecordDeleteService;

    @Autowired
    StationMessageService stationMessageService;

    @Autowired
    StationMessageReceiveMapper stationMessageReceiveMapper;

    @Autowired
    RegionPriceService regionPriceService;

    private ProductShelfLogService productShelfLogService;

    @Autowired
    public void setProductShelfLogService(ProductShelfLogService productShelfLogService) {
        this.productShelfLogService = productShelfLogService;
    }

    // 物资基础库列表
    private static final String BASICS_MATERIAL_LIST = "/thirdapi/matarialpurchase/queryPageMaterialDtl";


    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<Product> queryWrapper) {
        IPage<Product> page = this.page(new Query<Product>().getPage(jsonObject), queryWrapper);
        return new PageUtils(page);
    }

    @Override
    public void create(Product product) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(product);
    }

    @Override
    public void update(Product product) {
        super.updateById(product);
    }


    @Override
    public Product getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

//    /**
//     * 批量导入物资
//     *
//     * @param dtos
//     * @return
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void importBatchMaterial(List<CreateMaterialDTO> dtos) {
//        List<Product> products = new ArrayList<>();
//        List<ProductSku> productSkus = new ArrayList<>();
//        List<String> classIds = new ArrayList<>();
//        dtos.forEach(dto -> {
//            Product product = new Product();
//            product.setProductName(dto.getProductName());
//            product.setProductDescribe(dto.getProductDescribe());
//            product.setClassId(dto.getClassId());
//            product.setProductInventoryId(dto.getProductInventoryId());
//            product.setProductType(PublicEnum.TYPE_MATERIAL.getCode());
//            product.setState(ProductEnum.STATE_STAY_PUTAWAY.getCode());
//            if (baseMapper.selectCount(Wrappers.query(product)) > 0) {
//                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "【" + dto.getProductName() + "】 物资重复导入！");
//            }
//            products.add(product);
//            ProductSku productSku = new ProductSku();
//            productSku.setSkuName(dto.getSpec());
//            productSku.setUnit(dto.getUnit());
//            productSku.setProductId(product.getProductId());
//            productSku.setState(PublicEnum.STATE_STOP.getCode());
//            if (productSkuMapper.selectCount(Wrappers.query(productSku)) > 0) {
//                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "【" + dto.getSpec() + "】 规格重复导入！");
//            }
//            productSkus.add(productSku);
//            classIds.add(dto.getClassId());
//        });
//        boolean b = this.saveBatch(products);
//        if (!b) {
//            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "保存失败！");
//        }
//        boolean b1 = productSkuService.saveBatch(productSkus);
//        if (!b1) {
//            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "保存失败！");
//        }
//        //分类修改为有商品
//        productCategoryService.updateHaveProductStateById(classIds, PublicEnum.IS_YES.getCode());
//    }

//    /**
//     * 根据物资id修改物资信息
//     *
//     * @param dto
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void updateMaterialInfo(UpdateProductInfoDTO dto) {
//        // 修改商品基本信息
//        Product product = new Product();
//        product.setProductId(dto.getProductId());
//        product.setProductName(dto.getProductName());
//        product.setProductDescribe(dto.getProductDescribe());
//        product.setClassId(dto.getClassId());
//        product.setShopId(shopId);
//        product.setProductMinPrice(dto.getProductMinPrice());
//        baseMapper.updateById(product);
//        List<String> classIds = new ArrayList<>();
//        classIds.add(dto.getClassId());
//        productCategoryService.updateHaveProductStateById(classIds, PublicEnum.IS_YES.getCode());
//        // 保存媒体
//        List<ProductFileSaveOrUpdateDTO> mediumList = dto.getMediumList();
//        List<File> files = new ArrayList<>();
//        if (mediumList.size() > 0) {
//            mediumList.forEach(m -> {
//                File file = new File();
//                BeanUtils.copyProperties(m, file);
//                file.setRelevanceId(dto.getProductId());
//                file.setRelevanceType(FileEnum.RELEVANCE_TYPE_PRODUCT.getCode());
//                files.add(file);
//            });
//            fileService.saveOrUpdateBatch(files);
//        }
//        if (dto.getCostPrice() == null) {
//            dto.setCostPrice(new BigDecimal(0));
//        }
//        // 修改规格信息
//        productSkuService.lambdaUpdate().eq(ProductSku::getProductId, dto.getProductId())
//                .set(ProductSku::getSkuName, dto.getSpec())
//                .set(ProductSku::getOriginalPrice, dto.getOriginalPrice())
//                .set(ProductSku::getSellPrice, dto.getSellPrice())
//                .set(ProductSku::getStock, dto.getStock())
//                .set(ProductSku::getUnit, dto.getUnit())
//                .set(ProductSku::getCostPrice, dto.getCostPrice())
//                .set(ProductSku::getSkuImg, dto.getSpecImage())
//                .set(ProductSku::getGmtModified, new Date())
//                .update();
//    }

    /**
     * 根据ids批量逻辑删除
     *
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeLogicBatch(List<String> ids) {
//        lambdaUpdate().in(Product::getProductId, ids)
//                .set(Product::getIsDelete, PublicEnum.IS_DELETE_YES.getCode())
//                .set(Product::getGmtModified, new Date())
//                .update();
//        productSkuService.lambdaUpdate()
//                .in(ProductSku::getProductId, ids)
//                .set(ProductSku::getIsDelete, PublicEnum.IS_DELETE_YES.getCode())
//                .set(ProductSku::getGmtModified, new Date())
//                .update();

        List<Product> list = lambdaQuery().in(Product::getProductId, ids)
                        .select(Product::getProductId, Product::getProductName).list();
        removeByIds(ids);
        productSkuService.lambdaUpdate().in(ProductSku::getProductId, ids).remove();
        LambdaUpdateWrapper<FloorGoods> q = Wrappers.lambdaUpdate(FloorGoods.class);
        q.in(FloorGoods::getGoodsId, ids);
        floorGoodsMapper.delete(q);
        productShelfLogService.saveBatch(ProductShelfLog.init(list,0));
        fileService.lambdaUpdate().in(File::getRelevanceId, ids).eq(File::getRelevanceType, FileEnum.RELEVANCE_TYPE_PRODUCT.getCode()).remove();
    }


    /**
     * 根据商品id集合批量修改上下架状态
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductState(UpdateProductStateDTO dto) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String shopId = user.getShopId();
        if (mallConfig.isPlatformFee == 1) {
            EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId())
                    .select(EnterpriseInfo::getEnterpriseId,
                            EnterpriseInfo::getShuDaoFlag,
                            EnterpriseInfo::getInteriorId,
                            EnterpriseInfo::getEnterpriseType).one();
            if (supplier == null) {
                throw new BusinessException("未查询到机构信息！");
            }
            if (supplier.getShuDaoFlag() == 1 || supplier.getInteriorId() != null) {
            } else {
                platformYearFeeService.checkYearShopIsOut(shopId);
            }
        }
        List<String> productIds = dto.getProductIds();
        List<Product> list = lambdaQuery().in(Product::getProductId, productIds)
                .select(Product::getProductId, Product::getProductName).list();
        int state = dto.getState();
        // 上架
        int code1 = ProductEnum.STATE_PUTAWAY.getCode();
        // 下架
        int code2 = ProductEnum.STATE_SOLD_OUT.getCode();
        // 同时修改sku的启用/停用状态
        if (code1 == state) {
            // 如果是上架判断是否有权限
            Integer isPlatformAdmin = ThreadLocalUtil.getCurrentUser().getIsPlatformAdmin();
            if (isPlatformAdmin == null || isPlatformAdmin != 1) {
                throw new BusinessException(500, "没有平台管理员权限！");
            }
            List<String> ids = new ArrayList<>();
            List<String> productNames = new ArrayList<>();
            for (String productId : dto.getProductIds()) {
                Product product = lambdaQuery().eq(Product::getProductId, productId).select(Product::getIsCompletion, Product::getProductName, Product::getRelevanceNo).one();
                // 控制基础库启用
//                if (product!=null){
//                    handleProductTextureStatus(product.getRelevanceNo());
//                }
                if (product == null || product.getIsCompletion() != ProductEnum.IS_COMPLETION_1.getCode()) {
                    try {
                        productNames.add(product.getProductName());
                    } catch (Exception e) {
                        System.out.println(product);
                        throw new RuntimeException(e);
                    }
                    continue;
                } else {
                    ids.add(productId);
                }
            }
            if (ids.size() == 0 || productNames.size() != 0) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "【" + productNames + "】商品未编辑完成，请检查后重试！");
            }
            lambdaUpdate().in(Product::getProductId, ids).set(Product::getState, code1).set(Product::getFailReason, null).set(Product::getPutawayDate, new Date()).set(Product::getGmtModified, new Date()).update();
//            productSkuService.lambdaUpdate()
//                    .in(ProductSku::getProductId, ids)
//                    .set(ProductSku::getState, PublicEnum.STATE_OPEN.getCode())
//                    .set(ProductSku::getGmtModified, new Date())
//                    .update();
            //记录上架日志
            productShelfLogService.saveBatch(ProductShelfLog.init(list,1));
        }
        // 下架
        if (code2 == state) {
            lambdaUpdate().in(Product::getProductId, productIds).set(Product::getState, code2).set(Product::getPutawayDate, null).set(Product::getFailReason, null).set(Product::getGmtModified, new Date())
                    // 记录下架人员
                    .set(ThreadLocalUtil.getCurrentUser() != null, Product::getModifyId, ThreadLocalUtil.getCurrentUser().getUserId())
                    .set(ThreadLocalUtil.getCurrentUser() != null, Product::getModifyName, ThreadLocalUtil.getCurrentUser().getUserName())

                    .update();
//            productSkuService.lambdaUpdate()
//                    .in(ProductSku::getProductId, productIds)
//                    .set(ProductSku::getState, PublicEnum.STATE_STOP.getCode())
//                    .set(ProductSku::getGmtModified, new Date())
//                    .update();
            // 清空购物车
            shoppingCartMapper.removeRealByProductIds(productIds);

            List<WFloorGoodsVO> name = floorGoodsMapper.selectRealByProductIdIds(productIds);


            if (name != null && name.size() > 0) {
                //清空刪除楼层管理商品
                floorGoodsMapper.removeRealByProductIdIds(productIds);

                String notice = null;
                for (int i = 0; i < name.size(); i++) {
                    String value1 = null;
                    if (name.get(i).getFloorName() != null) {
                        value1 = String.valueOf(name.get(i).getFloorNameText());
                    } else {
                        value1 = "";
                    }

                    if (i > 0) {
                        notice = notice + "," + value1;
                    } else {
                        notice = value1;
                    }

                }
                String notice2 = notice + "楼层的【";
                notice = String.valueOf(name.get(0).getProductName());
                notice2 = notice2 + notice + "】商品已经下架，系统已经自动删除。";

                String data = DateUtil.getyyyymmddHHmmss(new Date());

                int s = (int) (Math.random() * 999999999 + 1);
                s = s + 100 * 100 * 100 * 1000;
                String s2 = "11684" + s;
                if (s < 0) {
                    s = -s;
                }

                stationMessageReceiveMapper.insertMessage(s2, SYSTEM_NAME, "系统", "楼层商品删除通知", notice2, 0, data);
                s = (int) Math.random() * 100 + 1;
                String s3 = s2 + s;
                stationMessageReceiveMapper.insertReceiveMessage(s3, BACK_OFFICE_ADMINISTRATOR_NAME, "xt1100000000000000001", s2);

            }
            //记录下架日志
            productShelfLogService.saveBatch(ProductShelfLog.init(list,2));
        }
        // 待审核
        if (state == 3) {

            List<String> ids = new ArrayList<>();
            List<String> productNames = new ArrayList<>();
            for (String productId : dto.getProductIds()) {

                Product product = lambdaQuery().eq(Product::getProductId, productId).select(Product::getIsCompletion, Product::getProductName, Product::getRelevanceNo,Product::getShopId).one();
                if (shopId!=null){
                    shopService.auditArrearageByShopId(product.getShopId());
                }
                // 控制基础库启用
                if (product != null) {
                    handleProductTextureStatus(product.getRelevanceNo());
                }
                //List<ProductSku> skuList = productSkuService.getProductSkuByProductId(productId, null);
                //ProductSku productSku = skuList.get(0);
//                if (productSku.getSellPrice().compareTo(productSku.getOriginalPrice()) > 0) {
//                    throw new BusinessException(500, "【" + product.getProductName() + "】销售价格不能大于原价");
//                }
                if (product == null || product.getIsCompletion() != ProductEnum.IS_COMPLETION_1.getCode()) {
                    productNames.add(product.getProductName());
                    continue;
                } else {
                    ids.add(productId);
                }
            }
            if (ids.size() == 0 || productNames.size() != 0) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "【" + productNames + "】商品未编辑完成，请检查后重试！");
            }
            lambdaUpdate().in(Product::getProductId, productIds).set(Product::getState, state).set(Product::getPutawayDate, null).set(Product::getFailReason, null).set(Product::getGmtModified, new Date())
                    .set(Product::getJcState,0).set(Product::getJcTjName,user.getUserName()).set(Product::getJcTjId,user.getUserId()).set(Product::getJcTjTime,new Date()).update();
        }
        // 审核失败
        if (state == 4) {
            lambdaUpdate().in(Product::getProductId, productIds).set(Product::getState, state).set(Product::getPutawayDate, null).set(Product::getFailReason, dto.getFailReason()).set(Product::getGmtModified, new Date()).update();
        }
    }

    /**
     * 下架店铺所有商品
     *
     * @param shopIds
     */
    @Override
    public void updateProductStateStop(List<String> shopIds) {
        if (CollectionUtils.isEmpty(shopIds)) {
            return;
        }
        int code1 = ProductEnum.STATE_SOLD_OUT.getCode();
        lambdaUpdate().in(Product::getShopId, shopIds).set(Product::getState, code1).set(Product::getGmtModified, new Date()).update();
        List<Product> list = lambdaQuery().in(Product::getShopId, shopIds).select(Product::getProductId).list();
//        if (CollectionUtils.isEmpty(list) || list.get(0) == null) {
//            return;
//        }
//        List<String> productIds = list.stream().map(t -> {
//            return t.getProductId();
//        }).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(productIds)) {
//            return;
//        }
//        productSkuService.lambdaUpdate()
//                .in(ProductSku::getProductId, productIds)
//                .set(ProductSku::getState, PublicEnum.STATE_STOP.getCode())
//                .set(ProductSku::getGmtModified, new Date())
//                .update();

    }


//    /**
//     * 根据物资id获取物资商品信息
//     *
//     * @param productId
//     * @return
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public MaterialProductInfoVO getMaterialById(String productId, Integer state) {
//        LambdaQueryChainWrapper<Product> w = lambdaQuery();
//        w.eq(Product::getIsDelete, PublicEnum.IS_DELETE_NO.getCode()).eq(Product::getProductId, productId);
//        if (state != null) {
//            w.eq(Product::getState, state);
//        }
//        Product product = w.one();
//        if (product == null) {
//            return null;
//        }
//        MaterialProductInfoVO vo = new MaterialProductInfoVO();
//        BeanUtils.copyProperties(product, vo);
//        // 分类名称
//        ProductCategory productCategory = productCategoryService.getById(product.getClassId());
//        if (productCategory != null) {
//            vo.setClassName(productCategory.getClassName());
//        }
//
//        // 商品媒体
//        List<ProductFileVO> mediumList = fileService.listProductMediumVO(productId);
//        vo.setProductMediumVOS(mediumList);
//
//        // 价格
//        ProductSku sku = productSkuService.lambdaQuery().eq(ProductSku::getProductId, productId).one();
//        if (sku != null) {
//            vo.setOriginalPrice(sku.getOriginalPrice());
//            vo.setSellPrice(sku.getSellPrice());
//            vo.setStock(sku.getStock());
//            vo.setUnit(sku.getUnit());
//            vo.setSoldNum(sku.getSoldNum());
//            vo.setCostPrice(sku.getCostPrice());
//            vo.setSpecImage(sku.getSkuImg());
//        }
//        return vo;
//    }

//    /**
//     * 新增物资
//     *
//     * @param dto
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void createMaterialInfo(CreateProductInfoDTO dto) {
//        Product product = new Product();
//        BeanUtils.copyProperties(dto, product);
//        product.setShopId(shopId);
//        product.setProductType(PublicEnum.TYPE_MATERIAL.getCode());
//        product.setState(ProductEnum.STATE_STAY_PUTAWAY.getCode());
//        if (baseMapper.selectCount(Wrappers.query(product)) > 0) {
//            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "【" + dto.getProductName() + "】 物资重复保存！");
//        }
//        int insert = baseMapper.insert(product);
//        if (insert == 0) {
//            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品保存失败！");
//        }
//        // 保存媒体
//        List<ProductMediumDTO> mediumList = dto.getMediumList();
//        List<File> files = new ArrayList<>();
//        if (mediumList.size() > 0) {
//            mediumList.forEach(m -> {
//                File file = new File();
//                BeanUtils.copyProperties(m, file);
//                file.setRelevanceId(product.getProductId());
//                file.setRelevanceType(FileEnum.RELEVANCE_TYPE_PRODUCT.getCode());
//                files.add(file);
//            });
//            boolean b = fileService.saveBatch(files);
//            if (!b) {
//                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品媒体保存失败！");
//            }
//        }
//        // 保存规格信息
//        ProductSku productSku = new ProductSku();
//        productSku.setProductId(product.getProductId());
//        productSku.setSkuName(dto.getSpec());
//        productSku.setUnit(dto.getUnit());
//        productSku.setOriginalPrice(dto.getOriginalPrice());
//        productSku.setSellPrice(dto.getSellPrice());
//        productSku.setStock(dto.getStock());
//        if (dto.getCostPrice() == null) dto.setCostPrice(new BigDecimal(0));
//        productSku.setCostPrice(dto.getCostPrice());
//        productSku.setState(PublicEnum.STATE_OPEN.getCode());
//        productSku.setSkuImg(dto.getSpecImage());
//        if (productSkuMapper.selectCount(Wrappers.query(productSku)) > 0) {
//            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "【" + dto.getSpec() + "】 规格重复保存！");
//        }
//        boolean save = productSkuService.save(productSku);
//        if (!save) {
//            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品保存失败!");
//        }
//    }
//
//    /**
//     * 根据实体属性分页查询物资商品信息
//     *
//     * @param jsonObject
//     * @param queryWrapper
//     * @return
//     */
//    @Override
//    public PageUtils queryMaterialPage(JSONObject jsonObject, LambdaQueryWrapper<Product> queryWrapper) {
//        Map<String, Object> innerMap = jsonObject.getInnerMap();
//        String productName = (String) innerMap.get("productName");
//        String classId = (String) innerMap.get("classId");
//        queryWrapper.eq(Product::getShopId, shopId);
//        queryWrapper.eq(Product::getProductType, PublicEnum.TYPE_MATERIAL.getCode());
//        queryWrapper.eq(Product::getIsDelete, PublicEnum.IS_DELETE_NO.getCode());
//        queryWrapper.orderByDesc(Product::getGmtModified);
//        if (!StringUtils.isEmpty(productName)) {
//            queryWrapper.like(Product::getProductName, productName);
//        }
//        if (!StringUtils.isEmpty(classId)) {
//            queryWrapper.eq(Product::getClassId, classId);
//        }
//        IPage<Product> page = this.page(
//                new Query<Product>().getPage(jsonObject),
//                queryWrapper
//        );
//
//        List<Product> records = page.getRecords();
//        ArrayList<MaterialProductInfoVO> vos = new ArrayList<>();
//        if (records.size() > 0) {
//            records.forEach(t -> {
//                MaterialProductInfoVO vo = new MaterialProductInfoVO();
//                BeanUtils.copyProperties(t, vo);
//
//                // 分类名称
//                ProductCategory productCategory = productCategoryService.getById(t.getClassId());
//                if (productCategory != null) {
//                    vo.setClassName(productCategory.getClassName());
//                }
//
//                // 商品媒体
//                List<ProductFileVO> mediumList = fileService.listProductMediumVO(t.getProductId());
//                vo.setProductMediumVOS(mediumList);
//
//                // 价格
//                ProductSku sku = productSkuService.lambdaQuery()
//                        .eq(ProductSku::getIsDelete, PublicEnum.IS_DELETE_NO.getCode())
//                        .eq(ProductSku::getProductId, t.getProductId())
//                        .one();
//                if (sku != null) {
//                    vo.setOriginalPrice(sku.getOriginalPrice());
//                    vo.setSellPrice(sku.getSellPrice());
//                    vo.setStock(sku.getStock());
//                    vo.setUnit(sku.getUnit());
//                    vo.setSoldNum(sku.getSoldNum());
//                    vo.setCostPrice(sku.getCostPrice());
//                    vo.setSpecImage(sku.getSkuImg());
//                }
//                vos.add(vo);
//            });
//        }
//        page.setRecords(null);
//        PageUtils pageUtils = new PageUtils(page);
//        pageUtils.setList(vos);
//        return pageUtils;
//    }

//    /**
//     * 根据店铺id和分类类型查询店铺商品分类树
//     *
//     * @param classType
//     * @return
//     */
//    @Override
//    public List<ProductCategory> listProductClassByClassType(Integer classType) {
//        // 默认先查询商品
//        List<Product> list = lambdaQuery().eq(Product::getShopId, shopId)
//                .eq(Product::getProductType, classType)
//                .eq(Product::getIsDelete, PublicEnum.IS_DELETE_NO.getCode())
//                .eq(Product::getMallType, mallConfig.mallType)
//                .groupBy(Product::getClassId)
//                .select(Product::getClassId)
//                .list();
//        if (list.size() == 0) return new ArrayList<>();
//
//        List<String> classIds = list.stream().map(t -> {
//            return t.getClassId();
//        }).collect(Collectors.toList());
//        List<ProductCategory> categoryList = productCategoryService.listByIds(classIds);
//        List<ProductCategory> res = productCategoryService.ListByCategoryParentByIds(classType, categoryList);
//        return res;
//    }


//    /**
//     * 根据分类id查询店铺商品分页列表（通用）
//     *
//     * @param jsonObject
//     * @param lambdaQuery
//     * @return
//     */
//    @Override
//    public PageUtils listProductPageByClassId(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery) {
//        Map<String, Object> innerMap = jsonObject.getInnerMap();
//        String productName = (String) innerMap.get("productName");
//        String classId = (String) innerMap.get("classId");
//        Integer productType = (Integer) innerMap.get("productType");
//        lambdaQuery.eq(Product::getShopId, shopId);
//        lambdaQuery.eq(Product::getIsDelete, PublicEnum.IS_DELETE_NO.getCode());
//        lambdaQuery.orderByDesc(Product::getSort);
//        lambdaQuery.eq(Product::getMallType, mallConfig.mallType);
//        if (!StringUtils.isEmpty(productName)) {
//            lambdaQuery.like(Product::getProductName, productName);
//        }
//        if (!StringUtils.isEmpty(classId)) {
//            lambdaQuery.eq(Product::getClassId, classId);
//        }
//        if (productType != null) {
//            lambdaQuery.eq(Product::getProductType, productType);
//
//        }
//        IPage<Product> page = this.page(
//                new Query<Product>().getPage(jsonObject),
//                lambdaQuery
//        );
//        return new PageUtils(page);
//    }

    /**
     * 根据物资id获取物资商品信息（物资C端）
     *
     * @param productId
     * @return
     */
    @Override
    public WMaterialnfoVO materialInfo(String productId) {
        Product product = lambdaQuery().eq(Product::getProductId, productId)
                .eq(Product::getShowState, 0)
                .one();
        if (product == null) {
            throw new BusinessException(ProductEnum.RESULT_CODE_500101.getCode(), ProductEnum.RESULT_CODE_500101.getRemark());
        }
        if (product.getState() != 1) {
            throw new BusinessException(ProductEnum.RESULT_CODE_500102.getCode(), ProductEnum.RESULT_CODE_500102.getRemark());
        }
        WMaterialnfoVO vo = new WMaterialnfoVO();
        BeanUtils.copyProperties(product, vo);
        // 分类名称
        ProductCategory productCategory = productCategoryService.getProductCategoryById(product.getClassId(), null);
        if (productCategory != null) vo.setClassName(productCategory.getClassName());

        //品牌名称
        if (StringUtils.isNotEmpty(product.getBrandId())) {
            Brand brand = brandService.getById(product.getBrandId());
            if (brand != null) vo.setBrandName(brand.getName());
        }


        // 商品媒体
        List<ProductFileVO> mediumList = fileService.listProductMediumVO(productId);
        vo.setProductFile(mediumList);

        // 联系电话
        Shop shop = shopMapper.selectById(product.getShopId());
        if (shop.getShopId().equals("1645601878095495170")){
            Product one = lambdaQuery().eq(Product::getOutKeyId, vo.getProductId()).one();
            vo.setOutKeyId(one.getProductId());
        }
        if (shop != null) {
            vo.setDetailedAddress(shop.getProvince() + shop.getCity());
            vo.setIsInternalSettlement(shop.getIsInternalSettlement());
            if (shop.getContactNumber() != null) {
                String encryptionPhone = EncryptionUtil.encryptionPhone(shop.getContactNumber());
                vo.setContactNumber(encryptionPhone);
            }
            vo.setShopState(shop.getState());
        }
        //销量
        BigDecimal soldNum = getsoldNumCount(product.getSerialNum());
        vo.setSoldNum(soldNum);

        // 价格
        List<ProductSku> productSkuByProductId = productSkuService.getProductSkuByProductId(productId, PublicEnum.STATE_OPEN.getCode());
        if (productSkuByProductId.size() == 0) return vo;
        ProductSku sku = productSkuByProductId.get(0);
        if (sku != null) {
            vo.setSoldNum(sku.getSoldNum());
            //TODO  物资需求  登录用户（销售价格）和未登录用户（原价）所见价格
            if (mallConfig.mallType == 0) {
                UserLogin user = ThreadLocalUtil.getCurrentUser();
                if (user == null) {
                    vo.setSellPrice(sku.getOriginalPrice());
                    vo.setOriginalPrice(null);
                } else {
                    vo.setSellPrice(sku.getSellPrice());
                }
            } else {
                vo.setSellPrice(sku.getSellPrice());
                vo.setOriginalPrice(sku.getOriginalPrice());
            }
            vo.setStock(sku.getStock());
            vo.setUnit(sku.getUnit());
            vo.setSecondUnit(sku.getSecondUnit());
            vo.setSecondUnitNum(sku.getSecondUnitNum());
            vo.setSkuImg(sku.getSkuImg());
            vo.setSkuName(sku.getSkuName());
            vo.setIsZone(sku.getIsZone());
        }

        // 分类路径
        List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(product.getClassId());
        vo.setClassPathVOS(categoryParentPath);

        List<RegionPrice> list = regionPriceService.lambdaQuery().eq(RegionPrice::getProductId, productId).list();
        if (list != null && list.size() > 0) {
            for(RegionPrice rp:list){
                try{
                    if(rp.getRegionName().equals("全区域")) {
                        rp.setSelectAddressOptionsAll((rp.getAreaCode() != null && rp.getAreaCode().length()>0) ? new ObjectMapper().readValue(rp.getAreaCode(), new TypeReference<List<String>>(){}) : null);
                    }else{
                        rp.setSelectAddressOptions((rp.getAreaCode() != null && rp.getAreaCode().length()>0) ? new ObjectMapper().readValue(rp.getAreaCode(), new TypeReference<List<List<String>>>(){}) : null);
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
        vo.setRegionPrice(list);
        return vo;
    }

    private BigDecimal getsoldNumCount(String serialNum) {
        List<OrderItem> list = orderItemService.lambdaQuery().eq(OrderItem::getProductSn, serialNum).select(OrderItem::getBuyCounts).list();
        if (list != null && list.size() > 0) {
            BigDecimal sum = list.stream()
                    .map(OrderItem::getBuyCounts) // 提取需要求和的属性值
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            return sum;
        }
        return BigDecimal.valueOf(0);
    }

    /**
     * 根据id获取商品
     *
     * @param productId
     * @param state
     * @return
     */
    @Override
    public Product getProductById(String productId, Integer state) {
        LambdaQueryChainWrapper<Product> q = lambdaQuery();
        q.eq(Product::getProductId, productId);
        if (state != null) {
            q.eq(Product::getState, state);
        }
        q.select(Product.class, f -> {
            return !f.getProperty().equals("productDescribe") && !f.getProperty().equals("remarks");
        });
        return q.one();
    }

//    /**
//     * 根据参数查询商品信息列表
//     *
//     * @param jsonObject
//     * @param q
//     * @return
//     */
//    @Override
//    public PageUtils listPlatformProductPageByClassId(JSONObject jsonObject, LambdaQueryWrapper<Product> q) {
//        Map<String, Object> innerMap = jsonObject.getInnerMap();
//        Integer page = (Integer) innerMap.get("page");
//        Integer limit = (Integer) innerMap.get("limit");
//        Integer mallType = mallConfig.mallType;
//        if (page == null || limit == null) {
//            page = 1;
//            limit = 10;
//        }
//        int start = (page - 1) * limit;
//        ListPlatformProductPageByClassIdDTO dto = com.baomidou.mybatisplus.core.toolkit.BeanUtils.mapToBean(innerMap, ListPlatformProductPageByClassIdDTO.class);
//        dto.setStart(start);
//        dto.setLimit(limit);
//        dto.setMallType(mallType);
//        List<PlatformDeviceListVO> vos = productMapper.listPlatformProductPageByClassId(dto);
//        int count = productMapper.countPlatformProductPageByClassId(dto);
//        if (CollectionUtils.isEmpty(vos)) {
//            PageUtils pageUtils = new PageUtils();
//            pageUtils.setList(new ArrayList<>());
//            pageUtils.setCurrPage(page);
//            pageUtils.setPageSize(limit);
//            return pageUtils;
//        }
//        PageUtils<PlatformDeviceListVO> pageUtils = new PageUtils<>();
//        pageUtils.setList(vos);
//        pageUtils.setTotalCount(count);
//        // 计算总页数
//        pageUtils.setTotalPage(totalPage);
//        pageUtils.setCurrPage(page);
//        pageUtils.setPageSize(limit);
//        return pageUtils;
//    }

    /**
     * 店铺商品分页列表（店铺）
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listShopManageDevicePage(JSONObject jsonObject, LambdaQueryWrapper<Product> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String productName = (String) innerMap.get("productName");
        String classId = (String) innerMap.get("classId");
        List<Integer> state = (ArrayList<Integer>) innerMap.get("state");
        List<Integer> productTypes = (ArrayList<Integer>) innerMap.get("productTypes");
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        String startModifiedDate = (String) innerMap.get("startModifiedDate");
        String endModifiedDate = (String) innerMap.get("endModifiedDate");
        String startPutawayDate = (String) innerMap.get("startPutawayDate");
        String endPutawayDate = (String) innerMap.get("endPutawayDate");
        String belowPrice = (String) innerMap.get("belowPrice");
        String abovePrice = (String) innerMap.get("abovePrice");
        Integer productType = (Integer) innerMap.get("productType");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        Integer isOneself = (Integer) innerMap.get("isOneself");
        q.like(productName != null, Product::getProductName, productName);
        q.isNotNull(isOneself != null && isOneself == 1, Product::getRelevanceNo);
        q.isNull(isOneself != null && isOneself == 0, Product::getRelevanceNo);
        q.like(StringUtils.isNotEmpty(keywords), Product::getProductName, keywords);
        q.orderByDesc(orderBy == 0, Product::getPutawayDate);
        q.orderByDesc(orderBy == 1, Product::getShopSort);
        q.orderByDesc(orderBy == 2, Product::getGmtCreate);
        q.orderByDesc(orderBy == 3, Product::getGmtModified);
        q.eq(Product::getMallType, mallConfig.mallType);
        q.eq(Product::getShopId, ThreadLocalUtil.getCurrentUser().getShopId());
        q.like(StringUtils.isNotEmpty(classId), Product::getClassPath, classId);
        q.in(!CollectionUtils.isEmpty(state), Product::getState, state);
        q.in(!CollectionUtils.isEmpty(productTypes), Product::getProductType, productTypes);
        q.between(StringUtils.isNotEmpty(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), Product::getGmtCreate, startCreateDate, endCreateDate);
        q.between(StringUtils.isNotEmpty(startModifiedDate) && StringUtils.isNotEmpty(endModifiedDate), Product::getGmtModified, startModifiedDate, endModifiedDate);
        q.between(StringUtils.isNotEmpty(startPutawayDate) && StringUtils.isNotEmpty(endPutawayDate), Product::getGmtModified, startPutawayDate, endPutawayDate);
        q.ge(StringUtils.isNotBlank(abovePrice), Product::getProductMinPrice, abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), Product::getProductMinPrice, belowPrice);
        q.eq(productType != null, Product::getProductType, productType);
        q.select(Product.class, f -> {
            return !f.getProperty().equals("productDescribe") && !f.getProperty().equals("remarks");
        });
        IPage<Product> page = this.page(new Query<Product>().getPage(jsonObject), q);
//        //封装vo
//        List<Product> records = page.getRecords();
//        if(records.size() == 0) return new PageUtils(page);
//        List<ShopManageProductListVO> vos = new ArrayList<>();
//        for (Product product : records) {
//            ShopManageProductListVO vo = new ShopManageProductListVO();
//            BeanUtils.copyProperties(product,vo);
//            Shop shop = shopService.getById(product.getShopId());
//            if(shop != null){
//                vo.setShopName(shop.getShopName());
//            }
//            vos.add(vo);
//        }
//        PageUtils pageUtils = new PageUtils(page);
//        pageUtils.setList(vos);
//        return pageUtils;
        return new PageUtils(page);
    }





    /**
     * 物资分页列表（店铺）
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listShopManageMaterialPage(JSONObject jsonObject, LambdaQueryWrapper<Product> q) {
        String shopId = ThreadLocalUtil.getCurrentUser().getShopId();
        Shop shop = shopService.getById(shopId);
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String productName = (String) innerMap.get("productName");
        String classId = (String) innerMap.get("classId");
        List<Integer> state = (ArrayList<Integer>) innerMap.get("state");
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String serialNum = (String) innerMap.get("serialNum");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        String startModifiedDate = (String) innerMap.get("startModifiedDate");
        String endModifiedDate = (String) innerMap.get("endModifiedDate");
        String startPutawayDate = (String) innerMap.get("startPutawayDate");
        String endPutawayDate = (String) innerMap.get("endPutawayDate");
        String belowPrice = (String) innerMap.get("belowPrice");
        String abovePrice = (String) innerMap.get("abovePrice");
        String supplierName = (String) innerMap.get("supplierName");
        Integer productType = (Integer) innerMap.get("productType");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        Integer isOneself = (Integer) innerMap.get("isOneself");
        q.like(productName != null, Product::getProductName, productName);
        if (isOneself != null) {
            if (isOneself == 0) {
                // 如果是3表示是确认过来的物资，不是自有的
                q.eq(Product::getSupplierSubmitState, 3);
            }
            // 如果是0表示是自有的
            if (isOneself == 1) {
                q.eq(Product::getSupplierSubmitState, 0);
            }
        }


        if (orderBy == null) {
            q.orderByDesc(Product::getPutawayDate);
        } else {
            q.orderByDesc(orderBy == 0, Product::getPutawayDate);
            q.orderByDesc(orderBy == 1, Product::getShopSort);
            q.orderByDesc(orderBy == 2, Product::getGmtCreate);
            q.orderByDesc(orderBy == 3, Product::getGmtModified);
        }
        q.eq(Product::getMallType, mallConfig.mallType);
        if (StringUtils.isNotEmpty(keywords)) {
            q.and(wrapper -> wrapper.like(Product::getProductName, keywords)
                    .or().like(Product::getSupplierName, keywords)
                    .or().like(Product::getSerialNum, keywords)
                    .or().like(Product::getSkuName, keywords));
        }
        q.eq(Product::getShopId, shopId);
        q.like(StringUtils.isNotEmpty(supplierName), Product::getSupplierName, supplierName);
        q.like(StringUtils.isNotEmpty(serialNum), Product::getSerialNum, serialNum);
        q.like(StringUtils.isNotEmpty(classId), Product::getClassPath, classId);
        q.in(!CollectionUtils.isEmpty(state), Product::getState, state);
        q.between(StringUtils.isNotEmpty(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), Product::getGmtCreate, startCreateDate, endCreateDate);
        q.between(StringUtils.isNotEmpty(startModifiedDate) && StringUtils.isNotEmpty(endModifiedDate), Product::getGmtModified, startModifiedDate, endModifiedDate);
        q.between(StringUtils.isNotEmpty(startPutawayDate) && StringUtils.isNotEmpty(endPutawayDate), Product::getGmtModified, startPutawayDate, endPutawayDate);
        q.ge(StringUtils.isNotBlank(abovePrice), Product::getProductMinPrice, abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), Product::getProductMinPrice, belowPrice);
        q.eq(productType != null, Product::getProductType, productType);
        q.select(Product.class, f -> {
            return !f.getProperty().equals("productDescribe") && !f.getProperty().equals("remarks");
        });
        IPage<Product> page = this.page(new Query<Product>().getPage(jsonObject), q);
        List<Product> records = page.getRecords();
        if (records != null && records.size() > 0) {
            for (Product record : records) {
                record.setShopState(shop.getState());
                ProductCategory s = JSON.parseObject(stringRedisTemplate.opsForValue().get("CLASSID:" + record.getClassId()), ProductCategory.class);
                if (s != null) {
                    record.setClassPathName(s == null ? "" : s.getClassPath());
                } else {
                    ProductCategory byId = productCategoryService.lambdaQuery()
                            .eq(ProductCategory::getClassId, record.getClassId())
                            .select(ProductCategory::getClassPath).one();
                    record.setClassPathName(byId == null ? "" : byId.getClassPath());
                    stringRedisTemplate.opsForValue().set("CLASSID:" + record.getClassId(), JSON.toJSONString(byId));
                }
                List<ProductSku> skuList = productSkuService.lambdaQuery()
                        .eq(ProductSku::getProductId, record.getProductId())
                        .select(ProductSku::getStock, ProductSku::getCostPrice,
                                ProductSku::getOriginalPrice, ProductSku::getSellPrice).list();
//                List<ProductSku> skuList = productSkuService.getProductSkuByProductId(record.getProductId(), null);
                if (skuList != null && skuList.size() > 0) {
                    ProductSku sku = skuList.get(0);
                    record.setStock(sku.getStock());
                    record.setCostPrice(sku.getCostPrice());
                    record.setOriginalPrice(sku.getOriginalPrice());
                    record.setSellPrice(sku.getSellPrice());
                    //record.setProfitPrice(sku.getSellPrice().subtract(sku.getCostPrice()));
                }
                //查询商品区域价格
                List<RegionPrice> list = regionPriceService.lambdaQuery().eq(RegionPrice::getProductId, record.getProductId()).list();
                if (list != null && list.size() > 0) {
                    for(RegionPrice rp:list){
                        try{
                            if(rp.getRegionName().equals("全区域")) {
                                rp.setSelectAddressOptionsAll((rp.getAreaCode() != null && rp.getAreaCode().length()>0) ? new ObjectMapper().readValue(rp.getAreaCode(), new TypeReference<List<String>>(){}) : null);
                            }else{
                                rp.setSelectAddressOptions((rp.getAreaCode() != null && rp.getAreaCode().length()>0) ? new ObjectMapper().readValue(rp.getAreaCode(), new TypeReference<List<List<String>>>(){}) : null);
                            }
                        }catch (Exception e){
                            e.printStackTrace();
                        }
                    }
                }
                record.setRegionPrice(list);
            }
        }

        return new PageUtils(page);
    }

    /**
     * 批量导入物资（店铺）
     *
     * @param dtos
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importBatchMaterial(List<ImportMaterialDTO> dtos) {
        if (mallConfig.isPlatformFee == 1) {
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            String shopId = user.getShopId();
            EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId())
                    .select(EnterpriseInfo::getEnterpriseId,
                            EnterpriseInfo::getShuDaoFlag,
                            EnterpriseInfo::getInteriorId,
                            EnterpriseInfo::getEnterpriseType).one();
            if (supplier == null) {
                throw new BusinessException("未查询到机构信息！");
            }
            if (supplier.getShuDaoFlag() == 1 || supplier.getInteriorId() != null) {
            } else {
                platformYearFeeService.checkYearShopIsOut(shopId);
            }
        }
        String shopId = ThreadLocalUtil.getCurrentUser().getShopId();
        for (ImportMaterialDTO dto : dtos) {
            Integer count = lambdaQuery().eq(Product::getRelevanceId, dto.getRelevanceId()).eq(Product::getProductType, dto.getProductType()).count();
            if (count > 0) continue;
            Product product = new Product();
            // 物资暂时只有一种前缀编号
            product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
            product.setProductName(dto.getProductTitle());
            product.setShopId(shopId);
            //确定商品按照店铺排序
            Shop shop = shopService.getById(shopId);
            if (shop.getIsBusiness() == 1) {
                product.setShopType(1);
            } else if (shop.getIsInternalShop() == 1) {
                product.setShopType(2);
            } else {
                product.setShopType(3);
            }
            product.setClassId(dto.getClassId());
            product.setProductType(dto.getProductType());
            product.setState(ProductEnum.STATE_STAY_PUTAWAY.getCode());
            product.setRelevanceName(dto.getProductTitle());
            product.setRelevanceId(dto.getRelevanceId());
//            product.setRelevanceNo(dto.getRelevanceNo());
            product.setProductInventoryId(dto.getProductInventoryId());
            product.setIsCompletion(ProductEnum.IS_COMPLETION_0.getCode());
            List<String> classPath = productCategoryService.getCategoryParentIdList(product.getClassId());
            String cp = "";
            if (!CollectionUtils.isEmpty(classPath)) {
                for (String s : classPath) {
                    cp += s + "/";
                }
            }
            product.setClassPath(cp.substring(0, cp.length() - 1));
            product.setClassPath(cp);
            int insert = this.baseMapper.insert(product);
            if (insert > 0) {
                // 修改分类有商品
                productCategoryService.updateCategoryYesProduct(dto.getClassId());
                ProductSku sku = new ProductSku();
                sku.setProductId(product.getProductId());
                sku.setSkuName(dto.getSpecTitle());
                sku.setStock(new BigDecimal("1"));
                sku.setUnit(dto.getUnit());
                sku.setProductType(dto.getProductType());
                sku.setState(PublicEnum.STATE_STOP.getCode());
                productSkuService.save(sku);
            }

        }
    }

    /**
     * "获取物资（通用）
     *
     * @param dto
     * @return
     */
    @Override
    public MaterInfoVO getMaterialInfo(GetMaterialInfoDTO dto) {
        MaterInfoVO vo = new MaterInfoVO();
        String productId = dto.getProductId();
        // 查询商品信息
        Product product = getById(productId);
        if (product == null) return vo;
        BeanUtils.copyProperties(product, vo);
        String shopNameById = shopService.getShopNameById(product.getShopId());
        vo.setShopName(shopNameById);
        // 查询分类路径
        List<String> classPath = productCategoryService.getCategoryParentIdList(product.getClassId());
        vo.setClassPath(classPath);
        ProductCategory byId = productCategoryService.lambdaQuery()
                    .eq(ProductCategory::getClassId, product.getClassId())
                    .select(ProductCategory::getClassPath).one();
        vo.setClassPathName(byId == null ? "" : byId.getClassPath());
        // 获取品牌名称
        if (StringUtils.isNotEmpty(product.getBrandId())) {
            Brand brand = brandService.getById(product.getBrandId());
            if (brand != null) {
                vo.setBrandName(brand.getName());
            }
        }
        // 如果品牌的不等于空
//        if(StringUtils.isNotEmpty(product.getBrandId())) {
//            Brand byId = brandService.getById(product.getBrandId());
//            if(byId == null){
//                product.setBrandId(null);
//            }else {
//                // 品牌不为空
//                if(byId.getName().equals(product.getBrandName())){
//                }else {
//                    // 如果不相等，则使用用户输入的品牌
//                    product.setBrandId(null);
//                }
//            }
//        }
        // 获取主图
        List<File> files = fileService.listFileByParameters(product.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode(), FileEnum.IS_MIN_YES.getCode(), FileEnum.TYPE_IMG.getCode(), FileEnum.IMG_TYPE_GENERAL.getCode());
        vo.setAdminFile(files);
        // 获取小图
        List<File> files2 = fileService.listFileByParameters(product.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode(), FileEnum.IS_MIN_NO.getCode(), FileEnum.TYPE_IMG.getCode(), FileEnum.IMG_TYPE_MIN.getCode());
        vo.setMinFile(files2);
        // 获取商品图片
        List<File> files3 = fileService.listFileByParameters(product.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode(), FileEnum.IS_MIN_NO.getCode(), FileEnum.TYPE_IMG.getCode(), FileEnum.IMG_TYPE_GENERAL.getCode());
        vo.setProductFiles(files3);
        // 获取规格信息
        List<ProductSku> productSkus = productSkuService.getProductSkuByProductId(product.getProductId(), null);
        if (!CollectionUtils.isEmpty(productSkus)) {
            CopyDeviceSkuVO cvo = new CopyDeviceSkuVO();
            BeanUtils.copyProperties(productSkus.get(0), cvo);
            BeanUtils.copyProperties(cvo, vo);


        }
        //查询商品区域价格
        List<RegionPrice> list = regionPriceService.lambdaQuery().eq(RegionPrice::getProductId, dto.getProductId()).list();
        if (list != null && list.size() > 0) {
            for(RegionPrice rp:list){
                try{
                    if(rp.getRegionName().equals("全区域")) {
                        rp.setSelectAddressOptionsAll(new ObjectMapper().readValue(rp.getAreaCode(), new TypeReference<List<String>>(){}));
                    }else{
                        rp.setSelectAddressOptions(new ObjectMapper().readValue(rp.getAreaCode(), new TypeReference<List<List<String>>>(){}));
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
        vo.setRegionPrice(list);


        return vo;

    }

    @Autowired
    PlatformYearFeeService platformYearFeeService;

    /**
     * 新增物资（店铺）
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createMaterial(CreateMaterialDTO dto) {
        if (mallConfig.isPlatformFee == 1) {
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            String shopId = user.getShopId();
            EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId())
                    .select(EnterpriseInfo::getEnterpriseId,
                            EnterpriseInfo::getShuDaoFlag,
                            EnterpriseInfo::getInteriorId,
                            EnterpriseInfo::getEnterpriseType).one();
            if (supplier == null) {
                throw new BusinessException("未查询到机构信息！");
            }
            if (supplier.getShuDaoFlag() == 1 || supplier.getInteriorId() != null) {
            } else {
                platformYearFeeService.checkYearShopIsOut(shopId);
            }
        }
        if (dto.getProductDescribe() != null && dto.getProductDescribe().length() > 2500) {
            throw new BusinessException("商品描述内容过大！");
        }
        List<String> classPath = dto.getClassPath();
        if (CollectionUtils.isEmpty(classPath)) {
            throw new BusinessException(400, "分类不能为空！");
        }
        ProductCategory one = productCategoryService.lambdaQuery().eq(ProductCategory::getClassId, classPath.get(0)).select(ProductCategory::getClassName).one();
        if (one == null) {
            throw new BusinessException(500, "分类不存在！");
        }
        if (dto.getProductType() == 0) {
            if (!one.getClassName().equals("低值易耗品")) {
                throw new BusinessException(500, "商品类型错误，不能" + one.getClassName() + "分类的商品！");
            }
        }
        if ((dto.getTaxRate()==null&&dto.getTaxRate().compareTo(BigDecimal.valueOf(0))==0)) {
            throw new BusinessException(500, "商品税率税率不能为空！");
        }

        String shopId = ThreadLocalUtil.getCurrentUser().getShopId();
        // 如果是主要材料判断是否有权限
        if ("主要材料".equals(one.getClassName())) {
            Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, shopId).select(Shop::getIsPrincipalMaterial).one();
            if (shop == null) {
                throw new BusinessException(500, "店铺不存在！");
            }
            Integer isPrincipalMaterial = shop.getIsPrincipalMaterial();
            if (isPrincipalMaterial == 0) {
                throw new BusinessException(500, "不能上架主要材料分类商品，请联系管理员！");
            }
        }


        // 处理去空格
        if (StringUtils.isNotBlank(dto.getProductName())) {
            dto.setProductName(dto.getProductName().trim());
        }
        if (StringUtils.isNotBlank(dto.getSkuName())) {
            dto.setSkuName(dto.getSkuName().trim());
        }
        if (mallConfig.isProductRepetitionCheck == 1) {
            Product product2 = lambdaQuery().eq(Product::getProductName, dto.getProductName()).eq(Product::getSkuName, dto.getSkuName()).eq(Product::getShopId, shopId).eq(Product::getProductType, dto.getProductType()).one();
            if (product2 != null) {
                throw new BusinessException(400, "商品名称重复");
            }
        }
        // 临购商品只能物资分公司上架
        if (dto.getProductType() != null && dto.getProductType() == 1) {
            if (!ThreadLocalUtil.getCurrentUser().getEnterpriseName().equals("四川路桥建设集团股份有限公司物资分公司") && !ThreadLocalUtil.getCurrentUser().getEnterpriseName().equals("四川路桥建设集团物资有限责任公司")) {
                throw new BusinessException(400, "无临购商品上架权限！");
            }
        }
        // 判断价格
        BigDecimal settlePrice = dto.getSettlePrice();
        BigDecimal sellPrice = dto.getSellPrice();
        //  结算价不能高于销售价
//        if (settlePrice.compareTo(sellPrice) == 1) {
//            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "结算价不能高于销售价！");
//        }
        // 计算结算价
//        List<SystemParam> materialRatio = systemParamService.listByCode("materialRatio", 1);
//        if (!CollectionUtils.isEmpty(materialRatio)) {
//            SystemParam systemParam = materialRatio.get(0);
//            BigDecimal multiplyPercent = CommonUtil.getMultiplyPercent(systemParam.getKeyValue(), sellPrice);
//            dto.setSettlePrice(multiplyPercent);
//        }
        // 保存商品信息
        Product product = new Product();
        BeanUtils.copyProperties(dto, product);
        product.setShopId(shopId);
        //确定商品按照店铺排序
        Shop shop = shopService.getById(shopId);
        if (shop.getIsBusiness() == 1) {
            product.setShopType(1);
        } else if (shop.getIsInternalShop() == 1) {
            product.setShopType(2);
        } else {
            product.setShopType(3);
        }

        product.setSupperBy(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        product.setSupplierName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
        // 处理分类路径

        // 处理品牌
        String newBrandName = dto.getBrandName();
        if (!StringUtils.isEmpty(newBrandName)) {
            Brand brand = brandService.lambdaQuery().eq(Brand::getName, newBrandName).eq(Brand::getClassId, product.getClassId()).one();
            if (brand == null) {
                Brand brand1 = new Brand();
                brand1.setName(newBrandName);
                brandService.save(brand1);
                product.setBrandId(brand1.getBrandId());
                product.setBrandName(newBrandName);
//                throw new BusinessException("品牌不存在");
            } else {
                product.setBrandId(brand.getBrandId());
                product.setBrandName(newBrandName);
            }
        }
        String cp = "";
        if (!CollectionUtils.isEmpty(classPath)) {
            for (String s : classPath) {
                cp += s + "/";
            }
        }
        product.setClassPath(cp.substring(0, cp.length() - 1));
        product.setIsCompletion(ProductEnum.IS_COMPLETION_1.getCode());
        product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
        product.setProductMinPrice(dto.getSellPrice());
        product.setState(ProductEnum.STATE_STAY_PUTAWAY.getCode());
        product.setProductMinImg(dto.getMinFile().get(0).getUrl());


        // 自动设置分类


        MaterialDtlDTO redisMaterialInfo = getRedisMaterialInfo(dto.getClassId(), dto.getRelevanceNo(), dto.getRelevanceName());
        product.setClassPath((String) redisMaterialInfo.getClassPath());
        product.setClassId((String) redisMaterialInfo.getClassId());

        product.setProductName(product.getRelevanceName());
        boolean save1 = save(product);
        if (!save1) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "保存失败！");
        }
        //保存商品区域价格
        List<BigDecimal> priceList = new ArrayList<>();
        List<RegionPrice> regionPrice = dto.getRegionPrice();
        for(RegionPrice rp:regionPrice){
            rp.setProductId(product.getProductId());
            rp.setArea((rp.getDetailAddress() != null && rp.getDetailAddress().size() > 0) ? String.join(",",rp.getDetailAddress()) : null);
            try {
                rp.setAreaCode((rp.getDetailAddress() != null && rp.getDetailAddress().size() > 0) ? new ObjectMapper().writeValueAsString(
                        rp.getSelectAddressOptionsAll() != null ? rp.getSelectAddressOptionsAll() : rp.getSelectAddressOptions()
                ) : null);
            } catch (Exception e) {
                e.printStackTrace();
            }
            rp.setAnnualizedRate(product.getAnnualizedRate());
            priceList.add(rp.getBonusTaxInPrice() != null ? rp.getBonusTaxInPrice():rp.getTaxInPrice());
        }
        regionPriceService.saveBatch(regionPrice);

        BigDecimal minPrice = priceList.stream().min(BigDecimal::compareTo).orElse(null);
        lambdaUpdate().eq(Product::getProductId, product.getProductId()).set(Product::getProductMinPrice,minPrice).update();

        // 保存成功应该修改该分类为有商品
        productCategoryService.updateCategoryYesProduct(dto.getClassId());

        // 保存主图
        File adminFile = dto.getAdminFile().get(0);
        adminFile.setRelevanceId(product.getProductId());
        boolean save2 = fileService.save(adminFile);
        if (!save2) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "主图保存失败！");
        }

        // 保存商品图片
        List<File> productFiles = dto.getProductFiles();
        for (File productFile : productFiles) {
            productFile.setRelevanceId(product.getProductId());
        }
        boolean save3 = fileService.saveBatch(productFiles);
        if (!save3) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品图片保存失败！");
        }

        // 保存小图
        File minFile = dto.getMinFile().get(0);
        minFile.setRelevanceId(product.getProductId());
        boolean save4 = fileService.save(minFile);
        if (!save4) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "小图保存失败！");
        }
        // 保存规格
        ProductSku productSku = new ProductSku();
        BeanUtils.copyProperties(dto, productSku);
        productSku.setProductId(product.getProductId());
        // 规格主图默认是商品的主图
        productSku.setSkuImg(minFile.getUrl());
        productSku.setState(PublicEnum.STATE_STOP.getCode());
        boolean save5 = productSkuService.save(productSku);
        if (!save5) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品保存失败！");
        }
    }

    /**
     * 修改物资（店铺）
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMaterial(UpdateMaterialDTO dto) {
        if (mallConfig.isPlatformFee == 1) {
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            String shopId = user.getShopId();
            EnterpriseInfo supplier = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId())
                    .select(EnterpriseInfo::getEnterpriseId,
                            EnterpriseInfo::getShuDaoFlag,
                            EnterpriseInfo::getInteriorId,
                            EnterpriseInfo::getEnterpriseType).one();
            if (supplier == null) {
                throw new BusinessException("未查询到机构信息！");
            }
            if (supplier.getShuDaoFlag() == 1 || supplier.getInteriorId() != null) {
            } else {
                platformYearFeeService.checkYearShopIsOut(shopId);
            }
        }
        if (dto.getProductDescribe() != null && dto.getProductDescribe().length() > 2500) {
            throw new BusinessException("商品描述内容过大！");
        }
        String shopId = ThreadLocalUtil.getCurrentUser().getShopId();
        // 处理去空格
        if (StringUtils.isNotBlank(dto.getProductName())) {
            dto.setProductName(dto.getProductName().trim());
        }
        if (StringUtils.isNotBlank(dto.getSkuName())) {
            dto.setSkuName(dto.getSkuName().trim());
        }
        if (mallConfig.isProductRepetitionCheck == 1) {
            Product product2 = lambdaQuery().eq(Product::getProductName, dto.getProductName()).eq(Product::getSkuName, dto.getSkuName()).eq(Product::getShopId, shopId).eq(Product::getProductType, dto.getProductType()).one();
            if (product2 != null) {
                // 如果不等于null判断是否是当前的
                if (product2.getProductId().equals(dto.getProductId())) {
                } else {
                    throw new BusinessException(400, "商品名称重复");
                }
            }
        }


//        //判断当季度是否已经修改过销售价格
//        List<RegionPrice> oldlist = regionPriceService.lambdaQuery().eq(RegionPrice::getProductId, dto.getProductId()).list();
//        Date oldTime = oldlist.get(0).getGmtModified() == null ? oldlist.get(0).getGmtCreate() : oldlist.get(0).getGmtModified();//上次修改时间
//
//        // 转换为LocalDate（忽略时间部分）
//        LocalDate checkDate = oldTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//        // 获取当前季度的起始和结束日期
//        QuarterRange currentQuarter = getCurrentQuarter();
//        // 检查日期是否在季度范围内（包含首尾）
//        boolean b = !checkDate.isBefore(currentQuarter.start) && !checkDate.isAfter(currentQuarter.end);
//
//        for (RegionPrice regionPrice : dto.getRegionPrice()) {//修改的新的区域价格
//            if(regionPrice.getRegionPriceId() == null && b){//有添加新的区域价格
//                throw new BusinessException(400, "本季度价格有修改");
//            }
//            RegionPrice one = regionPriceService.lambdaQuery().eq(RegionPrice::getRegionPriceId, regionPrice.getRegionPriceId()).one();
//            if(regionPrice.getTaxInPrice().compareTo(one.getTaxInPrice()) != 0  && b){
//                throw new BusinessException(400, "本季度价格有修改");
//            }
//        }

        BigDecimal sellPrice = dto.getSellPrice();
        BigDecimal originalPrice = dto.getOriginalPrice();
        //结算价不能高于销售价
//        if (sellPrice.compareTo(originalPrice) == 1) {
//            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "销售价不能高于原价！");
//        }

        //商品税率不能为空或者为0
        if (dto.getTaxRate() == null || dto.getTaxRate().compareTo(BigDecimal.ZERO) == 0) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品税率不能为空或者为0！");
        }
//        List<SystemParam> materialRatio = systemParamService.listByCode("materialRatio", 1);
//        if (!CollectionUtils.isEmpty(materialRatio)) {
//            SystemParam systemParam = materialRatio.get(0);
//            BigDecimal multiplyPercent = CommonUtil.getMultiplyPercent(systemParam.getKeyValue(), sellPrice);
//            dto.setSettlePrice(multiplyPercent);
//        }
        // 修改商品信息
        Product product = new Product();
        BeanUtils.copyProperties(dto, product);
//        product.setSupperBy(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
//        product.setSupplierName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
        // 处理分类路径
        List<String> classPath = dto.getClassPath();
        if (CollectionUtils.isEmpty(classPath)) {
            throw new BusinessException(400, "分类不能为空！");
        }
        String cp = "";
        if (!CollectionUtils.isEmpty(classPath)) {
            for (String s : classPath) {
                cp += s + "/";
            }
        }


        // 处理品牌
        String newBrandName = dto.getBrandName();
        if (!StringUtils.isEmpty(newBrandName)) {
            Brand brand = brandService.lambdaQuery().eq(Brand::getName, newBrandName).eq(Brand::getClassId, product.getClassId()).one();
            if (brand == null) {
                Brand brand1 = new Brand();
                brand1.setName(newBrandName);
                brandService.save(brand1);
                product.setBrandId(brand1.getBrandId());
                product.setBrandName(newBrandName);
//                throw new BusinessException("品牌不存在");
            } else {
                product.setBrandId(brand.getBrandId());
                product.setBrandName(newBrandName);
            }
        }
        product.setClassPath(cp.substring(0, cp.length() - 1));
        product.setIsCompletion(ProductEnum.IS_COMPLETION_1.getCode());
        product.setProductMinPrice(dto.getSellPrice());
        product.setProductMinImg(dto.getMinFile().get(0).getUrl());
        if (product.getState() == 1) {
            product.setState(3);
        }

        MaterialDtlDTO redisMaterialInfo = getRedisMaterialInfo(dto.getClassId(), dto.getRelevanceNo(), dto.getRelevanceName());
        product.setClassPath(redisMaterialInfo.getClassPath());
        product.setClassId(redisMaterialInfo.getClassId());

        product.setProductName(product.getRelevanceName());
        boolean u1 = updateById(product);
        if (!u1) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "修改失败！");
        }
        List<BigDecimal> priceList = new ArrayList<>();
        //更新商品区域价格
        List<RegionPrice> regionPrice = dto.getRegionPrice();
        for(RegionPrice rp:regionPrice){
            if(rp.getRegionPriceId() == null){//有添加新的区域价格
                //更新标签 确定修改价格
                lambdaUpdate().eq(Product::getProductId, dto.getProductId()).set(Product::getPriceState,1).update();
            }else{
                RegionPrice one = regionPriceService.lambdaQuery().eq(RegionPrice::getRegionPriceId, rp.getRegionPriceId()).one();
                if(rp.getTaxInPrice().compareTo(one.getTaxInPrice()) != 0){
                    //更新标签
                    lambdaUpdate().eq(Product::getProductId, dto.getProductId()).set(Product::getPriceState,1).update();
                }
            }
            rp.setArea((rp.getDetailAddress() != null && rp.getDetailAddress().size() > 0) ? String.join(",",rp.getDetailAddress()) : null);
            try {
                rp.setAreaCode((rp.getDetailAddress() != null && rp.getDetailAddress().size() > 0) ? new ObjectMapper().writeValueAsString(
                        rp.getSelectAddressOptionsAll() != null ? rp.getSelectAddressOptionsAll() : rp.getSelectAddressOptions()
                ) : null);
            } catch (Exception e) {
                e.printStackTrace();
            }
            rp.setAnnualizedRate(product.getAnnualizedRate());
            rp.setMarkUpNum(product.getMarkUpNum());
            priceList.add(rp.getBonusTaxInPrice() != null ? rp.getBonusTaxInPrice():rp.getTaxInPrice());
        }
        regionPriceService.updateBatchById(regionPrice);

        BigDecimal minPrice = priceList.stream().min(BigDecimal::compareTo).orElse(null);
        lambdaUpdate().eq(Product::getProductId, product.getProductId()).set(Product::getProductMinPrice,minPrice).update();

        // 保存前删除所有图片
        fileService.deleteBatchFileByRelevanceIdAndType(product.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode());

        // 保存主图
        File adminFile = dto.getAdminFile().get(0);
        adminFile.setFileId(null);
        adminFile.setRelevanceId(product.getProductId());
        boolean save2 = fileService.save(adminFile);
        if (!save2) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "图片修改失败！");
        }

        // 保存商品图片
        List<File> productFiles = dto.getProductFiles();
        for (File productFile : productFiles) {
            productFile.setFileId(null);
            productFile.setRelevanceId(product.getProductId());
        }
        boolean save3 = fileService.saveBatch(productFiles);
        if (!save3) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "图片修改失败！");
        }

        // 保存小图
        File minFile = dto.getMinFile().get(0);
        minFile.setFileId(null);
        minFile.setRelevanceId(product.getProductId());
        boolean save4 = fileService.save(minFile);
        if (!save4) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "图片修改失败！");
        }
        // 保存规格
        ProductSku productSku = new ProductSku();
        BeanUtils.copyProperties(dto, productSku);
        // 规格主图默认是商品的主图
        productSku.setSkuImg(minFile.getUrl());
        // 处理第二系数
        if (dto.getSecondUnitNum() == null) {
            productSku.setSecondUnitNum(null);
        }
        boolean u5 = productSkuService.updateById(productSku);
        if (!u5) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "修改失败！！");
        }
    }

    // 季度范围封装类
    private static class QuarterRange {
        final LocalDate start;
        final LocalDate end;
        QuarterRange(LocalDate start, LocalDate end) {
            this.start = start;
            this.end = end;
        }
    }
    // 获取当前季度的日期范围
    private static QuarterRange getCurrentQuarter() {
        LocalDate today = LocalDate.now();
        int currentYear = today.getYear();
        // 计算当前季度（1-4）
        int currentQuarter = (today.getMonthValue() - 1) / 3 + 1;
        // 计算季度起始月份（1月、4月、7月、10月）
        Month startMonth = Month.of((currentQuarter - 1) * 3 + 1);
        // 季度起始日（当月1号）
        LocalDate quarterStart = LocalDate.of(currentYear, startMonth, 1);
        // 季度结束日（季度最后一天）
        LocalDate quarterEnd = quarterStart.plusMonths(2)
                .with(TemporalAdjusters.lastDayOfMonth());
        return new QuarterRange(quarterStart, quarterEnd);
    }

    /**
     * 物资分页列表（平台）
     *
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */

    @Override
    public PageUtils listPlatformMaterialPage(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery) {
        // 获取总记录数
        int total = productMapper.listPlatformMaterialPageCount(jsonObject);
        pageUtils.pageDispose(jsonObject, total);
        Page<PlatformMaterialListVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        innerMap.put("isDelete", 0);
        List<PlatformMaterialListVO> list = productMapper.listPlatformMaterialPage(pages, jsonObject);
        if(list.size() > 0) {
            String upPercentage = systemParamService.listByCode(PublicEnum.UpPercentage.getRemark(), 1).get(0).getKeyValue2();
            BigDecimal  convertedUpPercentage = new BigDecimal(upPercentage.trim());
            String lowerPercentage = systemParamService.listByCode(PublicEnum.LowerPercentage.getRemark(), 1).get(0).getKeyValue2();
            BigDecimal  convertedLowerPercentage = new BigDecimal(lowerPercentage.trim());
            for (PlatformMaterialListVO platformMaterialListVO : list) {
                LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
                if (platformMaterialListVO.getBrandId() != null) {
                    queryWrapper.eq(Product::getBrandId, platformMaterialListVO.getBrandId());
                } else {
                    queryWrapper.isNull(Product::getBrandId);
                }
                if (platformMaterialListVO.getSkuName() != null) {
                    queryWrapper.eq(Product::getSkuName, platformMaterialListVO.getSkuName());
                } else {
                    queryWrapper.isNull(Product::getSkuName);
                }
                if (platformMaterialListVO.getRelevanceName() != null) {
                    queryWrapper.eq(Product::getRelevanceName, platformMaterialListVO.getRelevanceName());
                } else {
                    queryWrapper.isNull(Product::getRelevanceName);
                }
                if (platformMaterialListVO.getClassId() != null) {
                    queryWrapper.eq(Product::getClassId, platformMaterialListVO.getClassId());
                } else {
                    queryWrapper.isNull(Product::getClassId);
                }
                List<Product> result = productService.list(queryWrapper);
                if (result.size() > 0 && result.get(0).getProductAvePrice() != null) {
                    platformMaterialListVO.setProductAvePrice(result.get(0).getProductAvePrice());
                } else {
                    platformMaterialListVO.setProductAvePrice(platformMaterialListVO.getProductMinPrice());
                }
                //platformMaterialListVO.setAvePriceUpLimit(platformMaterialListVO.getProductAvePrice().multiply(BigDecimal.ONE.add(convertedUpPercentage.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP))));
                //platformMaterialListVO.setAvePriceLowerLimit(platformMaterialListVO.getProductAvePrice().multiply(BigDecimal.ONE.subtract(convertedLowerPercentage.divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP))));
            }
        }
        pages.setRecords(list);
        return new PageUtils(pages);
    }

    /**
     * 物资分页列表含有均价（平台）
     *
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    @Override
    public PageUtils listPlatformMaterialPagePVP(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery) {
        // 获取总记录数
        int total = productMapper.listPlatformMaterialPageCountPVP(jsonObject);
        pageUtils.pageDispose(jsonObject, total);
        Page<PlatformMaterialListVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        innerMap.put("isDelete", 0);
        pages.setRecords(productMapper.listPlatformMaterialPagePVP(pages, jsonObject));
        return new PageUtils(pages);
    }

    @Override
    public void outputExcel(JSONObject jsonObject, HttpServletResponse response) {
        Page<PlatformMaterialListVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        innerMap.put("isDelete", 0);
        List<PlatformMaterialListVO> dataList = productMapper.listPlatformMaterialPagePVP(null, jsonObject);
        try {
            XSSFWorkbook wb = new XSSFWorkbook();
            XSSFSheet sheet = wb.createSheet("商品信息数据库");
            XSSFRow headRow = sheet.createRow(0);
            headRow.createCell(0).setCellValue("序号");
            headRow.createCell(1).setCellValue("名称");
            headRow.createCell(2).setCellValue("物资分类");
            headRow.createCell(3).setCellValue("店铺名称");
            headRow.createCell(4).setCellValue("类型");
            headRow.createCell(5).setCellValue("商品均价(不含税)");
            headRow.createCell(6).setCellValue("销售价格");

            for (int i = 0; i < dataList.size(); i++) {
                Row dataRow = sheet.createRow(i + 1);
                PlatformMaterialListVO item = dataList.get(i);
                dataRow.createCell(0).setCellValue(i + 1);
                dataRow.createCell(1).setCellValue(item.getProductName());
                dataRow.createCell(2).setCellValue(item.getClassPath());
                dataRow.createCell(3).setCellValue(item.getShopName());
                dataRow.createCell(4).setCellValue(item.getIsOpenImport()==0?"历史上架":"外部获取");
                dataRow.createCell(5).setCellValue(convertToCellValue(item.getProductAvePrice()));
                dataRow.createCell(6).setCellValue(convertToCellValue(item.getProductMinPrice()));
            }
            // 自动调整列宽
            for (int i = 0; i < 6; i++) {
                sheet.autoSizeColumn(i);
            }

            String fileName = URLEncoder.encode("test.xlsx", "utf-8");
            response.setContentType("application/x-msdownload; charset=UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            OutputStream os = response.getOutputStream();
            wb.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String convertToCellValue(Object value) {
        if (value == null) {
            return "";
        }
        if (value instanceof BigDecimal) {
            // 保留两位小数，可根据需要调整
            return ((BigDecimal) value).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString();
        }
        return value.toString();
    }

    /**
     * 商品列表
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils materialPageList(JSONObject jsonObject, LambdaQueryWrapper<Product> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        if (StringUtils.isNotBlank((String) innerMap.get("brandId"))) {
            Brand brand = brandService.getById((String) innerMap.get("brandId"));
            innerMap.put("brandName", brand.getName());
        } else if (StringUtils.isNotBlank((String) innerMap.get("brandName"))) {
            innerMap.put("brandName", innerMap.get("brandName"));
        }
        //2025-1-15新增判断，屏蔽分子公司店铺，分公司自营店的店铺id是 1645601878095495170
        //isBusinessShip  存储的是所有自营店的店铺id ,如果参数表中没有分公司的远程机构id，则排除分公司自营店的店铺id
        ArrayList<String> s = shopBusinessService.getStopBusiness();
        if (s != null && s.size() > 0) {
            innerMap.put("isBusinessShip", s);
        }
        Integer mallType = mallConfig.mallType;
        innerMap.put("mallType", mallType);
        String orderBy = (String) innerMap.get("orderBy");
        if (StringUtils.isEmpty(orderBy)) {
            innerMap.put("orderBy", 0);
        } else {
            //价格排序
            if (orderBy.equals("1-desc")) {
                innerMap.put("orderBy", 11);
            }
            if (orderBy.equals("1-asc")) {
                innerMap.put("orderBy", 10);
            }
            //销量排序
            if (orderBy.equals("3-desc")) {
                innerMap.put("orderBy", 31);
            }
            if (orderBy.equals("3-asc")) {
                innerMap.put("orderBy", 30);
            }
            if (orderBy.equals("2-desc")) {
                innerMap.put("orderBy", 21);
            }
            if (orderBy.equals("2-asc")) {
                innerMap.put("orderBy", 20);
            }
            if (orderBy.equals("4-desc")) {
                innerMap.put("orderBy", 41);
            }
            if (orderBy.equals("4-asc")) {
                innerMap.put("orderBy", 40);
            }
            if (orderBy.startsWith("0")) {
                innerMap.put("orderBy", 0);
            }
        }
        // 编号和关键词查询去空格
        if (innerMap.get("keywords") != null) {
            innerMap.put("keywords", (String) innerMap.get("keywords").toString().trim());
        }
        if (innerMap.get("serialNum") != null) {
            innerMap.put("serialNum", (String) innerMap.get("serialNum").toString().trim());
        }
        int count = productMapper.listHomeMaterialPageCount(innerMap);
        pageUtils.pageDispose(jsonObject, count);
        Page<WMaterialVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<WMaterialVO> vos = productMapper.listHomeMaterialPage(pages, innerMap);

//        for (WMaterialVO vo : vos) {
//            List<RegionPrice> list = regionPriceService.lambdaQuery().eq(RegionPrice::getProductId, vo.getProductId()).list();
//            if(list != null && list.size()>0){
//                BigDecimal min = list.get(0).getBonusTaxInPrice() != null ? list.get(0).getBonusTaxInPrice() : list.get(0).getTaxInPrice();
//                vo.setProductMinPrice(min);
//                for (RegionPrice regionPrice : list) {
//                    BigDecimal price = regionPrice.getBonusTaxInPrice() != null ? regionPrice.getBonusTaxInPrice() : regionPrice.getTaxInPrice();
//                    if(price.compareTo(min) == -1){
//                        min = price;
//                        vo.setProductMinPrice(min);
//                    }
//                }
//            }else{
//                vo.setSellPrice(vo.getOriginalPrice());
//                vo.setProductMinPrice(vo.getOriginalPrice());
//                vo.setOriginalPrice(null);
//            }
//        }

        //物资需求：登录用户看到销售原价，未登录用户看到商品的原价
        try {
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            if (user == null) {
                for (WMaterialVO vo : vos) {
                    vo.setSellPrice(vo.getOriginalPrice());
                    vo.setProductMinPrice(vo.getOriginalPrice());
                    vo.setOriginalPrice(null);
                }
            } else {
//                    Stream<String> productIds = vos.stream().map(item -> item.getProductId());
//                    List<ProductCollect> list = productCollectService.lambdaQuery()
//                            .eq(ProductCollect::getUserId, user.getUserId())
//                            .in(ProductCollect::getProductId, productIds)
//                            .select(ProductCollect::getProductId)
//                            .groupBy(ProductCollect::getProductId).list();
//                    List<String> collect = list.stream().map(ProductCollect::getProductId).collect(Collectors.toList());
//                    for (WMaterialVO vo : vos) {
//                        if (collect.contains(vo.getProductId())){
//                            vo.setCollect(true);
//                        }
//                    }
                for (WMaterialVO vo : vos) {
                    Integer count1 = productCollectService.lambdaQuery()
                            .eq(ProductCollect::getUserId, user.getUserId())
                            .eq(ProductCollect::getProductId, vo.getProductId()).count();
                    if (count1 > 0) {
                        vo.setCollect(true);
                    }
                }

            }
        } catch (BusinessException e) {
            for (WMaterialVO vo : vos) {
                vo.setSellPrice(vo.getOriginalPrice());
                vo.setProductMinPrice(vo.getOriginalPrice());
                vo.setOriginalPrice(null);
            }
        }

        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    /**
     * 根据商品id获取商品列表
     *
     * @param productId
     * @return
     */
    @Override
    public IndexMaterialVO getIndexMaterialVO(String productId) {
        IndexMaterialVO vo = new IndexMaterialVO();
        Product product = lambdaQuery()
                .eq(Product::getProductId, productId)
                .eq(Product::getState, ProductEnum.STATE_PUTAWAY.getCode())
                .select(Product::getProductId, Product::getSerialNum, Product::getShopType, Product::getProductName, Product::getShopId, Product::getProductMinImg)
                .one();
        if (product == null) return vo;
        vo.setProductId(product.getProductId());
        vo.setSerialNum(product.getSerialNum());
        vo.setProductName(product.getProductName());
        vo.setShopId(product.getShopId());
        vo.setProductMinImg(product.getProductMinImg());
        vo.setShopType(product.getShopType());
        // 获取商品小图
//            File one = fileService.lambdaQuery().eq(File::getRelevanceId, product.getProductId())
//                    .eq(File::getRelevanceType, FileEnum.RELEVANCE_TYPE_PRODUCT.getCode())
//                    .eq(File::getFileType, FileEnum.TYPE_IMG.getCode())
//                    .eq(File::getImgType, FileEnum.IMG_TYPE_MIN.getCode())
//                    .eq(File::getMallType, mallConfig.mallType).one();
        ProductSku one = productSkuService.lambdaQuery()
                .eq(ProductSku::getProductId, product.getProductId())
                .select(ProductSku::getSkuName, ProductSku::getSellPrice, ProductSku::getOriginalPrice)
                .one();
        if (one != null) {
            vo.setSkuName(one.getSkuName());
            if (ThreadLocalUtil.getCurrentUser() == null) {
                vo.setSellPrice(one.getOriginalPrice());
            } else {
                vo.setSellPrice(one.getSellPrice());
            }
        }
        return vo;
    }


    /**
     * 获取商品信息列表
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listProductFullInfoVOPage(JSONObject jsonObject, LambdaQueryWrapper<Product> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer mallType = mallConfig.mallType;
        innerMap.put("mallType", mallType);

        int count = productMapper.listProductFullInfoVOPageCount(innerMap);
        pageUtils.pageDispose(jsonObject, count);
        Page<ProductFullInfoVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ProductFullInfoVO> vos = productMapper.listProductFullInfoVOPage(pages, innerMap);
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    /**
     * 批量修改
     *
     * @param products
     */
    @Override
    public void updateBatch(List<Product> products) {
        for (Product product : products) {
            product.setSynthesisSort(product.getSort());
        }
        updateBatchById(products);
    }


    /**
     * 根据id查询商品并且排除商品描述
     *
     * @param productId
     * @return
     */
    @Override
    public Product getProductExcludeRemarkById(String productId) {
        LambdaQueryChainWrapper<Product> q = lambdaQuery().eq(Product::getProductId, productId);
        q.select(Product.class, f -> {
            return !f.getProperty().equals("productDescribe") && !f.getProperty().equals("remarks");
        });
        return q.one();
    }


    /**
     * 获取商品交易记录
     *
     * @param jsonObject
     * @return
     */
    @Override
    public PageUtils getProductDetailDealRecord(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String productId = (String) innerMap.get("productId");
        Integer mallType = mallConfig.mallType;
        innerMap.put("mallType", mallType);
        innerMap.put("productId", productId);
//        innerMap.put("orderClass", "3");
        int count = orderItemMapper.getProductDetailDealRecordCount(innerMap);
        pageUtils.pageDispose(jsonObject, count);
        Page<ProductDetailDealRecordVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<ProductDetailDealRecordVO> vos = orderItemMapper.getProductDetailDealRecord(pages, innerMap);
        if (mallType == 1) {
            for (ProductDetailDealRecordVO vo : vos) {
                User user = userService.lambdaQuery().eq(User::getUserId, vo.getUserId()).select(User::getNickName, User::getUserImg).one();
                if (user != null) {
                    vo.setNickName(user.getNickName());
                    vo.setUserImg(user.getUserImg());
                }
            }
        }
        if (mallType == 0) {
            // 发送请求查询用户信息
            List<ProductDetailDealRecordVO> productDetailDealRecordVOS = productService.getRecordListUserInfo(vos);
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            if (user == null) {
                for (ProductDetailDealRecordVO recordVO : vos) {
                    recordVO.setProductPrice(null);
                }
            }
            pages.setRecords(productDetailDealRecordVOS);
            return new PageUtils(pages);
        }
        pages.setRecords(vos);
        return new PageUtils(pages);
    }


    /**
     * 保存导入的物资商品
     *
     * @param material
     * @param productType
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveImportMaterial(Material material, Integer productType) {
        String shopId = ThreadLocalUtil.getCurrentUser().getShopId();
        Integer count = lambdaQuery().eq(Product::getProductName, material.getProductName()).eq(Product::getShopId, shopId).eq(Product::getProductType, productType).count();
        if (count > 0) {
            throw new BusinessException(400, "商品名称重复");
        }
        // 保存商品信息
        Product product = new Product();
        product.setProductName(material.getProductName());
        if (StringUtils.isEmpty(material.getClassNamePath())) {
            throw new BusinessException(400, "分类不能为空！");
        }
        String[] split = material.getClassNamePath().split("/");
        String className = split[split.length - 1];
        ProductCategory productCategory = productCategoryService.getCategoryByClassNameAndLevel(className, split.length, productType);
        if (productCategory == null) {
            throw new BusinessException(400, "分类不存在！");
        }
        List<String> categoryParentIdList = productCategoryService.getCategoryParentIdList(productCategory.getClassId());
        // 处理分类路径
        String cp = "";
        if (!CollectionUtils.isEmpty(categoryParentIdList)) {
            for (String s : categoryParentIdList) {
                cp += s + "/";
            }
        }
        Brand brandByBrandName = brandService.getBrandByBrandName(material.getBrandName());
        if (brandByBrandName != null) {
            product.setBrandId(brandByBrandName.getBrandId());
        }
        product.setSupperBy(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        product.setSupplierName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
        product.setBrandName(material.getBrandName());
        product.setShopId(shopId);
        //确定商品按照店铺排序
        Shop shop = shopService.getById(shopId);
        setProductTaxRate(shop.getEnterpriseId(),product);
        if (shop.getIsBusiness() == 1) {
            product.setShopType(1);
        } else if (shop.getIsInternalShop() == 1) {
            product.setShopType(2);
        } else {
            product.setShopType(3);
        }
        product.setClassId(productCategory.getClassId());
        product.setProductDescribe(material.getProductDescribe());
        product.setClassPath(cp.substring(0, cp.length() - 1));
        product.setIsCompletion(ProductEnum.IS_COMPLETION_0.getCode());
        product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
        product.setState(ProductEnum.STATE_STAY_PUTAWAY.getCode());
        product.setProductType(productType);
        product.setShopSort(material.getShopSort());
        product.setProductMinPrice(material.getProductMinPrice());
        boolean save = save(product);
        if (!save) {
            throw new BusinessException(400, "商品保存错误!");
        }
        // 保存成功应该修改该分类为有商品
        productCategoryService.updateCategoryYesProduct(productCategory.getClassId());

        // 保存规格
        ProductSku productSku = new ProductSku();
        BeanUtils.copyProperties(material, productSku);
        productSku.setProductId(product.getProductId());
        productSku.setProductType(productType);
        // 规格主图默认是商品的主图
        productSku.setState(PublicEnum.STATE_STOP.getCode());
        boolean save5 = productSkuService.save(productSku);
        if (!save5) {
            throw new BusinessException(400, "商品保存错误!");
        }
    }

    /**
     * excel导入物资
     *
     * @param shopMaterial
     * @param shopId
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveImportSupplierMaterial(ShopMaterial shopMaterial, String shopId) {
        if (StringUtils.isBlank(shopId)) {
            throw new BusinessException(500, "未选择店铺！");
        }
        if (mallConfig.isProductRepetitionCheck == 1) {
            Integer count = lambdaQuery()
                    .eq(Product::getProductName, shopMaterial.getProductName())
                    .eq(Product::getSkuName, shopMaterial.getSkuName())
                    .eq(Product::getShopId, shopId).count();
            if (count > 0) {
                throw new BusinessException(500, "商品名称重复");
            }
        }
        // 保存商品信息
        Product product = new Product();
        BeanUtils.copyProperties(shopMaterial, product);

        // 处理分类路径
        if (StringUtils.isEmpty(shopMaterial.getClassNamePath())) {
            throw new BusinessException(400, "分类不能为空！");
        }
        String[] split = shopMaterial.getClassNamePath().trim().split("/");
        ProductCategory productCategory = productCategoryService.getDataByClassPathName(shopMaterial.getClassNamePath().trim(), split.length, 1);
//        ProductCategory productCategory = productCategoryService.getCategoryByClassNameAndLevel(className, split.length, null);
        if (productCategory == null) {
            throw new BusinessException(400, "分类不存在！");
        }
        List<String> categoryParentIdList = productCategoryService.getCategoryParentIdList(productCategory.getClassId());
        // 处理分类路径
        String cp = "";
        if (!CollectionUtils.isEmpty(categoryParentIdList)) {
            for (String s : categoryParentIdList) {
                cp += s + "/";
            }
        }

        //保存物料信息
        String materialName = shopMaterial.getMaterialName();
        String materialNo = shopMaterial.getMaterialNo();
        if (StringUtils.isNotBlank(materialName)&&StringUtils.isNotBlank(materialNo)) {
            String s = stringRedisTemplate.opsForValue().get(productCategory.getClassId() + ":" +materialNo + ":" + materialName);
            if (StringUtils.isNotBlank(s)) {
                try {
                    MaterialVo materialVo = new ObjectMapper().readValue(s, MaterialVo.class);
                    product.setRelevanceId(materialVo.getBillId());
                    product.setRelevanceNo(materialVo.getBillNo());
                    product.setRelevanceName(materialVo.getMaterialName());
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            } else {
                MaterialVo materialVo = saveMaterialInfo(productCategory.getClassId(),materialNo,materialName);
                if (materialVo != null) {
                    product.setRelevanceName(materialVo.getMaterialName());
                    product.setRelevanceId(materialVo.getBillId());
                    product.setRelevanceNo(materialVo.getBillNo());
                }
            }

        }else {
            throw new BusinessException(500,"物料名称和物料编码不能为空！");
        }
        String classId = productCategory.getClassId();
        if (StringUtils.isNotBlank(shopMaterial.getBrandName())) {
            Brand brand = brandService.lambdaQuery().eq(Brand::getName, shopMaterial.getBrandName()).eq(Brand::getClassId, classId).one();
//            Brand brand = brandService.findByClassIdAndBrandName(classId, shopMaterial.getBrandName());
            if (brand != null) {
                product.setBrandId(brand.getBrandId());
                product.setBrandName(brand.getName());
            } else {
                Brand newBrand = new Brand();
                newBrand.setClassId(classId);
                newBrand.setClassPath(cp);
                newBrand.setName(shopMaterial.getBrandName());
                newBrand.setClassName(productCategory.getClassName());
                newBrand.setState(1);
                brandService.save(newBrand);
                product.setBrandId(newBrand.getBrandId());
                product.setBrandName(newBrand.getName());
            }
        }


        UserLogin user = ThreadLocalUtil.getCurrentUser();
        product.setSupperBy(user.getEnterpriseId());
        product.setSupplierName(user.getEnterpriseName());
        product.setProductType(0);
        product.setClassPath(cp.substring(0, cp.length() - 1));
        product.setIsCompletion(0);
        product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
        product.setShopId(shopId);
        //确定商品按照店铺排序
        Shop shop = shopService.getById(shopId);
        setProductTaxRate(shop.getEnterpriseId(),product);
        if (shop.getIsBusiness() == 1) {
            product.setShopType(1);
        } else if (shop.getIsInternalShop() == 1) {
            product.setShopType(2);
        } else {
            product.setShopType(3);
        }
        product.setProductMinPrice(shopMaterial.getSellPrice());
        product.setClassId(productCategory.getClassId());
        // 设置为-1提交，确认了才可
        product.setState(-1);
        product.setSupplierSubmitState(1);
        boolean save1 = save(product);
        if (!save1) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "保存失败！");
        }

        // 保存规格
        ProductSku productSku = new ProductSku();
        BeanUtils.copyProperties(shopMaterial, productSku);
        productSku.setProductId(product.getProductId());
        productSku.setSCostPrice(productSku.getCostPrice());
        productSku.setSSellPrice(productSku.getSellPrice());
        // 规格主图默认是商品的主图
        productSku.setState(PublicEnum.STATE_STOP.getCode());
        boolean save5 = productSkuService.save(productSku);
        if (!save5) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品保存失败！");
        }
    }

    //回调函数
    private MaterialVo saveMaterialInfo(String classId,String materialNo,String materialName) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("isActive", 1);
        hashMap.put("pageIndex", 1);
        hashMap.put("pageSize", 100);
        hashMap.put("classId", classId);
        hashMap.put("materialNo", materialNo.trim());
        hashMap.put("materialName", materialName);
        //根据参数查询物料信息
        List<MaterialVo> materialVos = productCategoryService.selectMaterialByclassId(hashMap);
        //查询集合中是否有对应物料
        if (materialVos != null && materialVos.size() > 0) {
            for (MaterialVo materialVo : materialVos) {
                if (materialVo.getMaterialName().equals(materialName)) {
                    return materialVo;
                }
            }
        }
        throw new BusinessException(500, "对应物料名称不存在");
    }

    /**
     * 补充成交记录用户信息
     *
     * @param dtos
     * @return
     */
    @Override
    public List<ProductDetailDealRecordVO> getRecordListUserInfo(List<ProductDetailDealRecordVO> dtos) {
        for (ProductDetailDealRecordVO vo : dtos) {
            User user = userService.lambdaQuery().eq(User::getUserId, vo.getUserId()).select(User::getNickName, User::getUserImg).one();
            if (user != null) {
                vo.setNickName(user.getNickName());
                vo.setUserImg(user.getUserImg());
            }
        }
        return dtos;
    }

    /**
     * 外部批量导入物资
     *
     * @param dtos
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportOuterProductVO> outerImportBatchMaterial(List<CuterCreateMaterialDTO> dtos, HttpServletRequest request) {
        ProductService productService = SpringBeanUtil.getBean(ProductService.class);
        ArrayList<ImportOuterProductVO> vos = new ArrayList<>();
        for (CuterCreateMaterialDTO dto : dtos) {
            ImportOuterProductVO vo = new ImportOuterProductVO();
            try {
                productService.outerImportMaterial(dto, vo, request);
            } catch (Exception e) {
                if (vo.getCode() == 50101) {
                    // 触发保存
                    ErrorInfo errorInfo = new ErrorInfo();
                    errorInfo.setBusinessType(1);
                    errorInfo.setErrorRqJson(JSON.toJSONString(dto));
                    errorInfo.setCreateTime(new Date());
                    errorInfo.setIsDispose(0);
                    errorInfo.setMethodName("outerImportBatchMaterial");
                    errorInfo.setErrorInfo(e.getMessage());
                    errorInfoService.save(errorInfo);
                }
                vo.setProductName(dto.getProductName());
                vo.setMessage(e.getMessage());
                if (vo.getCode() != null && vo.getCode() == 200) {
                    vo.setCode(500);
                }
            }
            vos.add(vo);
        }
        return vos;
    }

    /**
     * 外部导入物资
     *
     * @param dto
     * @param vo
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void outerImportMaterial(CuterCreateMaterialDTO dto, ImportOuterProductVO vo, HttpServletRequest request) {
        String codeByPrefix = CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark());
        vo.setCode(200);
        vo.setMessage("操作成功！");
        vo.setProductCode(codeByPrefix);
        vo.setProductName(dto.getProductName());
        vo.setOuterProductCode(dto.getOuterProductCode());
        if (dto.getProductDescribe() != null && dto.getProductDescribe().length() > 100000) {
            vo.setCode(50102);
            throw new BusinessException("商品描述内容过大！");
        }
        if (mallConfig.isProductRepetitionCheck == 1) {
            Integer count = lambdaQuery().eq(Product::getProductName, dto.getProductName()).eq(Product::getSkuName, dto.getSkuName()).eq(Product::getShopId, dto.getShopId()).eq(Product::getProductType, 0).count();
            if (count > 0) {
                vo.setCode(50100);
                throw new BusinessException("商品名称重复");
            }
        }
        ProductCategory productCategory = productCategoryService.lambdaQuery().eq(ProductCategory::getClassPath, dto.getClassNamePath()).one();
        if (productCategory == null) {
            vo.setCode(50090);
            throw new BusinessException("分类不存在！");
        }
        if (dto.getBasicsMaterialNo() == null || dto.getBasicsMaterialNo().trim().length() < 1) {
            vo.setCode(50094);
            throw new BusinessException("物资基础库编号不能为空！");
        }

        MaterialDtlDTO materialDtlDTO = getRedisMaterialInfo(productCategory.getClassId(), dto.getBasicsMaterialNo().trim(), dto.getBasicsMaterialName().trim());
        // 保存商品信息
        Product product = new Product();
        BeanUtils.copyProperties(dto, product);
        // 默认都是已确认的
        product.setSerialNum(codeByPrefix);
        //应物资公司要求：修改为待确认
        product.setSupplierSubmitState(2);
        product.setClassId(productCategory.getClassId());
//        Map map = list.get(0);
        product.setRelevanceName((String) materialDtlDTO.getMaterialName());
        product.setRelevanceNo((String) materialDtlDTO.getBillNo());
        product.setRelevanceId((String) materialDtlDTO.getBillId());
        if (mallConfig.isBusinessOrg == 1) {
            if (product.getShopId().equals("1645601878095495170")) {
                product.setShopId(mallConfig.businessShopId);
            }
        }
        Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, product.getShopId())
                .select(Shop::getIsBusiness, Shop::getIsInternalShop).one();
        if (shop.getIsBusiness() == 1) {
            product.setShopType(1);
        } else if (shop.getIsInternalShop() == 1) {
            product.setShopType(2);
        } else {
            product.setShopType(3);
        }

        product.setProductType(0);
        // 处理分类路径
        List<String> classPath = productCategoryService.getCategoryParentIdList(productCategory.getClassId());
        String cp = "";
        if (!CollectionUtils.isEmpty(classPath)) {
            for (String s : classPath) {
                cp += s + "/";
            }
        }
        if (CollectionUtils.isEmpty(classPath)) {
            vo.setCode(50090);
            throw new BusinessException("分类不存在");
        }
        String supplier_private_key = request.getHeader("supplier_private_key");
        String shopIdR = request.getHeader("shop_id");
        PrivateKeySupplier supplier = privateKeySupplierService.lambdaQuery().
                eq(PrivateKeySupplier::getPrivateKey, supplier_private_key)
                .eq(PrivateKeySupplier::getShopId, shopIdR).select(PrivateKeySupplier::getSupplierId, PrivateKeySupplier::getSupplierName).one();
        if (supplier == null) {
            vo.setCode(50097);
            throw new BusinessException("未配置供应商id！");
        }
        product.setSupperBy(supplier.getSupplierId());
        product.setSupplierName(supplier.getSupplierName());
        product.setClassPath(cp.substring(0, cp.length() - 1));


        product.setIsCompletion(ProductEnum.IS_COMPLETION_1.getCode());
//        product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
        Integer isPutaway = dto.getIsPutaway();
        product.setState(-1);
//        if (isPutaway != null && isPutaway == 1) {
//            product.setState(3);
//        } else {
//            product.setState(ProductEnum.STATE_STAY_PUTAWAY.getCode());
//        }
        product.setProductMinImg(dto.getMinFile().get(0).getUrl());
        product.setMallType(0);
        product.setIsOpenImport(1);
        product.setProductMinPrice(dto.getSellPrice());


//        if (mallConfig.isApiImportProductBradDispose == 2) {
//            if (StringUtils.isNotBlank(dto.getBrandName())) {
//                Brand brand = brandService.lambdaQuery().eq(Brand::getName, dto.getBrandName().trim()).eq(Brand::getClassId, productCategory.getClassId()).one();
//                if (brand == null) {
//                    vo.setErrorCode(50098);
//                    throw new BusinessException("对应分类品牌不存在！");
//                } else {
//                    product.setBrandId(brand.getBrandId());
//                    product.setBrandName(dto.getBrandName().trim());
//                }
//            }
//        }

//        if (mallConfig.isApiImportProductBradDispose == 1) {
        if (StringUtils.isNotBlank(dto.getBrandName())) {
            // 处理品牌
            String brandName = dto.getBrandName();
            String newBrandName = brandName.trim();
            Brand brand = brandService.lambdaQuery().eq(Brand::getName, newBrandName).eq(Brand::getClassId, productCategory.getClassId()).one();
            if (brand == null) {
                Brand brand1 = new Brand();
                brand1.setName(newBrandName);
                brand1.setClassId(product.getClassId());
                String className = productCategoryService.getById(productCategory.getClassId()).getClassName();
                brand1.setClassName(className);
                brand1.setClassPath(product.getClassPath());
                brandService.save(brand1);
                product.setBrandId(brand1.getBrandId());
                product.setBrandName(newBrandName);
            } else {
                product.setBrandId(brand.getBrandId());
                product.setBrandName(newBrandName);
            }
        }
//        }


        // 处理计量单位
        String unitTrim = dto.getUnit().trim();
        SystemParam materialUnit = systemParamService.lambdaQuery().eq(SystemParam::getKeyValue, unitTrim).eq(SystemParam::getCode, "materialUnit").one();
        if (materialUnit == null) {
            SystemParam systemParam = new SystemParam();
            systemParam.setName("计量单位");
            systemParam.setCode("materialUnit");
            systemParam.setKeyValue(unitTrim);
            systemParam.setKeyValue2(unitTrim);
            systemParam.setMaintain(1);
            systemParam.setType(1);
            systemParamService.save(systemParam);
        }


        boolean save1 = save(product);
        if (!save1) {
            vo.setCode(50091);
            throw new BusinessException("商品保存失败！");
        }
        // 保存成功应该修改该分类为有商品
        productCategoryService.updateCategoryYesProduct(productCategory.getClassId());

        // 保存主图
        ImportProductVO importProductVO = dto.getAdminFile().get(0);
        File adminFile = new File();
        BeanUtils.copyProperties(importProductVO, adminFile);
        adminFile.setIsMain(1);
        adminFile.setRelevanceType(1);
        adminFile.setFileType(1);
        adminFile.setImgType(0);
        adminFile.setRelevanceId(product.getProductId());
        boolean save2 = fileService.save(adminFile);
        if (!save2) {
            vo.setCode(50092);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "主图保存失败！");
        }

        // 保存商品图片
        List<ImportProductVO> productFileVOs = dto.getProductFiles();
        ArrayList<File> productFile = new ArrayList<>();
        for (ImportProductVO productFileVO : productFileVOs) {
            File file = new File();
            BeanUtils.copyProperties(productFileVO, file);
            file.setIsMain(0);
            file.setRelevanceType(1);
            file.setFileType(1);
            file.setImgType(0);
            file.setRelevanceId(product.getProductId());
            productFile.add(file);
        }
        boolean save3 = fileService.saveBatch(productFile);
        if (!save3) {
            vo.setCode(50093);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品图片保存失败！");
        }

        // 保存小图
        ImportProductVO minFileVO = dto.getMinFile().get(0);
        File minFile = new File();
        BeanUtils.copyProperties(minFileVO, minFile);
        minFile.setIsMain(0);
        minFile.setRelevanceType(1);
        minFile.setFileType(1);
        minFile.setImgType(1);
        minFile.setRelevanceId(product.getProductId());
        boolean save4 = fileService.save(minFile);
        if (!save4) {
            vo.setCode(50094);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "小图保存失败！");
        }
        // 保存规格
        ProductSku productSku = new ProductSku();
        BeanUtils.copyProperties(dto, productSku);
        productSku.setProductId(product.getProductId());
        productSku.setSkuImg(minFile.getUrl());
        productSku.setState(PublicEnum.STATE_STOP.getCode());
        productSku.setUnit(unitTrim);
        productSku.setSCostPrice(productSku.getCostPrice());
        productSku.setSSellPrice(productSku.getSellPrice());
        boolean save5 = productSkuService.save(productSku);
        if (!save5) {
            vo.setCode(50091);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品保存失败！");
        }


    }

    /**
     * 外部导入物资，店铺导入
     *
     * @param dto
     * @param vo
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void outerImportMaterialShop(CuterCreateMaterialDTO dto, ImportOuterProductVO vo, HttpServletRequest request) {
        String codeByPrefix = CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark());
        vo.setCode(200);
        vo.setMessage("操作成功！");
        vo.setProductCode(codeByPrefix);
        vo.setProductName(dto.getProductName());
        vo.setOuterProductCode(dto.getOuterProductCode());
        if (dto.getProductDescribe() != null && dto.getProductDescribe().length() > 100000) {
            vo.setCode(50102);
            throw new BusinessException("商品描述内容过大！");
        }
        if (mallConfig.isProductRepetitionCheck == 1) {
            Integer count = lambdaQuery().eq(Product::getProductName, dto.getProductName()).eq(Product::getSkuName, dto.getSkuName()).eq(Product::getShopId, dto.getShopId()).eq(Product::getProductType, 0).count();
            if (count > 0) {
                vo.setCode(50100);
                throw new BusinessException("商品名称重复");
            }
        }
        ProductCategory productCategory = productCategoryService.lambdaQuery().eq(ProductCategory::getClassPath, dto.getClassNamePath()).one();
        if (productCategory == null) {
            vo.setCode(50090);
            throw new BusinessException("分类不存在！");
        }
        MaterialDtlDTO redisMaterialInfo = getRedisMaterialInfo(productCategory.getClassId(), dto.getBasicsMaterialNo().trim(), dto.getBasicsMaterialName());

        // 保存商品信息
        Product product = new Product();
        BeanUtils.copyProperties(dto, product);
        product.setSerialNum(codeByPrefix);
        product.setClassId(productCategory.getClassId());

        product.setRelevanceId(redisMaterialInfo.getBillId());
        product.setRelevanceNo(redisMaterialInfo.getBillNo());
        product.setRelevanceName(redisMaterialInfo.getMaterialName());
        //第二自营点修改
        changShopIdBusiness(product);
        Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, product.getShopId())
                .select(Shop::getIsBusiness, Shop::getIsInternalShop,Shop::getEnterpriseId).one();
        //根据店铺id设置商品的税率
        setProductTaxRate(shop.getEnterpriseId(), product);
        if (shop.getIsBusiness() == 1) {
            product.setShopType(1);
        } else if (shop.getIsInternalShop() == 1) {
            product.setShopType(2);
        } else {
            product.setShopType(3);
        }
        product.setProductType(0);
        // 处理分类路径
        List<String> classPath = productCategoryService.getCategoryParentIdList(productCategory.getClassId());
        String cp = "";
        if (!CollectionUtils.isEmpty(classPath)) {
            for (String s : classPath) {
                cp += s + "/";
            }
        }
        if (CollectionUtils.isEmpty(classPath)) {
            vo.setCode(50090);
            throw new BusinessException("分类不存在");
        }
        String supplier_private_key = request.getHeader("supplier_private_key");
        String shopIdR = request.getHeader("shop_id");
        PrivateKeySupplier supplier = privateKeySupplierService.lambdaQuery().eq(PrivateKeySupplier::getPrivateKey, supplier_private_key).eq(PrivateKeySupplier::getShopId, shopIdR).select(PrivateKeySupplier::getSupplierId, PrivateKeySupplier::getSupplierName).one();
        if (supplier == null) {
            vo.setCode(50097);
            throw new BusinessException("未配置供应商id！");
        }


        product.setSupperBy(supplier.getSupplierId());
        product.setSupplierName(supplier.getSupplierName());
        product.setClassPath(cp.substring(0, cp.length() - 1));

        String classIdPath = (String) redisMaterialInfo.getClassPath();
        if (!product.getClassPath().equals(classIdPath)) {
            vo.setCode(50101);
            throw new BusinessException("商城分类和pcwp分类不统一！");
        }
        // 如果物资基础库被停用，则无法上架
//        int enable = (int) map.get("isEnable");
//        if (enable == 0) {
//            throw new BusinessException("物资基础库【 " + map.get("classNamePath") + " 】已经被停用，请修改商品物资基础库");
//        }
        product.setIsCompletion(ProductEnum.IS_COMPLETION_1.getCode());
//        product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
        Integer isPutaway = dto.getIsPutaway();
        if (isPutaway != null && isPutaway == 1) {
            product.setState(3);
        } else {
            product.setState(ProductEnum.STATE_STAY_PUTAWAY.getCode());
        }
        product.setProductMinImg(dto.getMinFile().get(0).getUrl());
        product.setMallType(0);
        product.setIsOpenImport(1);
        product.setProductMinPrice(dto.getSellPrice());


//        if (mallConfig.isApiImportProductBradDispose == 2) {
//            if (StringUtils.isNotBlank(dto.getBrandName())) {
//                Brand brand = brandService.lambdaQuery().eq(Brand::getName, dto.getBrandName().trim()).eq(Brand::getClassId, productCategory.getClassId()).one();
//                if (brand == null) {
//                    vo.setErrorCode(50098);
//                    throw new BusinessException("对应分类品牌不存在！");
//                } else {
//                    product.setBrandId(brand.getBrandId());
//                    product.setBrandName(dto.getBrandName().trim());
//                }
//            }
//        }


//        if (mallConfig.isApiImportProductBradDispose == 1) {
        if (StringUtils.isNotBlank(dto.getBrandName())) {
            // 处理品牌
            String brandName = dto.getBrandName();
            String newBrandName = brandName.trim();
            Brand brand = brandService.lambdaQuery().eq(Brand::getName, newBrandName).eq(Brand::getClassId, productCategory.getClassId()).one();
            if (brand == null) {
                Brand brand1 = new Brand();
                brand1.setName(newBrandName);
                brand1.setClassId(product.getClassId());
                String className = productCategoryService.getById(productCategory.getClassId()).getClassName();
                brand1.setClassName(className);
                brand1.setClassPath(product.getClassPath());
                brandService.save(brand1);
                product.setBrandId(brand1.getBrandId());
                product.setBrandName(newBrandName);
            } else {
                product.setBrandId(brand.getBrandId());
                product.setBrandName(newBrandName);
            }
        }
//        }

        // 处理计量单位
        String unitTrim = dto.getUnit().trim();
        SystemParam materialUnit = systemParamService.lambdaQuery().eq(SystemParam::getKeyValue, unitTrim).eq(SystemParam::getCode, "materialUnit").one();
        if (materialUnit == null) {
            SystemParam systemParam = new SystemParam();
            systemParam.setName("计量单位");
            systemParam.setCode("materialUnit");
            systemParam.setKeyValue(unitTrim);
            systemParam.setKeyValue2(unitTrim);
            systemParam.setMaintain(1);
            systemParam.setType(1);
            systemParamService.save(systemParam);
        }


        boolean save1 = save(product);
        if (!save1) {
            vo.setCode(50091);
            throw new BusinessException("商品保存失败！");
        }
        // 保存成功应该修改该分类为有商品
        productCategoryService.updateCategoryYesProduct(productCategory.getClassId());

        // 保存主图
        ImportProductVO importProductVO = dto.getAdminFile().get(0);
        File adminFile = new File();
        BeanUtils.copyProperties(importProductVO, adminFile);
        adminFile.setIsMain(1);
        adminFile.setRelevanceType(1);
        adminFile.setFileType(1);
        adminFile.setImgType(0);
        adminFile.setRelevanceId(product.getProductId());
        boolean save2 = fileService.save(adminFile);
        if (!save2) {
            vo.setCode(50092);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "主图保存失败！");
        }

        // 保存商品图片
        List<ImportProductVO> productFileVOs = dto.getProductFiles();
        ArrayList<File> productFile = new ArrayList<>();
        for (ImportProductVO productFileVO : productFileVOs) {
            File file = new File();
            BeanUtils.copyProperties(productFileVO, file);
            file.setIsMain(0);
            file.setRelevanceType(1);
            file.setFileType(1);
            file.setImgType(0);
            file.setRelevanceId(product.getProductId());
            productFile.add(file);
        }
        boolean save3 = fileService.saveBatch(productFile);
        if (!save3) {
            vo.setCode(50093);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品图片保存失败！");
        }

        // 保存小图
        ImportProductVO minFileVO = dto.getMinFile().get(0);
        File minFile = new File();
        BeanUtils.copyProperties(minFileVO, minFile);
        minFile.setIsMain(0);
        minFile.setRelevanceType(1);
        minFile.setFileType(1);
        minFile.setImgType(1);
        minFile.setRelevanceId(product.getProductId());
        boolean save4 = fileService.save(minFile);
        if (!save4) {
            vo.setCode(50094);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "小图保存失败！");
        }
        // 保存规格
        ProductSku productSku = new ProductSku();
        BeanUtils.copyProperties(dto, productSku);
        productSku.setProductId(product.getProductId());
        productSku.setSkuImg(minFile.getUrl());
        productSku.setState(PublicEnum.STATE_STOP.getCode());
        productSku.setUnit(unitTrim);
        productSku.setSCostPrice(productSku.getCostPrice());
        productSku.setSSellPrice(productSku.getSellPrice());
        boolean save5 = productSkuService.save(productSku);
        if (!save5) {
            vo.setCode(50091);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品保存失败！");
        }
    }

    private void changShopIdBusiness(Product product) {
        String shopId = product.getShopId();
        if (StringUtils.isNotEmpty(shopId)) {
            if (shopId.equals("1645601878095495170")) {
                product.setShopId(mallConfig.businessShopId);
            }
        }
    }

    /**
     * 导入商品
     *
     * @param file
     * @param productType
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportExcelResultVO> uploadExcelFile(MultipartFile file, Integer productType) {
        ProductService productService = SpringBeanUtil.getBean(ProductService.class);
        try {
                ArrayList<ImportExcelResultVO> vos = new ArrayList<>();
                List<Material> objects = EasyExcelUtils.readExcelOneSheet(file.getInputStream(), Material.class);
                if (objects.size() > 4000) {
                    throw new BusinessException(500, "数量超过4000禁止导入！");
                }
                for (Material material : objects) {
                    ImportExcelResultVO vo = new ImportExcelResultVO();
                    vo.setId(material.getId());
                    vo.setProductName(material.getProductName());
                    vo.setState(1);
                    try {
                        productService.saveImportMaterial(material, productType);
                    } catch (Exception e) {
                        vo.setState(0);
                        vo.setFail(e.getMessage());
                    }
                    vos.add(vo);
                }
                return vos;

        } catch (IOException e) {
            log.error("异常信息：" + e.getMessage());
        }
        return new ArrayList<>();
    }

    @Override
    public WMaterialnfoVO materialInfoLogin(String productId) {
        WMaterialnfoVO wMaterialnfoVO = materialInfo(productId);
        return wMaterialnfoVO;
    }

    /**
     * 检查商品是否上架
     *
     * @param dto
     * @return
     */
    @Override
    public List<CheckIsPutawayVO> checkIsPutaway(CheckIsPutawayPDTO dto) {
        String socialCreditCode = dto.getSocialCreditCode();
        List<CheckIsPutawayDTO> products = dto.getProducts();
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getSocialCreditCode, socialCreditCode).eq(EnterpriseInfo::getIsDeviceMall, 1).select(EnterpriseInfo::getEnterpriseId).one();
        if (enterpriseInfo == null) {
            throw new BusinessException(50010, "供应商未在装备运营平台完成注册！");
        }
        Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, enterpriseInfo.getEnterpriseId()).select(Shop::getShopId).one();
        if (shop == null) {
            throw new BusinessException(50011, "该供应商未在装备运营平台开店");
        }
        String shopId = shop.getShopId();

        Integer type = dto.getType();
        List<CheckIsPutawayVO> vos = new ArrayList<>();
        // 查询商品
        for (CheckIsPutawayDTO product : products) {
            Integer count = lambdaQuery().eq(Product::getRelevanceName, product.getProductName()).eq(Product::getSkuName, product.getSize()).eq(Product::getShopId, shopId).eq(Product::getState, 1).eq(type == 7, Product::getProductType, 1).eq(type == 8, Product::getProductType, 5).count();
            if (count > 0) {
                continue;
            } else {
                // 没有上架商品
                CheckIsPutawayVO vo = new CheckIsPutawayVO();
                vo.setProductName(product.getProductName());
                vo.setSize(product.getSize());
                vo.setErrCode(404);
                vos.add(vo);
            }
        }
        return vos;
    }

    /**
     * 获取物资（检查物资详情用）
     *
     * @param dto
     * @return
     */
    @Override
    public MaterInfoVO getCheckMaterialInfo(GetMaterialInfoDTO dto) {
        MaterInfoVO vo = new MaterInfoVO();
        String productId = dto.getProductId();
        // 查询商品信息
        Product product = getById(productId);
        if (product == null) return vo;
        BeanUtils.copyProperties(product, vo);
        // 查询分类路径
        List<String> classPath = productCategoryService.getCategoryParentIdList(product.getClassId());
        vo.setClassPath(classPath);
        // 获取品牌名称
        if (StringUtils.isNotEmpty(product.getBrandId())) {
            Brand brand = brandService.getById(product.getBrandId());
            if (brand != null) {
                vo.setBrandName(brand.getName());
            }
        }
        // 如果品牌的不等于空
//        if(StringUtils.isNotEmpty(product.getBrandId())) {
//            Brand byId = brandService.getById(product.getBrandId());
//            if(byId == null){
//                product.setBrandId(null);
//            }else {
//                // 品牌不为空
//                if(byId.getName().equals(product.getBrandName())){
//                }else {
//                    // 如果不相等，则使用用户输入的品牌
//                    product.setBrandId(null);
//                }
//            }
//        }
        // 获取主图
        List<File> files = fileService.listFileByParameters(product.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode(), FileEnum.IS_MIN_YES.getCode(), FileEnum.TYPE_IMG.getCode(), FileEnum.IMG_TYPE_GENERAL.getCode());
        vo.setAdminFile(files);
        // 获取小图
        List<File> files2 = fileService.listFileByParameters(product.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode(), FileEnum.IS_MIN_NO.getCode(), FileEnum.TYPE_IMG.getCode(), FileEnum.IMG_TYPE_MIN.getCode());
        vo.setMinFile(files2);
        // 获取商品图片
        List<File> files3 = fileService.listFileByParameters(product.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode(), FileEnum.IS_MIN_NO.getCode(), FileEnum.TYPE_IMG.getCode(), FileEnum.IMG_TYPE_GENERAL.getCode());
        vo.setProductFiles(files3);
        // 获取规格信息
        List<ProductSku> productSkus = productSkuService.getProductSkuByProductId(product.getProductId(), null);
        if (!CollectionUtils.isEmpty(productSkus)) {
            CopyDeviceSkuVO cvo = new CopyDeviceSkuVO();
            BeanUtils.copyProperties(productSkus.get(0), cvo);
            BeanUtils.copyProperties(cvo, vo);


        }
        vo.setCostPrice(null);
        vo.setOriginalPrice(null);
        List<RegionPrice> list = regionPriceService.lambdaQuery().eq(RegionPrice::getProductId, productId).list();
        if (list != null && list.size() > 0) {
            for(RegionPrice rp:list){
                try{
                    if(rp.getRegionName().equals("全区域")) {
                        rp.setSelectAddressOptionsAll(new ObjectMapper().readValue(rp.getAreaCode(), new TypeReference<List<String>>(){}));
                    }else{
                        rp.setSelectAddressOptions(new ObjectMapper().readValue(rp.getAreaCode(), new TypeReference<List<List<String>>>(){}));
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
        vo.setRegionPrice(list);
        return vo;
    }

    /**
     * 物资店铺查询供方商品
     *
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    @Override
    public PageUtils listMaterialSupplier(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        innerMap.put("enterpriseId", ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        int total = productMapper.listShopManageMaterialSupplierPageCount(innerMap);
        pageUtils.pageDispose(jsonObject, total);
        Page<MaterialSupplierListVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        pages.setRecords(productMapper.listShopManageMaterialSupplierPage(pages, innerMap));
        return new PageUtils(pages);

    }

    /**
     * 修改物资（供应商）
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMaterialSupplier(UpdateMaterialSupplierDTO dto) {
        if (dto.getProductDescribe() != null && dto.getProductDescribe().length() > 2500) {
            throw new BusinessException("商品描述内容过大！");
        }
        String shopId = dto.getShopId();
        if (mallConfig.isProductRepetitionCheck == 1) {
            Product product2 = lambdaQuery().eq(Product::getProductName, dto.getProductName()).eq(Product::getSkuName, dto.getSkuName()).eq(Product::getShopId, shopId).eq(Product::getProductType, dto.getProductType()).one();
            if (product2 != null) {
                // 如果不等于null判断是否是当前的
                if (product2.getProductId().equals(dto.getProductId())) {
                } else {
                    throw new BusinessException(400, "商品名称重复");
                }
            }
        }
        // 判断价格
        BigDecimal settlePrice = dto.getSettlePrice();
        BigDecimal sellPrice = dto.getSellPrice();
        //  结算价不能高于销售价
//        if (settlePrice.compareTo(sellPrice) == 1) {
//            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "结算价不能高于销售价！");
//        }
//        List<SystemParam> materialRatio = systemParamService.listByCode("materialRatio", 1);
//        if (!CollectionUtils.isEmpty(materialRatio)) {
//            SystemParam systemParam = materialRatio.get(0);
//            BigDecimal multiplyPercent = CommonUtil.getMultiplyPercent(systemParam.getKeyValue(), sellPrice);
//            dto.setSettlePrice(multiplyPercent);
//        }
        // 修改商品信息
        Product product = new Product();
        BeanUtils.copyProperties(dto, product);
        // 处理品牌
        // 处理品牌
        String newBrandName = dto.getBrandName();
        if (!StringUtils.isEmpty(newBrandName)) {
            Brand brand = brandService.lambdaQuery().eq(Brand::getName, newBrandName).eq(Brand::getClassId, product.getClassId()).one();
            if (brand == null) {
//            Brand brand1 = new Brand();
//            brand1.setName(newBrandName);
//            brandService.save(brand1);
//            product.setBrandId(brand1.getBrandId());
//            product.setBrandName(newBrandName);
                throw new BusinessException("品牌不存在");
            } else {
                product.setBrandId(brand.getBrandId());
                product.setBrandName(newBrandName);
            }
        }
//        product.setSupperBy(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
//        product.setSupplierName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
        // 处理分类路径
        List<String> classPath = dto.getClassPath();
        if (CollectionUtils.isEmpty(classPath)) {
            throw new BusinessException(400, "分类不能为空！");
        }
        String cp = "";
        if (!CollectionUtils.isEmpty(classPath)) {
            for (String s : classPath) {
                cp += s + "/";
            }
        }
        product.setClassPath(cp.substring(0, cp.length() - 1));
        product.setIsCompletion(ProductEnum.IS_COMPLETION_1.getCode());
        product.setProductMinPrice(dto.getSellPrice());
        product.setProductMinImg(dto.getMinFile().get(0).getUrl());

        // 是否可以修改对外的销售价成本价
        boolean isUpdatePrice = false;
        if (product.getState() != -1) {
            product.setSupplierSubmitState(2);
        } else {
            // 如果是等于-1表示商品还没有确认，可以修改
            isUpdatePrice = true;
        }
        // 只要修改了需要重新确认
        product.setState(-1);
        //设置物资缓存数据，用dto.getRelevanceNo()+

        if (product.getClassId() != null && dto.getRelevanceNo() != null) {
            MaterialDtlDTO redisMaterialInfo = getRedisMaterialInfo(product.getClassId(), dto.getRelevanceNo(), dto.getRelevanceName());
            if (redisMaterialInfo != null) {
                product.setClassPath(redisMaterialInfo.getClassPath());
                product.setClassId(redisMaterialInfo.getClassId());
                product.setRelevanceId(redisMaterialInfo.getBillId());
                product.setRelevanceNo(redisMaterialInfo.getBillNo());
                product.setRelevanceName(redisMaterialInfo.getMaterialName());
            } else {
                String materialNull = stringRedisTemplate.opsForValue().get(MaterialLockUtils.MATERIAL_NULL);
                if (materialNull.equals("-101")) {
                    throw new BusinessException("物资基础库不存在或者物资已经停用，请重新选择物资");
                } else {
                    throw new BusinessException("编号为" + dto.getRelevanceNo() + "的物资：" + dto.getRelevanceName() + "已停用，请修改联系管理员修改商品物资基础库");
                }
            }
        } else {
            MaterialDtlDTO redisMaterialInfo = selectMaterial(product.getClassId(), dto.getRelevanceNo());
            product.setClassPath(redisMaterialInfo.getClassPath());
            product.setClassId(redisMaterialInfo.getClassId());
            product.setRelevanceId(redisMaterialInfo.getBillId());
            product.setRelevanceNo(redisMaterialInfo.getBillNo());
            product.setRelevanceName(redisMaterialInfo.getMaterialName());
        }
        product.setProductName(product.getRelevanceName());
        boolean u1 = updateById(product);
        if (!u1) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "修改失败！");
        }
        List<BigDecimal> priceList = new ArrayList<>();
        //更新商品区域价格
        List<RegionPrice> regionPrice = dto.getRegionPrice();
        for(RegionPrice rp:regionPrice){
            if(rp.getRegionPriceId() == null){//有添加新的区域价格
                //更新标签 确定修改价格
                lambdaUpdate().eq(Product::getProductId, dto.getProductId()).set(Product::getPriceState,1).update();
            }else{
                RegionPrice one = regionPriceService.lambdaQuery().eq(RegionPrice::getRegionPriceId, rp.getRegionPriceId()).one();
                if(rp.getTaxInPrice().compareTo(one.getTaxInPrice()) != 0){
                    //更新标签
                    lambdaUpdate().eq(Product::getProductId, dto.getProductId()).set(Product::getPriceState,1).update();
                }
            }
            rp.setArea((rp.getDetailAddress() != null && rp.getDetailAddress().size() > 0) ? String.join(",",rp.getDetailAddress()) : null);
            try {
                rp.setAreaCode((rp.getDetailAddress() != null && rp.getDetailAddress().size() > 0) ? new ObjectMapper().writeValueAsString(
                        rp.getSelectAddressOptionsAll() != null ? rp.getSelectAddressOptionsAll() : rp.getSelectAddressOptions()
                ) : null);
            } catch (Exception e) {
                e.printStackTrace();
            }
            rp.setAnnualizedRate(product.getAnnualizedRate());
            rp.setMarkUpNum(product.getMarkUpNum());
            priceList.add(rp.getBonusTaxInPrice() != null ? rp.getBonusTaxInPrice():rp.getTaxInPrice());
        }
        regionPriceService.updateBatchById(regionPrice);
        BigDecimal minPrice = priceList.stream().min(BigDecimal::compareTo).orElse(null);
        lambdaUpdate().eq(Product::getProductId, product.getProductId()).set(Product::getProductMinPrice,minPrice).update();
        // 保存前删除所有图片
        fileService.deleteBatchFileByRelevanceIdAndType(product.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode());

        // 保存主图
        File adminFile = dto.getAdminFile().get(0);
        adminFile.setFileId(null);
        adminFile.setRelevanceId(product.getProductId());
        boolean save2 = fileService.save(adminFile);
        if (!save2) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "图片修改失败！");
        }

        // 保存商品图片
        List<File> productFiles = dto.getProductFiles();
        for (File productFile : productFiles) {
            productFile.setFileId(null);
            productFile.setRelevanceId(product.getProductId());
        }
        boolean save3 = fileService.saveBatch(productFiles);
        if (!save3) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "图片修改失败！");
        }

        // 保存小图
        File minFile = dto.getMinFile().get(0);
        minFile.setFileId(null);
        minFile.setRelevanceId(product.getProductId());
        boolean save4 = fileService.save(minFile);
        if (!save4) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "图片修改失败！");
        }
        // 保存规格
        ProductSku productSku = new ProductSku();
        BeanUtils.copyProperties(dto, productSku);
        productSku.setSCostPrice(dto.getCostPrice());
        productSku.setSSellPrice(dto.getSellPrice());

        // 不可以修改取反
//        if(!isUpdatePrice) {
//            productSku.setCostPrice(null);
//            productSku.setSellPrice(null);
//        }
        // 规格主图默认是商品的主图
        productSku.setSkuImg(minFile.getUrl());
        boolean u5 = productSkuService.updateById(productSku);
        if (!u5) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "修改失败！！");
        }
    }

    public MaterialDtlDTO selectMaterial(String classId, String relevanceNo) {
        String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + BASICS_MATERIAL_LIST;
        HashMap<Object, Object> paramsMap = new HashMap<>();
        paramsMap.put("pageIndex", 0);
        paramsMap.put("pageSize", 10);
        paramsMap.put("classId", classId);
        paramsMap.put("materialNo", relevanceNo);
        PageUtils<Map> r = null;
        try {
            r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
        } catch (Exception e) {
            throw new BusinessException("【远程异常】获取基础库物资错误：" + e.getMessage());
        }
        List<Map> list = r.getList();
        if (list == null) {
            //如果物资基础库不存在，设置缓存编号为key,缓存1分钟时间，避免重复请求
            stringRedisTemplate.opsForValue().set(MaterialLockUtils.MATERIAL_NULL + relevanceNo, "-101", 2, MINUTES);
            throw new BusinessException("物资基础库不存在或者物资已经停用，请重新选择物资");
        }
        for (Map map : list) {
            int enable = (int) map.get("isEnable");
            String billNo = ((String) map.get("billNo"));
            String MaterialClassId = ((String) map.get("classId"));
            String materialName = ((String) map.get("materialName"));
            if (enable == 0) {
                stringRedisTemplate.opsForValue().set(MaterialLockUtils.MATERIAL_NULL + billNo, "-100", 1, MINUTES);
                throw new BusinessException("物资编号：" + billNo + ";" + "物资名称" + materialName + "已停用】，请修改联系管理员修改商品物资基础库或重新选择物资");

            } else {
                stringRedisTemplate.opsForValue().set(MaterialLockUtils.CLASS_ID_AND_RELEVANCE_N0_LOCK_KEY +
                        MaterialClassId + ":" + billNo, JSONObject.toJSONString(map), 30, MINUTES);
                Integer classQueryCount = productCategoryService.lambdaQuery().eq(ProductCategory::getClassPath, map.get("classNamePath")).count();
                if (classQueryCount == 0) {
                    throw new BusinessException(500085, "和pcwp分类不统一！");
                } else {
                    return MaterialDtlDTO.fromMap(map);
                }
            }
        }
        // 如果物资基础库被停用，则无法上架
        return null;

    }

    /**
     * 批量修改供方提交状态
     *
     * @param dto
     */
    @Override
    public void updateProductSupplierSubState(UpdateProductSupplierSubStateDTO dto) {
        List<String> productIds = dto.getProductIds();
        int state = dto.getState();
        //同时修改sku的启用/停用状态
        if (state == 2 || state == 1) {
            List<String> ids = new ArrayList<>();
            List<String> productNames = new ArrayList<>();
            for (String productId : dto.getProductIds()) {
                Product product = lambdaQuery().eq(Product::getProductId, productId).select(Product::getIsCompletion, Product::getProductName).one();
                if (product == null || product.getIsCompletion() != ProductEnum.IS_COMPLETION_1.getCode()) {
                    productNames.add(product.getProductName());
                    continue;
                } else {
                    ids.add(productId);
                }
            }
            if (ids.size() == 0 || productNames.size() != 0) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "【" + productNames + "】商品未编辑完成，请检查后重试！");
            }
            lambdaUpdate().in(Product::getProductId, productIds).set(Product::getSupplierSubmitState, state).set(Product::getSupplierSubmitError, null).set(Product::getGmtModified, new Date()).update();
        }
        //代确认
        if (state == 3) {
            List<Product> productList = baseMapper.selectBatchIds(productIds);
            for (Product product : productList) {
                //如果税率==0或者税率是null,这默认为商品税率为 enterpriseInfoTaxRate
                BigDecimal enterpriseInfoTaxRate = enterpriseInfoService.getEnterpriseInfoTaxRate();
                if (product.getTaxRate() == null || product.getTaxRate().compareTo(BigDecimal.ZERO) == 0) {
//                    lambdaUpdate().in(Product::getProductId, productIds)
//                    .set(Product::getSupplierSubmitState, state).set(Product::getState, 0).set(Product::getSupplierSubmitError, null)
//                    .set(Product::getGmtModified, new Date()).update();
                    product.setTaxRate(enterpriseInfoTaxRate);
                }
                product.setSupplierSubmitState(state);
                product.setState(0);
                product.setSupplierSubmitError(null);
                product.setGmtModified(new Date());

            }
            updateBatchById(productList);
        }
        // 审核失败
        if (state == 4) {
            lambdaUpdate().in(Product::getProductId, productIds).set(Product::getSupplierSubmitState, state).set(Product::getSupplierSubmitError, dto.getFailReason()).set(Product::getGmtModified, new Date()).update();
        }
    }

    /**
     * 新增物资（供应商）
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void createMaterialSupplier(CreateMaterialSupplierDTO dto) {
        if (dto.getProductDescribe() != null && dto.getProductDescribe().length() > 2500) {
            throw new BusinessException("商品描述内容过大！");
        }
        String shopId = dto.getShopId();
        if (StringUtils.isBlank(shopId)) {
            throw new BusinessException(400, "未选择店铺！");
        }
        if (mallConfig.isProductRepetitionCheck == 1) {
            Product product2 = lambdaQuery().eq(Product::getProductName, dto.getProductName()).eq(Product::getSkuName, dto.getSkuName()).eq(Product::getShopId, shopId).eq(Product::getProductType, dto.getProductType()).one();
            if (product2 != null) {
                throw new BusinessException(400, "商品名称重复");
            }
        }
        // 判断价格
        BigDecimal settlePrice = dto.getSettlePrice();
        BigDecimal sellPrice = dto.getSellPrice();
        //  结算价不能高于销售价
//        if (settlePrice.compareTo(sellPrice) == 1) {
//            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "结算价不能高于销售价！");
//        }
        // 计算结算价
//        List<SystemParam> materialRatio = systemParamService.listByCode("materialRatio", 1);
//        if (!CollectionUtils.isEmpty(materialRatio)) {
//            SystemParam systemParam = materialRatio.get(0);
//            BigDecimal multiplyPercent = CommonUtil.getMultiplyPercent(systemParam.getKeyValue(), sellPrice);
//            dto.setSettlePrice(multiplyPercent);
//        }
        // 保存商品信息
        Product product = new Product();
        BeanUtils.copyProperties(dto, product);
        Shop shop = shopService.getById(shopId);
        if (shop.getIsBusiness() == 1) {
            product.setShopType(1);
        } else if (shop.getIsInternalShop() == 1) {
            product.setShopType(2);
        } else {
            product.setShopType(3);
        }

        // 处理品牌
        String newBrandName = dto.getBrandName();
        if (!StringUtils.isEmpty(newBrandName)) {
            Brand brand = brandService.lambdaQuery().eq(Brand::getName, newBrandName).eq(Brand::getClassId, product.getClassId()).one();
            if (brand == null) {
//            Brand brand1 = new Brand();
//            brand1.setName(newBrandName);
//            brandService.save(brand1);
//            product.setBrandId(brand1.getBrandId());
//            product.setBrandName(newBrandName);
                throw new BusinessException("品牌不存在");
            } else {
                product.setBrandId(brand.getBrandId());
                product.setBrandName(newBrandName);
            }
        }


        product.setSupperBy(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        product.setSupplierName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
        // 处理分类路径
        List<String> classPath = dto.getClassPath();
        if (CollectionUtils.isEmpty(classPath)) {
            throw new BusinessException(400, "分类不能为空！");
        }
        String cp = "";
        if (!CollectionUtils.isEmpty(classPath)) {
            for (String s : classPath) {
                cp += s + "/";
            }
        }
        product.setClassPath(cp.substring(0, cp.length() - 1));
        product.setIsCompletion(ProductEnum.IS_COMPLETION_1.getCode());
        product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
        product.setShopId(shopId);
        product.setProductMinPrice(dto.getSellPrice());
        // 设置为-1提交，确认了才可
        product.setState(-1);
        //TODO 运费设置
        product.setProductMinImg(dto.getMinFile().get(0).getUrl());
        product.setShopId(dto.getShopId());
        product.setSupplierSubmitState(1);


        // 自动设置分类
        String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + BASICS_MATERIAL_LIST;
        HashMap<Object, Object> paramsMap = new HashMap<>();
        paramsMap.put("versionId","1942518269757186050");
        paramsMap.put("pageIndex", 0);
        paramsMap.put("pageSize", 10);
        paramsMap.put("materialNo", dto.getRelevanceNo());
        PageUtils<Map> r = null;
        try {
            r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
        } catch (Exception e) {
            throw new BusinessException("【远程异常】获取基础库物资错误：" + e.getMessage());
        }
        List<Map> list = r.getList();
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("物资基础库该下不存在该物资！");
        }
        List<Map> collect1 = list.stream().filter(item -> Integer.valueOf(item.get("isEnable").toString()) == 1).collect(Collectors.toList());
        Map map = collect1.get(0);
        log.info("pcwp查询物资基础库分类返回：" + map);
        Integer classQueryCount = productCategoryService.lambdaQuery().eq(ProductCategory::getClassPath, map.get("classNamePath")).count();
        if (classQueryCount == 0) {
            throw new BusinessException(500085, "和pcwp分类不统一！");
        }
        product.setClassPath((String) map.get("classIdPath"));
        product.setClassId((String) map.get("classId"));

        // 限制物资基础库状态
        // 如果物资基础库被停用，则无法上架
        int enable = (int) map.get("isEnable");
        if (enable == 0) {
            throw new BusinessException("物资基础库【 " + map.get("classNamePath") + " 】已经被停用，请修改商品物资基础库");
        }
        Integer isOpenImport = dto.getIsOpenImport();
        if (isOpenImport != null) {
            product.setIsOpenImport(isOpenImport);
        }
        product.setProductName(product.getRelevanceName());
        boolean save1 = save(product);
        if (!save1) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "保存失败！");
        }
        List<BigDecimal> priceList = new ArrayList<>();
        //保存商品区域价格
        List<RegionPrice> regionPrice = dto.getRegionPrice();
        for(RegionPrice rp:regionPrice){
            rp.setProductId(product.getProductId());
            rp.setArea((rp.getDetailAddress() != null && rp.getDetailAddress().size() > 0) ? String.join(",",rp.getDetailAddress()) : null);
            try {
                rp.setAreaCode((rp.getDetailAddress() != null && rp.getDetailAddress().size() > 0) ? new ObjectMapper().writeValueAsString(
                        rp.getSelectAddressOptionsAll() != null ? rp.getSelectAddressOptionsAll() : rp.getSelectAddressOptions()
                ) : null);
            } catch (Exception e) {
                e.printStackTrace();
            }
            rp.setAnnualizedRate(product.getAnnualizedRate());
            priceList.add(rp.getBonusTaxInPrice() != null ? rp.getBonusTaxInPrice():rp.getTaxInPrice());
        }
        regionPriceService.saveBatch(regionPrice);
        BigDecimal minPrice = priceList.stream().min(BigDecimal::compareTo).orElse(null);
        lambdaUpdate().eq(Product::getProductId, product.getProductId()).set(Product::getProductMinPrice,minPrice).update();
        // 保存成功应该修改该分类为有商品
        productCategoryService.updateCategoryYesProduct(dto.getClassId());

        // 保存主图
        File adminFile = dto.getAdminFile().get(0);
        adminFile.setRelevanceId(product.getProductId());
        boolean save2 = fileService.save(adminFile);
        if (!save2) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "主图保存失败！");
        }

        // 保存商品图片
        List<File> productFiles = dto.getProductFiles();
        for (File productFile : productFiles) {
            productFile.setRelevanceId(product.getProductId());
        }
        boolean save3 = fileService.saveBatch(productFiles);
        if (!save3) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品图片保存失败！");
        }

        // 保存小图
        File minFile = dto.getMinFile().get(0);
        minFile.setRelevanceId(product.getProductId());
        boolean save4 = fileService.save(minFile);
        if (!save4) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "小图保存失败！");
        }
        // 保存规格
        ProductSku productSku = new ProductSku();
        BeanUtils.copyProperties(dto, productSku);
        // 处理副单位
        if (dto.getSecondUnitNum() == null) {
            productSku.setSecondUnitNum(BigDecimal.valueOf(0));
        }
        // 规格型号为空
        productSku.setProductId(product.getProductId());
        productSku.setSCostPrice(productSku.getCostPrice());
        productSku.setSSellPrice(productSku.getSellPrice());
        // 规格主图默认是商品的主图
        productSku.setSkuImg(minFile.getUrl());
        productSku.setState(PublicEnum.STATE_STOP.getCode());
        boolean save5 = productSkuService.save(productSku);
        if (!save5) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品保存失败！");
        }
    }

    /**
     * 获取物资（供应商查询专用）
     *
     * @param dto
     * @return
     */
    @Override
    public MaterInfoVO getMaterialInfoSupplier(GetMaterialInfoDTO dto) {
        MaterInfoVO vo = new MaterInfoVO();
        String productId = dto.getProductId();
        // 查询商品信息
        Product product = getById(productId);
        if (product == null) return vo;
        BeanUtils.copyProperties(product, vo);
        String shopNameById = shopService.getShopNameById(product.getShopId());
        vo.setShopName(shopNameById);
        // 查询分类路径
        List<String> classPath = productCategoryService.getCategoryParentIdList(product.getClassId());
        vo.setClassPath(classPath);
        // 获取品牌名称
        if (StringUtils.isNotEmpty(product.getBrandId())) {
            Brand brand = brandService.getById(product.getBrandId());
            if (brand != null) {
                vo.setBrandName(brand.getName());
            }
        }
        // 如果品牌的不等于空
//        if(StringUtils.isNotEmpty(product.getBrandId())) {
//            Brand byId = brandService.getById(product.getBrandId());
//            if(byId == null){
//                product.setBrandId(null);
//            }else {
//                // 品牌不为空
//                if(byId.getName().equals(product.getBrandName())){
//                }else {
//                    // 如果不相等，则使用用户输入的品牌
//                    product.setBrandId(null);
//                }
//            }
//        }
        // 获取主图
        List<File> files = fileService.listFileByParameters(product.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode(), FileEnum.IS_MIN_YES.getCode(), FileEnum.TYPE_IMG.getCode(), FileEnum.IMG_TYPE_GENERAL.getCode());
        vo.setAdminFile(files);
        // 获取小图
        List<File> files2 = fileService.listFileByParameters(product.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode(), FileEnum.IS_MIN_NO.getCode(), FileEnum.TYPE_IMG.getCode(), FileEnum.IMG_TYPE_MIN.getCode());
        vo.setMinFile(files2);
        // 获取商品图片
        List<File> files3 = fileService.listFileByParameters(product.getProductId(), FileEnum.RELEVANCE_TYPE_PRODUCT.getCode(), FileEnum.IS_MIN_NO.getCode(), FileEnum.TYPE_IMG.getCode(), FileEnum.IMG_TYPE_GENERAL.getCode());
        vo.setProductFiles(files3);
        // 获取规格信息
        ProductSku productSku = productSkuService.lambdaQuery().eq(ProductSku::getProductId, product.getProductId()).one();
        if (productSku != null) {
            CopyDeviceSkuVO cvo = new CopyDeviceSkuVO();
            BeanUtils.copyProperties(productSku, cvo);
            BeanUtils.copyProperties(cvo, vo);
//            // 供应商价格不一样
//            vo.setSellPrice(productSku.getSSellPrice());
//            vo.setCostPrice(productSku.getSCostPrice());
        }
        //查询商品区域价格
        List<RegionPrice> list = regionPriceService.lambdaQuery().eq(RegionPrice::getProductId, dto.getProductId()).list();
        if (list != null && list.size() > 0) {
            for(RegionPrice rp:list){
                try{
                    if(rp.getRegionName().equals("全区域")) {
                        rp.setSelectAddressOptionsAll(new ObjectMapper().readValue(rp.getAreaCode(), new TypeReference<List<String>>(){}));
                    }else{
                        rp.setSelectAddressOptions(new ObjectMapper().readValue(rp.getAreaCode(), new TypeReference<List<List<String>>>(){}));
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
        vo.setRegionPrice(list);


        return vo;
    }

    /**
     * 查询供方提供的商品（店铺）
     *
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    @Override
    public PageUtils listMaterialSupplierAffirm(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        innerMap.put("shopId", ThreadLocalUtil.getCurrentUser().getShopId());
        int total = productMapper.listSupplierMaterialSupplierPageCount(innerMap);
        pageUtils.pageDispose(jsonObject, total);
        Page<MaterialSupplierListVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        pages.setRecords(productMapper.listSupplierMaterialSupplierPage(pages, innerMap));
        return new PageUtils(pages);
    }


    @Override
    public PageUtils productFromList(JSONObject jsonObject, QueryWrapper<ProductFromVo> wrapper) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String shopId = user.getShopId();
        String businessShopId = (String) jsonObject.getInnerMap().get("shopId");
        if (StringUtils.isNotEmpty(businessShopId)) {
            wrapper.eq("p.shop_id", businessShopId);
        } else {
            wrapper.eq("p.shop_id", shopId);
        }
        IPage<ProductFromVo> pages = new Query<ProductFromVo>().getPage(jsonObject);
        List<ProductFromVo> list = baseMapper.productFromList(pages, wrapper);
        if (list == null || list.size() == 0) {
            list = new ArrayList<>();
        }
        PageUtils<ProductFromVo> pageUtils = new PageUtils(pages);
        pageUtils.setList(list);
        return pageUtils;

        /**
         * 确认全部商品（店铺）
         * @param jsonObject
         * @param lambdaQuery
         */

    }

    private void getWrapper(JSONObject jsonObject, QueryWrapper<ProductFromVo> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String productName = (String) innerMap.get("productName");
        String serialNum = (String) innerMap.get("serialNum");
        String staSellPrice = (String) innerMap.get("staSellPrice");
        String endSellPrice = (String) innerMap.get("endSellPrice");
        String staPutawayDate = (String) innerMap.get("staPutawayDate");
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endPutawayDate = (String) innerMap.get("endPutawayDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        String keywords = (String) innerMap.get("keywords");
        List<String> ids = (ArrayList<String>) jsonObject.get("ids");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        if (keywords != null && keywords != "") {
            wrapper.and(qw -> qw.like("p.serial_num", keywords).or().like("p.product_name", keywords));

        }
        if (productName != null && productName != "") {
            wrapper.like("p.product_name", productName);
        }
        if (ids != null && ids.size() > 0) {
            wrapper.in("p.product_id", ids);
        }
        if (serialNum != null && serialNum != "") {
            wrapper.like("p.serial_num", serialNum);
        }
        if (staPutawayDate != null && staPutawayDate != "") {
            wrapper.gt("p.putaway_date", staPutawayDate);
        }
        if (endPutawayDate != null && endPutawayDate != "") {
            wrapper.lt("p.putaway_date", endPutawayDate);
        }
        if (endCreateDate != null && endCreateDate != "") {
            wrapper.eq("p.gmt_create", endCreateDate);
        }
        if (startCreateDate != null && startCreateDate != "") {
            wrapper.gt("p.gmt_create", startCreateDate);
        }
        if (endSellPrice != null && endSellPrice != "") {
            wrapper.lt("ps.sell_price", endSellPrice);
        }
        if (staSellPrice != null && staSellPrice != "") {
            wrapper.gt("ps.sell_price", staSellPrice);
        }
        if (orderBy != null) {
            if (orderBy == 1) {
                wrapper.orderByDesc("p.gmt_create");
            }
            if (orderBy == 0) {
                wrapper.orderByDesc("p.putaway_date");
            }
        } else {
            wrapper.orderByDesc("p.putaway_date");
        }
        wrapper.isNotNull("p.gmt_create");
    }

    @Override
    public void batchAffirmSupplierProduct(JSONObject jsonObject, LambdaQueryWrapper<Product> lambdaQuery) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();

        // 只查询当前店铺
        innerMap.put("shopId", ThreadLocalUtil.getCurrentUser().getShopId());

        // 获取到id
        List<String> productQueryIds = productMapper.batchAffirmSupplierProductQueryIds(innerMap);

        Integer state = (Integer) innerMap.get("updateState");
        String updateFailReason = (String) innerMap.get("updateFailReason");
        if (state != null) {
            if (state == 3) {
                lambdaUpdate().in(Product::getProductId, productQueryIds).set(Product::getSupplierSubmitState, state).set(Product::getState, 0).set(Product::getSupplierSubmitError, null).set(Product::getGmtModified, new Date()).update();
            }
            // 审核失败
            if (state == 4) {
                lambdaUpdate().in(Product::getProductId, productQueryIds).set(Product::getSupplierSubmitState, state).set(Product::getSupplierSubmitError, updateFailReason).set(Product::getGmtModified, new Date()).update();
            }
        }
    }


    @Override
    public void productFromListLoad(JSONObject jsonObject, QueryWrapper<ProductFromVo> wrapper, HttpServletResponse response) {
        getWrapper(jsonObject, wrapper);
        String shopId = ThreadLocalUtil.getCurrentUser().getShopId();
        //接口调整，物资子公司可以查看到物资分公司的商品
        String businessShopId = (String) jsonObject.getInnerMap().get("shopId");
        if (StringUtils.isNotEmpty(businessShopId)) {
            wrapper.eq("p.shop_id", businessShopId);
        } else {
            wrapper.eq("p.shop_id", shopId);
        }
        wrapper.eq("p.shop_id", shopId);
        wrapper.isNotNull("p.putaway_date");
        // 只导出4000条数据
        wrapper.last("LIMIT " + 10000);
        List<ProductFromVo> list = baseMapper.productFromListLoad(wrapper);

        try {
            String src = mallConfig.templateFormUrl;
//             String src = src;
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("dataList", list);
            try {
                if (shopId.equals("1645601878095495170")) {
                    // 导出模板  物资公司供应商商品报表模版，多出一个二级供应商
                    ExcelForWebUtil.exportExcel(response, dataMap, "物资公司供应商商品报表模板.xlsx", src, "商品报表.xlsx");
                } else {
                    ExcelForWebUtil.exportExcel(response, dataMap, "商品报表模板.xlsx", src, "商品报表.xlsx");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }

    }


    @Override
    public PageUtils getPlatformProductFromList(JSONObject jsonObject, QueryWrapper<PlatformProductFromVo> wrapper) {

        getplatformProductFromWapper(jsonObject, wrapper);
        IPage<PlatformProductFromVo> pages = new Query<PlatformProductFromVo>().getPage(jsonObject);
        List<PlatformProductFromVo> list = baseMapper.platformProductFromList(pages, wrapper);
        if (list == null || list.size() == 0) {
            list = new ArrayList<>();
        }
        PageUtils<PlatformProductFromVo> pageUtils = new PageUtils(pages);
        pageUtils.setList(list);
        return pageUtils;
    }

    private void getplatformProductFromWapper(JSONObject jsonObject, QueryWrapper<PlatformProductFromVo> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String productName = (String) innerMap.get("productName");
        String serialNum = (String) innerMap.get("serialNum");
        String staSellPrice = (String) innerMap.get("staSellPrice");
        String endSellPrice = (String) innerMap.get("endSellPrice");
        String shopName = (String) innerMap.get("shopName");
        String enterPriseName = (String) innerMap.get("enterPriseName");
        String staPutawayDate = (String) innerMap.get("staPutawayDate");
        String endPutawayDate = (String) innerMap.get("endPutawayDate");
        String keywords = (String) innerMap.get("keywords");
        Integer productType = (Integer) innerMap.get("productType");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        List<String> ids = (ArrayList<String>) jsonObject.get("ids");
        if (ids != null && ids.size() > 0) {
            wrapper.in("p.product_id", ids);
        }
        if (keywords != null && keywords != "") {

            wrapper.and(qw -> qw.like("p.serial_num", keywords).or().like("p.product_name", keywords).or().like("sh.shop_name", keywords).or().like("en.enterprise_name", keywords));
        }
        if (productType != null) {
            wrapper.eq("p.product_type",productType);
        }
        if (productName != null && productName != "") {
            wrapper.like("p.product_name", productName);
        }
        if (serialNum != null && serialNum != "") {
            wrapper.like("p.serial_num", serialNum);
        }
        if (staPutawayDate != null && staPutawayDate != "") {
            wrapper.gt("p.putaway_date", staPutawayDate);
        }
        if (endPutawayDate != null && endPutawayDate != "") {
            wrapper.lt("p.putaway_date", endPutawayDate);
        }
        if (shopName != null && shopName != "") {
            wrapper.like("sh.shop_name", shopName);
        }
        if (enterPriseName != null && enterPriseName != "") {
            wrapper.like("en.enterprise_name", enterPriseName);
        }
        if (endSellPrice != null && endSellPrice != "") {
            wrapper.lt("ps.sell_price", endSellPrice);
        }
        if (staSellPrice != null && staSellPrice != "") {
            wrapper.gt("ps.sell_price", staSellPrice);
        }
        wrapper.isNotNull("p.putaway_date");
        wrapper.orderByAsc("p.putaway_date");
    }


    @Override
    public void getPlatformProductFromListLoad(JSONObject jsonObject, QueryWrapper<PlatformProductFromVo> wrapper, HttpServletResponse response) {
        getplatformProductFromWapper(jsonObject, wrapper);
        wrapper.last("LIMIT " + 4000);
        List<PlatformProductFromVo> list = baseMapper.platformProductFromListLoad(wrapper);

        try {
            String src = mallConfig.templateFormUrl;
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("dataList", list);
            ExcelForWebUtil.exportExcel(response, dataMap, "平台商品统计模板.xlsx", src, "平台商品统计.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 供方商品Excel导入
     *
     * @param file
     * @param shopId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportExcelResultVO> supplierUploadExcelFile(MultipartFile file, String shopId) {
        ProductService productService = SpringBeanUtil.getBean(ProductService.class);
        try {
            // 物资
            ArrayList<ImportExcelResultVO> vos = new ArrayList<>();
            List<ShopMaterial> objects = EasyExcelUtils.readExcelOneSheet(file.getInputStream(), ShopMaterial.class);
            if (objects.size() > 4000) {
                throw new BusinessException(500, "数量超过4000禁止导入！");
            }
            for (ShopMaterial material : objects) {
                ImportExcelResultVO vo = new ImportExcelResultVO();
                vo.setId(material.getId());
                vo.setProductName(material.getProductName());
                vo.setState(1);


                try {
                    if (material.getProductDescribe() != null && material.getProductDescribe().length() > 2500) {
                        throw new BusinessException(500, "商品描述不能超过2000个字符");
                    }
                    productService.saveImportSupplierMaterial(material, shopId);

                } catch (Exception e) {
                    vo.setState(0);
                    vo.setFail(e.getMessage());
                }
                vos.add(vo);
            }
            return vos;
        } catch (IOException e) {
            log.error("异常信息：" + e.getMessage());
        }
        return new ArrayList<>();
    }


    /**
     * 根据种类id查询商品信息
     *
     * @param classId
     * @return
     */
    @Override
    public List<Product> findAllByClassId(String classId) {
        LambdaQueryWrapper<Product> q = new LambdaQueryWrapper<>();
        q.eq(Product::getClassId, classId);
        List<Product> list = list(q);
        return list;
    }


    @Override
    public void updateClassId(Object nowClassId, Object oldclassId) {

    }

    @Override
    public List<Product> getDataByClassIdList(String classId) {
        LambdaQueryWrapper<Product> q = new LambdaQueryWrapper<>();
        q.eq(Product::getClassId, classId);
        List<Product> list = list(q);
        return list;

    }

    /**
     * 查询上级f分类路径     * @param nowparentId
     */
    @Override
    public String getDataByClassIdParentPath(String nowparentId) {
        return baseMapper.getDataByClassIdParentPath(nowparentId);
    }

    /**
     * 根据当前查询条件全部通过商品
     *
     * @param jsonObject
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allProductStatePass(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        List<String> productIds = productMapper.listPlatformMaterialProductIds(innerMap);
        if (!CollectionUtils.isEmpty(productIds)) {
            // 如果是上架判断是否有权限
            Integer isPlatformAdmin = ThreadLocalUtil.getCurrentUser().getIsPlatformAdmin();
            if (isPlatformAdmin == null || isPlatformAdmin != 1) {
                throw new BusinessException(500, "没有平台管理员权限！");
            }
            lambdaUpdate().in(Product::getProductId, productIds).eq(Product::getIsCompletion, 1).set(Product::getState, 1).set(Product::getFailReason, null).set(Product::getPutawayDate, new Date()).set(Product::getGmtModified, new Date()).update();
//            productSkuService.lambdaUpdate()
//                    .in(ProductSku::getProductId, productIds)
//                    .set(ProductSku::getState, PublicEnum.STATE_OPEN.getCode())
//                    .set(ProductSku::getGmtModified, new Date())
//                    .update();
        }
    }

    /**
     * 商品导入（店铺导入）
     *
     * @param dtos
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportOuterProductVO> importBatchMaterialShop(List<CuterCreateMaterialDTO> dtos, HttpServletRequest request) {
        ProductService productService = SpringBeanUtil.getBean(ProductService.class);
        ArrayList<ImportOuterProductVO> vos = new ArrayList<>();
        for (CuterCreateMaterialDTO dto : dtos) {
            ImportOuterProductVO vo = new ImportOuterProductVO();
            try {
                productService.outerImportMaterialShop(dto, vo, request);
            } catch (Exception e) {
                if (vo.getCode() == 50101) {
                    // 触发保存
                    ErrorInfo errorInfo = new ErrorInfo();
                    errorInfo.setBusinessType(1);
                    errorInfo.setErrorRqJson(JSON.toJSONString(dto));
                    errorInfo.setCreateTime(new Date());
                    errorInfo.setIsDispose(0);
                    errorInfo.setMethodName("importBatchMaterialShop");
                    errorInfo.setErrorInfo(e.getMessage());
                    errorInfoService.save(errorInfo);
                }
                vo.setProductName(dto.getProductName());
                vo.setMessage(e.getMessage());
                if (vo.getCode() != null && vo.getCode() == 200) {
                    vo.setCode(500);
                }
            }
            vos.add(vo);

        }
        return vos;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public List<ImportExcelResultVO> shopUploadProductMallExcelFile(MultipartFile file) {
        try {
            ArrayList<ImportExcelResultVO> vos = new ArrayList<>();
            List<Material> objects = EasyExcelUtils.readExcelOneSheet(file.getInputStream(), Material.class);
            if (objects.size() > 4000) {
                throw new BusinessException(500, "数量超过4000禁止导入！");
            }
            //查询出来的物资基础库信息，如果物资存在，不查询，不存在，查询物资
            List<Map<String, Object>> selectMaterialList = new ArrayList<>();
            ArrayList<String> materialIds = new ArrayList<>();
            for (Material material : objects) {
                ImportExcelResultVO vo = new ImportExcelResultVO();
                vo.setId(material.getId());
                vo.setProductName(material.getProductName());
                vo.setState(1);
                try {
                    if ((material.getProductDescribe() != null && material.getProductDescribe().length() > 2500)) {
                        throw new BusinessException(500, "商品描述不能超过2000个字符");
                    }
                    UserLogin user = ThreadLocalUtil.getCurrentUser();
                    String shopId = user.getShopId();
                    if (mallConfig.isProductRepetitionCheck == 1) {
                        int count = lambdaQuery().eq(Product::getProductName, material.getProductName()).eq(Product::getSkuName, material.getSkuName()).eq(Product::getShopId, shopId).count();
                        if (count != 0) {
                            throw new BusinessException(500, "商品名称重复");
                        }
                    }  // 保存商品信息
                    Product product = new Product();
                    BeanUtils.copyProperties(material, product);
                    product.setShopName(user.getShopName());
                    product.setShopId(user.getShopId());
                    //确定商品按照店铺排序
                    Shop shop = shopService.getById(shopId);
                    setProductTaxRate(shop.getEnterpriseId(), product);
                    if (shop.getIsBusiness() == 1) {
                        product.setShopType(1);
                    } else if (shop.getIsInternalShop() == 1) {
                        product.setShopType(2);
                    } else {
                        product.setShopType(3);
                    }

                    // 处理分类路径
                    if (StringUtils.isEmpty(material.getClassNamePath())) {
                        throw new BusinessException(400, "分类不能为空！");
                    }
                    String[] split = material.getClassNamePath().trim().split("/");
                    ProductCategory productCategory = productCategoryService.getDataByClassPathName(material.getClassNamePath().trim(), split.length, 1);
                    if (productCategory == null) {
                        throw new BusinessException(400, "分类不存在！");
                    }
                    // 处理分类路径
                    List<String> classPath = productCategoryService.getCategoryParentIdList(productCategory.getClassId());
                    String cp = "";
                    if (!CollectionUtils.isEmpty(classPath)) {
                        for (String s : classPath) {
                            cp += s + "/";
                        }
                    }


                    //保存物料信息
                    String materialName = material.getMaterialName();
                    String materialNo = material.getMaterialNo();
                    if (StringUtils.isNotBlank(materialName)&&StringUtils.isNotBlank(materialNo)) {
                        String s = stringRedisTemplate.opsForValue().get(productCategory.getClassId() + ":" +materialNo + ":" + materialNo);
                        String ids = productCategory.getClassId() + ":" + materialName;
                        if (materialIds.contains(ids)) {
                            throw new BusinessException(400, "物资基础库不存在不存在！");
                        }
                        if (StringUtils.isNotBlank(s)) {
                            try {
                                MaterialVo materialVo = new ObjectMapper().readValue(s, MaterialVo.class);
                                product.setRelevanceId(materialVo.getBillId());
                                product.setRelevanceNo(materialVo.getBillNo());
                                product.setRelevanceName(materialVo.getMaterialName());
                            } catch (JsonProcessingException e) {
                                e.printStackTrace();
                            }
                        } else {
                            MaterialVo materialVo = saveMaterialInfo(productCategory.getClassId(),materialNo,materialName);

                            if (materialVo != null) {
                                product.setRelevanceName(materialVo.getMaterialName());
                                product.setRelevanceId(materialVo.getBillId());
                                product.setRelevanceNo(materialVo.getBillNo());
                            }
                        }
                    }else {
                        throw new BusinessException(500,"物料名称和物料编码不能为空！");
                    }
                    String classId = productCategory.getClassId();
                    if (StringUtils.isNotBlank(material.getBrandName())) {
                        Brand brand = brandService.findByClassIdAndBrandName(classId, material.getBrandName());
                        if (brand != null) {
                            product.setBrandId(brand.getBrandId());
                            product.setBrandName(brand.getName());
                        } else {
                            Brand newBrand = new Brand();
                            newBrand.setClassId(classId);
                            newBrand.setClassPath(cp);
                            newBrand.setName(material.getBrandName());
                            newBrand.setClassName(productCategory.getClassName());
                            newBrand.setState(1);
                            brandService.save(newBrand);
                            product.setBrandId(newBrand.getBrandId());
                            product.setBrandName(newBrand.getName());
                        }
                    }
                    product.setProductType(0);
                    product.setSupperBy(user.getEnterpriseId());
                    product.setSupplierName(user.getEnterpriseName());
                    product.setIsCompletion(0);
                    product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
                    product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
                    product.setClassId(productCategory.getClassId());
                    if (material.getSellPrice()==null){
                        throw new BusinessException("销售价格不能为空");
                    }
                    product.setProductMinPrice(material.getSellPrice());
                    product.setClassPath(cp.substring(0, cp.length() - 1));
                    // 设置为-1提交，确认了才可
                    product.setState(0);
                    boolean save1 = save(product);
                    if (!save1) {
                        throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "保存失败！");
                    }
                    // 保存规格
                    ProductSku productSku = new ProductSku();
                    BeanUtils.copyProperties(material, productSku);
                    productSku.setProductId(product.getProductId());
                    if (product.getProductType()==10){
                        if (productSku.getSellPrice()==null||productSku.getSellPrice().compareTo(BigDecimal.valueOf(0))==0){
                            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "销售价格不能为空！");
                        }
                    }
                    productSku.setSCostPrice(productSku.getCostPrice());
                    productSku.setSSellPrice(productSku.getSellPrice());
                    // 规格主图默认是商品的主图
                    productSku.setState(PublicEnum.STATE_STOP.getCode());
                    boolean save5 = productSkuService.save(productSku);
                    if (!save5) {
                        throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品保存失败！");
                    }
                } catch (Exception e) {
                    vo.setState(0);
                    vo.setFail(e.getMessage());
                }
                vos.add(vo);
            }
            return vos;
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
        }
        return new ArrayList<>();
    }

    private void setProductTaxRate(String enterpriseId, Product product) {
        EnterpriseInfo byId = enterpriseInfoService.getById(enterpriseId);
        product.setTaxRate(byId.getTaxRate());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public List<ImportExcelResultVO> shopUploadLcProductMallExcelFile(MultipartFile file) {
        try {
            ArrayList<ImportExcelResultVO> vos = new ArrayList<>();
            List<LcMaterial> objects = EasyExcelUtils.readExcelOneSheet(file.getInputStream(), LcMaterial.class);
            if (objects.size() > 4000) {
                throw new BusinessException(500, "数量超过4000禁止导入！");
            }
            for (LcMaterial material : objects) {
                ImportExcelResultVO vo = new ImportExcelResultVO();
                vo.setId(material.getId());
                vo.setProductName(material.getProductName());
                vo.setState(1);
                try {
                    if ((material.getProductDescribe() != null && material.getProductDescribe().length() > 2500)) {
                        throw new BusinessException(500, "商品描述不能超过2000个字符");
                    }
                    UserLogin user = ThreadLocalUtil.getCurrentUser();
                    String shopId = user.getShopId();
                    if (mallConfig.isProductRepetitionCheck == 1) {
                        Product product2 = lambdaQuery().eq(Product::getProductName, material.getProductName()).eq(Product::getSkuName, material.getSkuName()).eq(Product::getShopId, shopId).one();
                        if (product2 != null) {
                            throw new BusinessException(500, "商品名称重复");
                        }
                    }  // 保存商品信息
                    Product product = new Product();
                    BeanUtils.copyProperties(material, product);
                    product.setShopName(user.getShopName());
                    product.setShopId(user.getShopId());

                    //确定商品按照店铺排序
                    Shop shop = shopService.getById(shopId);
                    if (shop.getIsBusiness() == 1) {
                        product.setShopType(1);
                    } else if (shop.getIsInternalShop() == 1) {
                        product.setShopType(2);
                    } else {
                        product.setShopType(3);
                    }
                    // 处理分类路径
                    if (StringUtils.isEmpty(material.getClassNamePath())) {
                        throw new BusinessException(400, "分类不能为空！");
                    }
                    // 临购只保留主要分类
                    if (material.getClassNamePath().contains("低值易耗品") || material.getClassNamePath().contains("其它材料")) {
                        throw new BusinessException(400, "只能上架主要分类！");
                    }
                    String[] split = material.getClassNamePath().trim().split("/");
                    ProductCategory productCategory = productCategoryService.getDataByClassPathName(material.getClassNamePath().trim(), split.length, 1);
                    if (productCategory == null) {
                        throw new BusinessException(400, "分类不存在！");
                    }
                    // 处理分类路径
                    List<String> classPath = productCategoryService.getCategoryParentIdList(productCategory.getClassId());
                    String cp = "";
                    if (!CollectionUtils.isEmpty(classPath)) {
                        for (String s : classPath) {
                            cp += s + "/";
                        }
                    }
                    //保存物料信息
                    String materialName = material.getMaterialName();
                    String materialNo = material.getMaterialNo();
                    if (StringUtils.isNotBlank(materialName)&&StringUtils.isNotBlank(materialNo)) {
                        String s = stringRedisTemplate.opsForValue().get(productCategory.getClassId() + ":" + materialNo + ":" +materialName);
                        if (StringUtils.isNotBlank(s)) {
                            try {
                                MaterialVo materialVo = new ObjectMapper().readValue(s, MaterialVo.class);
                                product.setRelevanceId(materialVo.getBillId());
                                product.setRelevanceNo(materialVo.getBillNo());
                                product.setRelevanceName(materialVo.getMaterialName());
                            } catch (JsonProcessingException e) {
                                e.printStackTrace();
                            }
                        } else {
                            MaterialVo materialVo = saveMaterialInfo(productCategory.getClassId(),materialNo,materialName);

                            if (materialVo != null) {
                                product.setRelevanceName(materialVo.getMaterialName());
                                product.setRelevanceId(materialVo.getBillId());
                                product.setRelevanceNo(materialVo.getBillNo());
                            }


                        }

                    }else {
                        throw new BusinessException(500,"物料名称和物料编码不能为空！");
                    }
                    String classId = productCategory.getClassId();
                    if (StringUtils.isNotBlank(material.getBrandName())) {
                        Brand brand = brandService.findByClassIdAndBrandName(classId, material.getBrandName());
                        if (brand != null) {
                            product.setBrandId(brand.getBrandId());
                            product.setBrandName(brand.getName());
                        } else {
                            Brand newBrand = new Brand();
                            newBrand.setClassId(classId);
                            newBrand.setClassPath(cp);
                            newBrand.setName(material.getBrandName());
                            newBrand.setClassName(productCategory.getClassName());
                            newBrand.setState(1);
                            brandService.save(newBrand);
                            product.setBrandId(newBrand.getBrandId());
                            product.setBrandName(newBrand.getName());
                        }
                    }
                    product.setProductType(1);
                    product.setSupperBy(user.getEnterpriseId());
                    product.setSupplierName(user.getEnterpriseName());
                    product.setIsCompletion(0);
                    product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
                    product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
                    product.setClassId(productCategory.getClassId());
                    product.setProductMinPrice(material.getSellPrice());
                    product.setClassPath(cp.substring(0, cp.length() - 1));
                    // 设置为-1提交，确认了才可
                    product.setState(0);
                    boolean save1 = save(product);
                    if (!save1) {
                        throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "保存失败！");
                    }
                    // 保存规格
                    ProductSku productSku = new ProductSku();
                    BeanUtils.copyProperties(material, productSku);
                    productSku.setProductId(product.getProductId());
                    productSku.setSCostPrice(productSku.getCostPrice());
                    productSku.setSSellPrice(productSku.getSellPrice());
                    // 规格主图默认是商品的主图
                    productSku.setState(PublicEnum.STATE_STOP.getCode());
                    boolean save5 = productSkuService.save(productSku);
                    if (!save5) {
                        throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品保存失败！");
                    }
                } catch (Exception e) {
                    vo.setState(0);
                    vo.setFail(e.getMessage());
                }
                vos.add(vo);
            }
            return vos;
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * 供方商品Excel导入
     *
     * @param file
     * @param shopId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportExcelResultVO> supplierUploadLcExcelFile(MultipartFile file, String shopId) {
        ProductService productService = SpringBeanUtil.getBean(ProductService.class);
        try {
            // 物资
            ArrayList<ImportExcelResultVO> vos = new ArrayList<>();
            List<LcShopMaterial> objects = EasyExcelUtils.readExcelOneSheet(file.getInputStream(), LcShopMaterial.class);
            if (objects.size() > 4000) {
                throw new BusinessException(500, "数量超过4000禁止导入！");
            }
            for (LcShopMaterial material : objects) {
                ImportExcelResultVO vo = new ImportExcelResultVO();
                vo.setId(material.getId());
                vo.setProductName(material.getProductName());
                vo.setState(1);
                try {
                    if (material.getProductDescribe() != null && material.getProductDescribe().length() > 2500) {
                        throw new BusinessException(500, "商品描述不能超过2000个字符");
                    }
                    productService.saveImportSupplierMaterial(material, shopId);

                } catch (Exception e) {
                    vo.setState(0);
                    vo.setFail(e.getMessage());
                }
                vos.add(vo);
            }
            return vos;
        } catch (IOException e) {
            log.error("异常信息：" + e.getMessage());
        }
        return new ArrayList<>();
    }

    @Override
    public void onSubmitMaterialSupplier(CreateMaterialSupplierDTO dto) {
        createMaterialSupplier(dto);

    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveImportSupplierMaterial(LcShopMaterial shopMaterial, String shopId) {
        if (StringUtils.isBlank(shopId)) {
            throw new BusinessException(500, "未选择店铺！");
        }
        if (mallConfig.isProductRepetitionCheck == 1) {
            Product product2 = lambdaQuery().eq(Product::getProductName, shopMaterial.getProductName()).eq(Product::getSkuName, shopMaterial.getSkuName()).eq(Product::getShopId, shopId).one();
            if (product2 != null) {
                throw new BusinessException(500, "商品名称重复");
            }
        }
        // 保存商品信息
        Product product = new Product();
        BeanUtils.copyProperties(shopMaterial, product);

        // 处理分类路径
        if (StringUtils.isEmpty(shopMaterial.getClassNamePath())) {
            throw new BusinessException(400, "分类不能为空！");
        }
        // 临购只保留主要分类
        if (shopMaterial.getClassNamePath().contains("低值易耗品") || shopMaterial.getClassNamePath().contains("其它材料")) {
            throw new BusinessException(400, "只能上架主要分类！");
        }

        String[] split = shopMaterial.getClassNamePath().trim().split("/");
        ProductCategory productCategory = productCategoryService.getDataByClassPathName(shopMaterial.getClassNamePath().trim(), split.length, 1);
//        ProductCategory productCategory = productCategoryService.getCategoryByClassNameAndLevel(className, split.length, null);
        if (productCategory == null) {
            throw new BusinessException(400, "分类不存在！");
        }
        List<String> categoryParentIdList = productCategoryService.getCategoryParentIdList(productCategory.getClassId());
        // 处理分类路径
        String cp = "";
        if (!CollectionUtils.isEmpty(categoryParentIdList)) {
            for (String s : categoryParentIdList) {
                cp += s + "/";
            }
        }

        //保存物料信息
        String materialName = shopMaterial.getMaterialName();
        String materialNo = shopMaterial.getMaterialNo();
        if (StringUtils.isNotBlank(materialName)&&StringUtils.isNotBlank(materialNo)) {
            String s = stringRedisTemplate.opsForValue().get(productCategory.getClassId() + ":" + materialNo + ":" +materialName);
            if (StringUtils.isNotBlank(s)) {
                try {
                    MaterialVo materialVo = new ObjectMapper().readValue(s, MaterialVo.class);
                    product.setRelevanceId(materialVo.getBillId());
                    product.setRelevanceNo(materialVo.getBillNo());
                    product.setRelevanceName(materialVo.getMaterialName());
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            } else {
                MaterialVo materialVo = saveMaterialInfo(productCategory.getClassId(),materialNo,materialName);
                if (materialVo != null) {
                    product.setRelevanceName(materialVo.getMaterialName());
                    product.setRelevanceId(materialVo.getBillId());
                    product.setRelevanceNo(materialVo.getBillNo());
                }
            }
        }else {
            throw new BusinessException(500,"物料名称和物料编码不能为空！");
        }
        String classId = productCategory.getClassId();
        if (StringUtils.isNotBlank(shopMaterial.getBrandName())) {
            Brand brand = brandService.findByClassIdAndBrandName(classId, shopMaterial.getBrandName());
            if (brand != null) {
                product.setBrandId(brand.getBrandId());
                product.setBrandName(brand.getName());
            } else {
                Brand newBrand = new Brand();
                newBrand.setClassId(classId);
                newBrand.setClassPath(cp);
                newBrand.setName(shopMaterial.getBrandName());
                newBrand.setClassName(productCategory.getClassName());
                newBrand.setState(1);
                brandService.save(newBrand);
                product.setBrandId(newBrand.getBrandId());
                product.setBrandName(newBrand.getName());
            }
        }


        UserLogin user = ThreadLocalUtil.getCurrentUser();
        product.setSupperBy(user.getEnterpriseId());
        product.setSupplierName(user.getEnterpriseName());
        product.setProductType(1);
        product.setClassPath(cp.substring(0, cp.length() - 1));
        product.setIsCompletion(0);
        product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
        product.setShopId(shopId);
        product.setProductMinPrice(shopMaterial.getSellPrice());
        product.setClassId(productCategory.getClassId());
        // 设置为-1提交，确认了才可
        product.setState(-1);
        product.setSupplierSubmitState(1);


        boolean save1 = save(product);
        if (!save1) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "保存失败！");
        }

        // 保存规格
        ProductSku productSku = new ProductSku();
        BeanUtils.copyProperties(shopMaterial, productSku);
        productSku.setProductId(product.getProductId());
        productSku.setSCostPrice(productSku.getCostPrice());
        productSku.setSSellPrice(productSku.getSellPrice());
        // 规格主图默认是商品的主图
        productSku.setState(PublicEnum.STATE_STOP.getCode());
        boolean save5 = productSkuService.save(productSku);
        if (!save5) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品保存失败！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportOuterProductVO> outerImportBatchLcMaterial(List<CuterCreateLcMaterialDTO> productList, HttpServletRequest request) {
        ProductService productService = SpringBeanUtil.getBean(ProductService.class);
        ArrayList<ImportOuterProductVO> vos = new ArrayList<>();
        for (CuterCreateLcMaterialDTO dto : productList) {
            ImportOuterProductVO vo = new ImportOuterProductVO();
            try {
                productService.outerImportLcMaterial(dto, vo, request);
            } catch (Exception e) {
                if (vo.getCode() == 50101) {
                    // 触发保存
                    ErrorInfo errorInfo = new ErrorInfo();
                    errorInfo.setBusinessType(1);
                    errorInfo.setErrorRqJson(JSON.toJSONString(dto));
                    errorInfo.setCreateTime(new Date());
                    errorInfo.setIsDispose(0);
                    errorInfo.setMethodName("outerImportBatchLcMaterial");
                    errorInfo.setErrorInfo(e.getMessage());
                    errorInfoService.save(errorInfo);
                }
                vo.setProductName(dto.getProductName());
                vo.setMessage(e.getMessage());
                if (vo.getCode() != null && vo.getCode() == 200) {
                    vo.setCode(500);
                }
            }
            vos.add(vo);
        }
        return vos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void outerImportLcMaterial(CuterCreateLcMaterialDTO dto, ImportOuterProductVO vo, HttpServletRequest request) {
        String codeByPrefix = CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark());
        vo.setCode(200);
        vo.setMessage("操作成功！");
        vo.setProductCode(codeByPrefix);
        vo.setProductName(dto.getProductName());
        vo.setOuterProductCode(dto.getOuterProductCode());
        if (dto.getProductDescribe() != null && dto.getProductDescribe().length() > 100000) {
            vo.setCode(50102);
            throw new BusinessException("商品描述内容过大！");
        }
        if (mallConfig.isProductRepetitionCheck == 1) {
            Integer count = lambdaQuery().eq(Product::getProductName, dto.getProductName()).eq(Product::getSkuName, dto.getSkuName()).eq(Product::getShopId, dto.getShopId()).eq(Product::getProductType, 0).count();
            if (count > 0) {
                vo.setCode(50100);
                throw new BusinessException("商品名称重复");
            }
        }
        ProductCategory productCategory = productCategoryService.lambdaQuery().eq(ProductCategory::getClassPath, dto.getClassNamePath()).one();
        if (productCategory == null) {
            vo.setCode(50090);
            throw new BusinessException("分类不存在！");
        }
        String basicsMaterialName = dto.getBasicsMaterialName().trim();
        String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + BASICS_MATERIAL_LIST;
        HashMap<Object, Object> paramsMap = new HashMap<>();
        paramsMap.put("pageIndex", 0);
        paramsMap.put("pageSize", 10);
        paramsMap.put("isActive", 1);
//        paramsMap.put("materialName", basicsMaterialName);
        paramsMap.put("materialNo", dto.getBasicsMaterialNo().trim());
        paramsMap.put("classId", productCategory.getClassId());
        PageUtils<Map> r = null;
        try {
            r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
        } catch (Exception e) {
            log.error("请求参数：" + paramsMap);
            vo.setCode(50096);
            throw new BusinessException(e.getMessage());
        }
        List<Map> list = r.getList();
        if (CollectionUtils.isEmpty(list)) {
            vo.setCode(50095);
            throw new BusinessException("物资基础库该分类下不存在该物资！");
        }

        // 保存商品信息
        Product product = new Product();
        BeanUtils.copyProperties(dto, product);
        // 默认都是已确认的
        product.setSupplierSubmitState(3);
        product.setSerialNum(codeByPrefix);
        product.setClassId(productCategory.getClassId());
        Map map = list.get(0);
        product.setRelevanceName((String) map.get("materialName"));
        product.setRelevanceNo((String) map.get("billNo"));
        product.setRelevanceId((String) map.get("billId"));
        changShopIdBusiness(product);
        Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, product.getShopId())
                .select(Shop::getIsBusiness, Shop::getIsInternalShop).one();
        if (shop.getIsBusiness() == 1) {
            product.setShopType(1);
        } else if (shop.getIsInternalShop() == 1) {
            product.setShopType(2);
        } else {
            product.setShopType(3);
        }
        product.setProductType(1);
        // 处理分类路径
        List<String> classPath = productCategoryService.getCategoryParentIdList(productCategory.getClassId());
        String cp = "";
        if (!CollectionUtils.isEmpty(classPath)) {
            for (String s : classPath) {
                cp += s + "/";
            }
        }
        if (CollectionUtils.isEmpty(classPath)) {
            vo.setCode(50090);
            throw new BusinessException("分类不存在");
        }
        String supplier_private_key = request.getHeader("supplier_private_key");
        String shopIdR = request.getHeader("shop_id");
        PrivateKeySupplier supplier = privateKeySupplierService.lambdaQuery().eq(PrivateKeySupplier::getPrivateKey, supplier_private_key).eq(PrivateKeySupplier::getShopId, shopIdR).select(PrivateKeySupplier::getSupplierId, PrivateKeySupplier::getSupplierName).one();
        if (supplier == null) {
            vo.setCode(50097);
            throw new BusinessException("未配置供应商id！");
        }
        product.setSupperBy(supplier.getSupplierId());
        product.setSupplierName(supplier.getSupplierName());
        product.setClassPath(cp.substring(0, cp.length() - 1));
        String classIdPath = (String) map.get("classIdPath");
        if (!product.getClassPath().equals(classIdPath)) {
            vo.setCode(50101);
            throw new BusinessException("商城分类和pcwp分类不统一！");
        }
        // 如果物资基础库被停用，则无法上架
        int enable = (int) map.get("isEnable");
        if (enable == 0) {
            throw new BusinessException("物资基础库【 " + map.get("classNamePath") + " 】已经被停用，请修改商品物资基础库");
        }
        product.setIsCompletion(ProductEnum.IS_COMPLETION_1.getCode());
//        product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
        Integer isPutaway = dto.getIsPutaway();
        if (isPutaway != null && isPutaway == 1) {
            product.setState(3);
        } else {
            product.setState(ProductEnum.STATE_STAY_PUTAWAY.getCode());
        }
        product.setProductMinImg(dto.getMinFile().get(0).getUrl());
        product.setMallType(0);
        product.setIsOpenImport(1);
        product.setProductMinPrice(dto.getSellPrice());
        // 材质
        product.setProductTexture(dto.getProductTexture());

//        if (mallConfig.isApiImportProductBradDispose == 2) {
//            if (StringUtils.isNotBlank(dto.getBrandName())) {
//                Brand brand = brandService.lambdaQuery().eq(Brand::getName, dto.getBrandName().trim()).eq(Brand::getClassId, productCategory.getClassId()).one();
//                if (brand == null) {
//                    vo.setErrorCode(50098);
//                    throw new BusinessException("对应分类品牌不存在！");
//                } else {
//                    product.setBrandId(brand.getBrandId());
//                    product.setBrandName(dto.getBrandName().trim());
//                }
//            }
//        }

//        if (mallConfig.isApiImportProductBradDispose == 1) {
        if (StringUtils.isNotBlank(dto.getBrandName())) {
            // 处理品牌
            String brandName = dto.getBrandName();
            String newBrandName = brandName.trim();
            Brand brand = brandService.lambdaQuery().eq(Brand::getName, newBrandName).eq(Brand::getClassId, productCategory.getClassId()).one();
            if (brand == null) {
                Brand brand1 = new Brand();
                brand1.setName(newBrandName);
                brand1.setClassId(product.getClassId());
                String className = productCategoryService.getById(productCategory.getClassId()).getClassName();
                brand1.setClassName(className);
                brand1.setClassPath(product.getClassPath());
                brandService.save(brand1);
                product.setBrandId(brand1.getBrandId());
                product.setBrandName(newBrandName);
            } else {
                product.setBrandId(brand.getBrandId());
                product.setBrandName(newBrandName);
            }
        }
//        }


        // 处理计量单位
        String unitTrim = dto.getUnit().trim();
        SystemParam materialUnit = systemParamService.lambdaQuery().eq(SystemParam::getKeyValue, unitTrim).eq(SystemParam::getCode, "materialUnit").one();
        if (materialUnit == null) {
            SystemParam systemParam = new SystemParam();
            systemParam.setName("计量单位");
            systemParam.setCode("materialUnit");
            systemParam.setKeyValue(unitTrim);
            systemParam.setKeyValue2(unitTrim);
            systemParam.setMaintain(1);
            systemParam.setType(1);
            systemParamService.save(systemParam);
        }


        boolean save1 = save(product);
        if (!save1) {
            vo.setCode(50091);
            throw new BusinessException("商品保存失败！");
        }
        // 保存成功应该修改该分类为有商品
        productCategoryService.updateCategoryYesProduct(productCategory.getClassId());

        // 保存主图
        ImportProductVO importProductVO = dto.getAdminFile().get(0);
        File adminFile = new File();
        BeanUtils.copyProperties(importProductVO, adminFile);
        adminFile.setIsMain(1);
        adminFile.setRelevanceType(1);
        adminFile.setFileType(1);
        adminFile.setImgType(0);
        adminFile.setRelevanceId(product.getProductId());
        boolean save2 = fileService.save(adminFile);
        if (!save2) {
            vo.setCode(50092);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "主图保存失败！");
        }

        // 保存商品图片
        List<ImportProductVO> productFileVOs = dto.getProductFiles();
        ArrayList<File> productFile = new ArrayList<>();
        for (ImportProductVO productFileVO : productFileVOs) {
            File file = new File();
            BeanUtils.copyProperties(productFileVO, file);
            file.setIsMain(0);
            file.setRelevanceType(1);
            file.setFileType(1);
            file.setImgType(0);
            file.setRelevanceId(product.getProductId());
            productFile.add(file);
        }
        boolean save3 = fileService.saveBatch(productFile);
        if (!save3) {
            vo.setCode(50093);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品图片保存失败！");
        }

        // 保存小图
        ImportProductVO minFileVO = dto.getMinFile().get(0);
        File minFile = new File();
        BeanUtils.copyProperties(minFileVO, minFile);
        minFile.setIsMain(0);
        minFile.setRelevanceType(1);
        minFile.setFileType(1);
        minFile.setImgType(1);
        minFile.setRelevanceId(product.getProductId());
        boolean save4 = fileService.save(minFile);
        if (!save4) {
            vo.setCode(50094);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "小图保存失败！");
        }
        // 保存规格
        ProductSku productSku = new ProductSku();
        BeanUtils.copyProperties(dto, productSku);
        //productSku.setSecondUnitNum(dto.getSecondUnitNum());
        //productSku.setSecondUnit(dto.getSecondUnit());
        // 规格型号为空
        if (StringUtils.isBlank(dto.getSkuName())) {
            productSku.setSkuName(dto.getSkuName());
        }
        productSku.setProductId(product.getProductId());
        productSku.setSkuImg(minFile.getUrl());
        productSku.setState(PublicEnum.STATE_STOP.getCode());
        productSku.setUnit(unitTrim);
        productSku.setSCostPrice(productSku.getCostPrice());
        productSku.setSSellPrice(productSku.getSellPrice());
        boolean save5 = productSkuService.save(productSku);
        if (!save5) {
            vo.setCode(50091);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品保存失败！");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ImportOuterProductVO> importBatchLcMaterialShop(List<CuterCreateLcMaterialDTO> productList, HttpServletRequest request) {
        ProductService productService = SpringBeanUtil.getBean(ProductService.class);
        ArrayList<ImportOuterProductVO> vos = new ArrayList<>();
        for (CuterCreateLcMaterialDTO dto : productList) {
            ImportOuterProductVO vo = new ImportOuterProductVO();
            try {
                productService.outerImportLcMaterialShop(dto, vo, request);
            } catch (Exception e) {
                if (vo.getCode() == 50101) {
                    // 触发保存
                    ErrorInfo errorInfo = new ErrorInfo();
                    errorInfo.setBusinessType(1);
                    errorInfo.setErrorRqJson(JSON.toJSONString(dto));
                    errorInfo.setCreateTime(new Date());
                    errorInfo.setIsDispose(0);
                    errorInfo.setMethodName("importBatchLcMaterialShop");
                    errorInfo.setErrorInfo(e.getMessage());
                    errorInfoService.save(errorInfo);
                }
                vo.setProductName(dto.getProductName());
                vo.setMessage(e.getMessage());
                if (vo.getCode() != null && vo.getCode() == 200) {
                    vo.setCode(500);
                }
            }
            vos.add(vo);
        }
        return vos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void outerImportLcMaterialShop(CuterCreateLcMaterialDTO dto, ImportOuterProductVO vo, HttpServletRequest request) {
        String codeByPrefix = CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark());
        vo.setCode(200);
        vo.setMessage("操作成功！");
        vo.setProductCode(codeByPrefix);
        vo.setProductName(dto.getProductName());
        vo.setOuterProductCode(dto.getOuterProductCode());
        if (dto.getProductDescribe() != null && dto.getProductDescribe().length() > 100000) {
            vo.setCode(50102);
            throw new BusinessException("商品描述内容过大！");
        }
        if (mallConfig.isProductRepetitionCheck == 1) {
            Integer count = lambdaQuery().eq(Product::getProductName, dto.getProductName()).eq(Product::getSkuName, dto.getSkuName()).eq(Product::getShopId, dto.getShopId()).eq(Product::getProductType, 0).count();
            if (count > 0) {
                vo.setCode(50100);
                throw new BusinessException("商品名称重复");
            }
        }
        ProductCategory productCategory = productCategoryService.lambdaQuery().eq(ProductCategory::getClassPath, dto.getClassNamePath()).one();
        if (productCategory == null) {
            vo.setCode(50090);
            throw new BusinessException("分类不存在！");
        }
        String basicsMaterialName = dto.getBasicsMaterialName().trim();
        String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + BASICS_MATERIAL_LIST;
        HashMap<Object, Object> paramsMap = new HashMap<>();
        paramsMap.put("pageIndex", 0);
        paramsMap.put("pageSize", 10);
        paramsMap.put("isActive", 1);
//        paramsMap.put("materialName", basicsMaterialName);
        paramsMap.put("materialNo", dto.getBasicsMaterialNo().trim());
        paramsMap.put("classId", productCategory.getClassId());
        PageUtils<Map> r = null;
        try {
            r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
        } catch (Exception e) {
            log.error("请求参数：" + paramsMap);
            vo.setCode(50096);
            throw new BusinessException(e.getMessage());
        }
        List<Map> list = r.getList();
        if (CollectionUtils.isEmpty(list)) {
            vo.setCode(50095);
            throw new BusinessException("物资基础库该分类下不存在该物资！");
        }

        // 保存商品信息
        Product product = new Product();
        BeanUtils.copyProperties(dto, product);
        product.setSerialNum(codeByPrefix);
        product.setClassId(productCategory.getClassId());
        Map map = list.get(0);
        product.setRelevanceName((String) map.get("materialName"));
        product.setRelevanceNo((String) map.get("billNo"));
        product.setRelevanceId((String) map.get("billId"));
        Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, product.getShopId())
                .select(Shop::getIsBusiness, Shop::getIsInternalShop).one();
        if (shop.getIsBusiness() == 1) {
            product.setShopType(1);
        } else if (shop.getIsInternalShop() == 1) {
            product.setShopType(2);
        } else {
            product.setShopType(3);
        }
        product.setProductType(1);
        // 材质
        product.setProductTexture(dto.getProductTexture());
        // 处理分类路径
        List<String> classPath = productCategoryService.getCategoryParentIdList(productCategory.getClassId());
        String cp = "";
        if (!CollectionUtils.isEmpty(classPath)) {
            for (String s : classPath) {
                cp += s + "/";
            }
        }
        if (CollectionUtils.isEmpty(classPath)) {
            vo.setCode(50090);
            throw new BusinessException("分类不存在");
        }
        String supplier_private_key = request.getHeader("supplier_private_key");
        String shopIdR = request.getHeader("shop_id");
        PrivateKeySupplier supplier = privateKeySupplierService.lambdaQuery().eq(PrivateKeySupplier::getPrivateKey, supplier_private_key).eq(PrivateKeySupplier::getShopId, shopIdR).select(PrivateKeySupplier::getSupplierId, PrivateKeySupplier::getSupplierName).one();
        if (supplier == null) {
            vo.setCode(50097);
            throw new BusinessException("未配置供应商id！");
        }
        product.setSupperBy(supplier.getSupplierId());
        product.setClassPath(cp.substring(0, cp.length() - 1));
        String classIdPath = (String) map.get("classIdPath");
        if (!product.getClassPath().equals(classIdPath)) {
            vo.setCode(50101);
            throw new BusinessException("商城分类和pcwp分类不统一！");
        }
        // 如果物资基础库被停用，则无法上架
        int enable = (int) map.get("isEnable");
        if (enable == 0) {
            throw new BusinessException("物资基础库【 " + map.get("classNamePath") + " 】已经被停用，请修改商品物资基础库");
        }
        product.setIsCompletion(ProductEnum.IS_COMPLETION_1.getCode());
//        product.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_WZ.getRemark()));
        Integer isPutaway = dto.getIsPutaway();
        if (isPutaway != null && isPutaway == 1) {
            product.setState(3);
        } else {
            product.setState(ProductEnum.STATE_STAY_PUTAWAY.getCode());
        }
        product.setProductMinImg(dto.getMinFile().get(0).getUrl());
        product.setMallType(0);
        product.setIsOpenImport(1);
        product.setProductMinPrice(dto.getSellPrice());


//        if (mallConfig.isApiImportProductBradDispose == 2) {
//            if (StringUtils.isNotBlank(dto.getBrandName())) {
//                Brand brand = brandService.lambdaQuery().eq(Brand::getName, dto.getBrandName().trim()).eq(Brand::getClassId, productCategory.getClassId()).one();
//                if (brand == null) {
//                    vo.setErrorCode(50098);
//                    throw new BusinessException("对应分类品牌不存在！");
//                } else {
//                    product.setBrandId(brand.getBrandId());
//                    product.setBrandName(dto.getBrandName().trim());
//                }
//            }
//        }


//        if (mallConfig.isApiImportProductBradDispose == 1) {
        if (StringUtils.isNotBlank(dto.getBrandName())) {
            // 处理品牌
            String brandName = dto.getBrandName();
            String newBrandName = brandName.trim();
            Brand brand = brandService.lambdaQuery().eq(Brand::getName, newBrandName).eq(Brand::getClassId, productCategory.getClassId()).one();
            if (brand == null) {
                Brand brand1 = new Brand();
                brand1.setName(newBrandName);
                brand1.setClassId(product.getClassId());
                String className = productCategoryService.getById(productCategory.getClassId()).getClassName();
                brand1.setClassName(className);
                brand1.setClassPath(product.getClassPath());
                brandService.save(brand1);
                product.setBrandId(brand1.getBrandId());
                product.setBrandName(newBrandName);
            } else {
                product.setBrandId(brand.getBrandId());
                product.setBrandName(newBrandName);
            }
        }
//        }

        // 处理计量单位
        String unitTrim = dto.getUnit().trim();
        SystemParam materialUnit = systemParamService.lambdaQuery().eq(SystemParam::getKeyValue, unitTrim).eq(SystemParam::getCode, "materialUnit").one();
        if (materialUnit == null) {
            SystemParam systemParam = new SystemParam();
            systemParam.setName("计量单位");
            systemParam.setCode("materialUnit");
            systemParam.setKeyValue(unitTrim);
            systemParam.setKeyValue2(unitTrim);
            systemParam.setMaintain(1);
            systemParam.setType(1);
            systemParamService.save(systemParam);
        }


        boolean save1 = save(product);
        if (!save1) {
            vo.setCode(50091);
            throw new BusinessException("商品保存失败！");
        }
        // 保存成功应该修改该分类为有商品
        productCategoryService.updateCategoryYesProduct(productCategory.getClassId());

        // 保存主图
        ImportProductVO importProductVO = dto.getAdminFile().get(0);
        File adminFile = new File();
        BeanUtils.copyProperties(importProductVO, adminFile);
        adminFile.setIsMain(1);
        adminFile.setRelevanceType(1);
        adminFile.setFileType(1);
        adminFile.setImgType(0);
        adminFile.setRelevanceId(product.getProductId());
        boolean save2 = fileService.save(adminFile);
        if (!save2) {
            vo.setCode(50092);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "主图保存失败！");
        }

        // 保存商品图片
        List<ImportProductVO> productFileVOs = dto.getProductFiles();
        ArrayList<File> productFile = new ArrayList<>();
        for (ImportProductVO productFileVO : productFileVOs) {
            File file = new File();
            BeanUtils.copyProperties(productFileVO, file);
            file.setIsMain(0);
            file.setRelevanceType(1);
            file.setFileType(1);
            file.setImgType(0);
            file.setRelevanceId(product.getProductId());
            productFile.add(file);
        }
        boolean save3 = fileService.saveBatch(productFile);
        if (!save3) {
            vo.setCode(50093);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品图片保存失败！");
        }

        // 保存小图
        ImportProductVO minFileVO = dto.getMinFile().get(0);
        File minFile = new File();
        BeanUtils.copyProperties(minFileVO, minFile);
        minFile.setIsMain(0);
        minFile.setRelevanceType(1);
        minFile.setFileType(1);
        minFile.setImgType(1);
        minFile.setRelevanceId(product.getProductId());
        boolean save4 = fileService.save(minFile);
        if (!save4) {
            vo.setCode(50094);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "小图保存失败！");
        }
        // 保存规格
        ProductSku productSku = new ProductSku();
        BeanUtils.copyProperties(dto, productSku);
        productSku.setProductId(product.getProductId());
        productSku.setSkuImg(minFile.getUrl());
        productSku.setState(PublicEnum.STATE_STOP.getCode());
        productSku.setUnit(unitTrim);
        //productSku.setSecondUnitNum(dto.getSecondUnitNum());
        productSku.setSCostPrice(productSku.getCostPrice());
        productSku.setSSellPrice(productSku.getSellPrice());
        boolean save5 = productSkuService.save(productSku);
        if (!save5) {
            vo.setCode(50091);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品保存失败！");
        }
    }

    @Override
    public void updateMaterialAndState(UpdateProductAndStateDTO dto) {
        // 保存
        UpdateMaterialDTO updateMaterialDTO = new UpdateMaterialDTO();
        BeanUtils.copyProperties(dto, updateMaterialDTO);
        updateMaterial(updateMaterialDTO);
        //上架
        UpdateProductStateDTO state = new UpdateProductStateDTO();
        BeanUtils.copyProperties(dto, state);
        updateProductState(state);
    }


    @Override
    public void putawayProductExport(JSONObject jsonObject, HttpServletResponse response) {
        String shopId = ThreadLocalUtil.getCurrentUser().getShopId();
        String src = mallConfig.templateFormUrl;
        QueryWrapper<Product> wrapper = new QueryWrapper<>();
        wrapper.eq("p.shop_id ", shopId);
        QueryWrapper<Product> q = getProductExport(jsonObject, wrapper);
        List<Product> list = baseMapper.findAllProduct(q);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", list);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "物资商品导出模板.xlsx", src, "物资商品.xlsx");
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @NotNull
    private QueryWrapper<Product> getProductExport(JSONObject jsonObject, QueryWrapper<Product> q) {

        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String productName = (String) innerMap.get("productName");
        String classId = (String) innerMap.get("classId");

        String startCreateDate = (String) innerMap.get("startCreateDate");
        String serialNum = (String) innerMap.get("serialNum");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        String startModifiedDate = (String) innerMap.get("startModifiedDate");
        String endModifiedDate = (String) innerMap.get("endModifiedDate");
        String startPutawayDate = (String) innerMap.get("startPutawayDate");
        String endPutawayDate = (String) innerMap.get("endPutawayDate");
        String belowPrice = (String) innerMap.get("belowPrice");
        String abovePrice = (String) innerMap.get("abovePrice");
        String supplierName = (String) innerMap.get("supplierName");
        String shopName = (String) innerMap.get("shopName");
        Integer productType = (Integer) innerMap.get("productType");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        Integer isOneself = (Integer) innerMap.get("isOneself");

        q.eq("p.state", 1);
        q.eq("p.is_delete", 0);
        ArrayList ids = (ArrayList) innerMap.get("productIds");
        if (ids != null && ids.size() > 0) {
            q.in("p.product_id", ids);
        } else {
            q.like(productName != null, "p.product_name", productName);
            if (isOneself != null) {
                if (isOneself == 0) {
                    // 如果是3表示是确认过来的物资，不是自有的
                    q.eq("p.supplier_submit_state", 3);
                }
                // 如果是0表示是自有的
                if (isOneself == 1) {
                    q.eq("p.supplier_submit_state", 0);
                }
            }
            if (orderBy == null) {
                q.orderByDesc("p.putaway_date ");
            } else {
                q.orderByDesc(orderBy == 0, "p.putaway_date ");
                q.orderByDesc(orderBy == 1, "p.shop_sort ");
                q.orderByDesc(orderBy == 2, "p.gmt_create ");
                q.orderByDesc(orderBy == 3, "p.gmt_modified ");

            }
            q.eq("p.mall_type ", mallConfig.mallType);
            if (StringUtils.isNotEmpty(keywords)) {
                q.and(wrapper -> wrapper.like("p.product_name", keywords)
                        .or().like("p.supplier_name", keywords)
                        .or().like("p.serial_num", keywords));
            }

            q.like(StringUtils.isNotEmpty(supplierName), "p.supplier_name", supplierName);
            q.like(StringUtils.isNotEmpty(shopName), "sh.shop_name", shopName);
            q.like(StringUtils.isNotEmpty(serialNum), "p.serial_num", serialNum);
            q.like(StringUtils.isNotEmpty(classId), "p.class_path", classId);

            q.between(StringUtils.isNotEmpty(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), "p.gmt_create ", startCreateDate, endCreateDate);
            q.between(StringUtils.isNotEmpty(startModifiedDate) && StringUtils.isNotEmpty(endModifiedDate), "p.gmt_modified ", startModifiedDate, endModifiedDate);
            q.between(StringUtils.isNotEmpty(startPutawayDate) && StringUtils.isNotEmpty(endPutawayDate), "p.putaway_date ", startPutawayDate, endPutawayDate);
            q.ge(StringUtils.isNotBlank(abovePrice), "p.product_min_price ", abovePrice);
            q.le(StringUtils.isNotBlank(belowPrice), "p.product_min_price ", belowPrice);
            q.eq(productType != null, "p.product_type ", productType);
            q.select(Product.class, f -> {
                return !f.getProperty().equals("productDescribe") && !f.getProperty().equals("remarks");
            });
            q.last("LIMIT 4000");

        }
        return q;
    }

    @Override
    public void listMaterialSupplierExport(JSONObject jsonObject, HttpServletResponse response) {
        String src = mallConfig.templateFormUrl;
        QueryWrapper<Product> wrapper = new QueryWrapper<>();

        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();

        String shopId = (String) jsonObject.getInnerMap().get("shopId");
        ArrayList<String> supplierSubmitStates = (ArrayList<String>) jsonObject.getInnerMap().get("supplierSubmitStates");
        wrapper.eq("p.`supper_by`", enterpriseId);
        wrapper.eq(shopId != null, "p.`shop_id`", shopId);
        if (supplierSubmitStates != null && supplierSubmitStates.size() > 0) {
            wrapper.in("p.`supplier_submit_state`", supplierSubmitStates);
        }
        QueryWrapper<Product> q = getProductExport(jsonObject, wrapper);
        List<Product> list = baseMapper.findAllProduct(q);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", list);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "物资商品导出模板.xlsx", src, "物资商品.xlsx");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateClassLJDLSFJ() {
        List<Product> list2 = lambdaQuery().notLike(Product::getClassPath, "%/%/%")
                .like(Product::getClassPath, "%/%")
                .select(Product::getProductId, Product::getRelevanceNo)
                .list();
        for (Product product : list2) {
            String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + BASICS_MATERIAL_LIST;
            HashMap<Object, Object> paramsMap = new HashMap<>();
            paramsMap.put("pageIndex", 0);
            paramsMap.put("pageSize", 10);
            paramsMap.put("materialNo", product.getRelevanceNo());
            PageUtils<Map> r = null;
            try {
                r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
            } catch (Exception e) {
                throw new BusinessException("【远程异常】获取基础库物资错误：" + e.getMessage());
            }
            List<Map> list = r.getList();
            if (CollectionUtils.isEmpty(list)) {
                throw new BusinessException("物资基础库该下不存在该物资！");
            }
//            Map map = list.get(0);
            List<Map> collect = list.stream().filter(item -> Integer.valueOf(item.get("isEnable").toString()) == 1).collect(Collectors.toList());
            Map map = collect.get(0);
            log.info("pcwp查询物资基础库分类返回：" + map);
            Integer classQueryCount = productCategoryService.lambdaQuery().eq(ProductCategory::getClassPath, map.get("classNamePath")).count();
            if (classQueryCount == 0) {
                throw new BusinessException(500085, "和pcwp分类不统一！");
            }
            // 如果物资基础库被停用，则无法上架
            int enable = (int) map.get("isEnable");
            if (enable == 0) {
                throw new BusinessException("物资基础库【 " + map.get("classNamePath") + " 】已经被停用，请修改商品物资基础库");
            }
            boolean update = lambdaUpdate()
                    .eq(Product::getProductId, product.getProductId())
                    .set(Product::getClassPath, map.get("classIdPath"))
                    .set(Product::getClassId, map.get("classId")).update();
            System.out.println(update);
        }
    }

    @Override
    public List<Product> selectAllByRulesClass(ProductRulesClass vo) {
        LambdaQueryChainWrapper<Product> q = lambdaQuery();
        //物资公司是多店铺供应商 （1 普通店铺 2多供应商店铺）

        //供应商商品

        //低值易耗品
        q.eq(Product::getProductType, 0)
                .eq(Product::getState, 1)
                .eq(Product::getClassId, vo.getClassId())
                .eq(Product::getRelevanceId, vo.getMaterialId());
        if (StringUtils.isNotBlank(vo.getBrandName())) {
            q.eq(Product::getBrandName, vo.getBrandName());
        }
        //多个规格用，隔开 ，查询品牌和多个规格（like）
        if (StringUtils.isNotBlank(vo.getSkuName())) {
            String[] split = vo.getSkuName().split("，");
            for (int i = 0; i < split.length; i++) {
                String sku = split[i];
                if (i == 0) {
                    q.and(wrapper -> wrapper.like(Product::getSkuName, "%" + sku.trim() + "%"));
                } else {
                    q.or(wrapper -> wrapper.like(Product::getSkuName, "%" + sku.trim() + "%"));
                }
            }

        }
        return q.list();


    }


    @Override
    public BigDecimal selfShopDataByRulesClass(ProductRulesClass vo) {
        LambdaQueryChainWrapper<Product> q = lambdaQuery();
        //物资公司是多店铺供应商 （1 普通店铺 2多供应商店铺）
        Shop one = shopService.lambdaQuery().eq(Shop::getShopClass, 2).one();
        //供应商商品
        q.eq(Product::getShopId, one.getShopId())
                //低值易耗品
                .eq(Product::getProductType, 0)
                .eq(Product::getClassId, vo.getClassId())
                .eq(Product::getRelevanceId, vo.getMaterialId());
        if (StringUtils.isNotBlank(vo.getBrandName())) {
            q.eq(Product::getBrandName, vo.getBrandName());
        }
        if (StringUtils.isNotBlank(vo.getSkuName())) {
            q.eq(Product::getSkuName, vo.getSkuName());
        }
        List<Product> list = q.list();
        return null;
    }


    @Override
    public List<Product> getAverageWarningProductList(ProductRulesClass info) {
        LambdaQueryChainWrapper<Product> q = lambdaQuery();
        q.eq(Product::getClassId, info.getClassId());
        q.eq(Product::getRelevanceId, info.getMaterialId());
        q.eq(Product::getState, 1);
        //品牌
        if (StringUtils.isNotBlank(info.getBrandName())) {
            q.eq(Product::getBrandName, info.getBrandName());
        }
        //多个规格用，隔开 ，查询品牌和多个规格（like）
        if (StringUtils.isNotBlank(info.getSkuName())) {
            String[] split = info.getSkuName().split("，");
            for (int i = 0; i < split.length; i++) {
                String sku = split[i];
                if (i == 0) {
                    q.and(wrapper -> wrapper.like(Product::getSkuName, "%" + sku.trim() + "%"));
                } else {
                    q.or(wrapper -> wrapper.like(Product::getSkuName, "%" + sku.trim() + "%"));
                }
            }

        }
        List<Product> list = q.list();
        return list;
    }


    @Override
    public BigDecimal averageSellPrice(List<Product> productList) {
        if (productList != null && productList.size() > 0) {
            BigDecimal sumSellPrice = productList.stream()
                    .map(Product::getSellPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal averageSellPrice = productList.isEmpty() ? BigDecimal.ZERO :
                    sumSellPrice.divide(BigDecimal.valueOf(productList.size()), 2, BigDecimal.ROUND_HALF_UP);
            return averageSellPrice;
        } else {
            throw new BusinessException(500, "没有商品");
        }
    }


    /**
     * 校验物资基础库是否存在或被停用
     *
     * @param relevanceNo 物资基础库编号
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleProductTextureStatus(String relevanceNo) {
        String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + BASICS_MATERIAL_LIST;
        HashMap<Object, Object> paramsMap = new HashMap<>();
        paramsMap.put("versionId","1942518269757186050");
        paramsMap.put("pageIndex", 0);
        paramsMap.put("pageSize", 10);
        paramsMap.put("isActive", true);
        paramsMap.put("materialNo", relevanceNo);

        PageUtils<Map> r = null;
        try {
            r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
        } catch (Exception e) {
            throw new BusinessException("【远程异常】获取基础库物资错误：" + e.getMessage());
        }
        List<Map> list = r.getList();
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("物资基础库该下不存在或者已停用物资编号：" + relevanceNo + "的物资，请联系管理员去物资基础库启用或者重新选择物资");
        }
        List<Map> collect = list.stream().filter(item -> Integer.valueOf(item.get("isEnable").toString()) == 1).collect(Collectors.toList());

        Map map = collect.get(0);

//        Map map = list.get(0);
        log.info("pcwp查询物资基础库分类返回：" + map);
        // 如果物资基础库被停用，则无法上架
        int enable = (int) map.get("isEnable");
        if (enable == 0) {
            throw new BusinessException("物资基础库【 " + map.get("classNamePath") + " 】已经被停用，请修改商品物资基础库");
        }


    }

    @Override
    public void setProdctSellPrise(List<Product> productList) {
        for (Product product : productList) {
            //判断商品是否是区域商品
            ProductSku productSku = productSkuService.lambdaQuery().eq(ProductSku::getProductId, product.getProductId()).list().get(0);
            product.setSellPrice(productSku.getSellPrice());
        }
    }


    @Override
    public MaterialDtlDTO getRedisMaterialInfo(String classId, String billNo, String basicsMaterialName) {
        String s = stringRedisTemplate.opsForValue()
                .get(MaterialLockUtils.CLASS_ID_AND_RELEVANCE_N0_LOCK_KEY +
                        classId + ":" + billNo);
        if (s != null) {
            MaterialDtlDTO materialDtlDTO = JSON.parseObject(s, MaterialDtlDTO.class);
            return materialDtlDTO;
        } else {
            String materialNull = stringRedisTemplate.opsForValue().get(MaterialLockUtils.MATERIAL_NULL);
            if (materialNull == null) {
                String basicsMaterialUrl = mallConfig.prodPcwp2Url02 + BASICS_MATERIAL_LIST;
                HashMap<Object, Object> paramsMap = new HashMap<>();
                paramsMap.put("versionId","1942518269757186050");
                paramsMap.put("pageIndex", 1);
                paramsMap.put("pageSize", 10);
                if (basicsMaterialName != null && basicsMaterialName != "") {
                    paramsMap.put("materialName", basicsMaterialName);
                }
                if (billNo != null) {
                    paramsMap.put("materialNo", billNo);
                }
                if (classId != null) {
                    paramsMap.put("classId", classId);
                }
                PageUtils<Map> r = null;
                try {
                    r = restTemplateUtils.postPCWP2PageR(basicsMaterialUrl, paramsMap);
                } catch (Exception e) {
                    throw new BusinessException("【远程异常】获取基础库物资错误：" + e.getMessage());
                }
                List<Map> list = r.getList();

                if (list == null && list.size() == 0) {
                    //如果物资基础库不存在，设置缓存编号为key,缓存1分钟时间，避免重复请求
                    stringRedisTemplate.opsForValue().set(MaterialLockUtils.MATERIAL_NULL + billNo, "-101", 2, MINUTES);
                    throw new BusinessException("物资基础库不存在，请重新选择物资");
                } else {
                    for (Map map : list) {
                        int enable = (int) map.get("isEnable");
                        String MaterialClassId = ((String) map.get("classId"));
                        String materialName = ((String) map.get("materialName"));
                        if (enable == 0) {
                            stringRedisTemplate.opsForValue().set(MaterialLockUtils.MATERIAL_NULL + billNo, "-100", 1, MINUTES);
                            throw new BusinessException("物资编号：" + billNo + ";" + "物资名称" + materialName + "已停用】，请修改联系管理员修改商品物资基础库或重新选择物资");
                        } else {
                            stringRedisTemplate.opsForValue().set(MaterialLockUtils.CLASS_ID_AND_RELEVANCE_N0_LOCK_KEY +
                                    MaterialClassId + ":" + billNo, JSONObject.toJSONString(map), 130, MINUTES);
                            Integer classQueryCount = productCategoryService.lambdaQuery().eq(ProductCategory::getClassPath, map.get("classNamePath")).count();
                            if (classQueryCount == 0) {
                                throw new BusinessException(500085, "和pcwp分类不统一！");
                            }

                        }
                        return MaterialDtlDTO.fromMap(map);
                    }
                    return MaterialDtlDTO.fromMap(list.get(0));
                }
            } else {
                throw new BusinessException("物资基础库不存在或者物资已经停用，请重新选择物资");
            }
        }
    }


    @Override
    public void setMaterialDtlDTOList(List<MaterialDtlDTO> materialDtlDTOList) {
        if (CollectionUtils.isEmpty(materialDtlDTOList)) {
            return;
        } else {
            materialDtlDTOList.stream().forEach(materialDtlDTO -> {
                String materialNo = materialDtlDTO.getBillNo();
                String classId = materialDtlDTO.getClassId();
                String key = classId + ":" + materialNo;
                String jsonString = JSON.toJSONString(materialDtlDTO);
                stringRedisTemplate.opsForValue().set(MaterialLockUtils.CLASS_ID_AND_RELEVANCE_N0_LOCK_KEY + key, jsonString, 30, MINUTES);
            });
        }

    }

    @Override
    @Transactional
    public List<ImportExcelResultVO> shopUploadProductMallZoneExcelFile(MultipartFile file, String shopId) {
        ArrayList<ImportExcelResultVO> vos = new ArrayList<>();
        try {
            List<ZoneMaterial> objects = EasyExcelUtils.readExcelOneSheet(file.getInputStream(), ZoneMaterial.class);
            if (objects.size() > 4000) {
                throw new BusinessException(500, "数量超过4000禁止导入！");
            }
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            //查询单个商品不同地区的销售价格
            Map<String, List<ZoneMaterial>> productSnZoneMap = objects.stream().collect(Collectors.groupingBy(ZoneMaterial::getProductSn));
            //修改商品数据（审核通过的商品应该重新下架）
            productSnZoneMap.forEach((key, zoneList) -> {
                ImportExcelResultVO vo = new ImportExcelResultVO();
                try {
                    Product one = lambdaQuery().eq(Product::getSerialNum, key).one();
                    if (one != null) {
                        ProductSku productSku = productSkuService.getProductSkuByProductId(one.getProductId(), null).get(0);
                        BigDecimal costPrice = productSku.getCostPrice();

                        for (ZoneMaterial zoneMaterial : zoneList) {
                            ImportExcelResultVO vo2 = new ImportExcelResultVO();
                            try {
                                //销售区域价格不能小于0
                                if (zoneMaterial.getZonePrice().compareTo(BigDecimal.ZERO) < 0) {
                                    throw new BusinessException("销售区域价格不能小于0");
                                }
                                //销售价格不能大于原价
                                if (zoneMaterial.getZonePrice().compareTo(productSku.getOriginalPrice()) > 0) {
                                    throw new BusinessException("销售区域价格不能大于原价");
                                }
                                //销售区域价格不能小于成本价
                                if (zoneMaterial.getZonePrice().compareTo(costPrice) < 0) {
                                    throw new BusinessException("销售区域价格不能小于成本价");
                                }
                                //切割省和市  模糊匹配一级和二级商品区域
                                String[] split = zoneMaterial.getZoneAddr().trim().split("/");
                                if (split.length != 2) {

                                    throw new BusinessException("销售区域只能是市");
                                }
                            } catch (BusinessException e) {
                                vo2.setProductSn(one.getSerialNum());
                                vo2.setProductName(one.getProductName());
                                vo2.setZone(zoneMaterial.getZoneAddr().trim());
                                vo2.setState(0);
                                vo2.setFail(e.getMessage());
                                vos.add(vo2);
                            }
                        }

                        //获取商品的销售区域的最小值。设置商品的最小值和规格的销售价格和比价类型
                        BigDecimal minPrice = zoneList.stream()
                                .map(ZoneMaterial::getZonePrice) // 获取所有 zonePrice
                                .min(Comparator.naturalOrder()) // 找到最小的价格
                                .orElse(BigDecimal.ZERO); // 如果列表为空，则返回默认值（这里是 BigDecimal.ZERO）
                        if (shopId == null) {
                            lambdaUpdate().eq(Product::getProductId, one.getProductId()).set(Product::getProductMinPrice, minPrice).update();
                            productSkuService.lambdaUpdate().eq(ProductSku::getSkuId, productSku.getSkuId())
                                    .set(ProductSku::getIsZone, 2)
                                    .set(ProductSku::getSellPrice, minPrice).update();
                        } else {
                            productSkuService.lambdaUpdate().eq(ProductSku::getSkuId, productSku.getSkuId())
                                    .set(ProductSku::getIsZone, 2)
                                    .set(ProductSku::getSSellPrice, minPrice).update();
                        }

                        //如果商品是已上架状态，修改商品状态为待审核状态
                        if (one.getState() == 1) {
                            one.setState(3);
                            baseMapper.updateById(one);
                        }


                    } else {
                        ZoneMaterial zone = zoneList.get(0);
                        vo.setId(zone.getId());
                        vo.setProductSn(zone.getProductSn());
                        vo.setProductSn(one.getSerialNum());
                        vo.setProductName(one.getProductName());
                        throw new BusinessException("商品编号" + zone.getProductSn() + "的商品不存在，请注意商品编号");

                    }
                } catch (Exception e) {
                    vo.setState(0);
                    vo.setFail(e.getMessage());
                    vos.add(vo);
                }
            });
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
        }
        return vos;
    }

    @Override
    public Product getByIdLocal(String productId) {
        return baseMapper.getByIdLocal(productId);
    }


    @Override
    public Product validateProduct(String productId, Object state) {
        Product product = getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
        if (product == null) {
            Product byId = productService.getProductExcludeRemarkById(productId);
            if (byId != null) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
            }
            throw new BusinessException(OrderEnum.RESULT_CODE_500201.getCode(), "商品不存在，计划失败！");
        }
        return product;
    }
    /**
     * 统计列表（平台商品统计）
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils getProductCountList(JSONObject jsonObject, LambdaQueryWrapper<Product> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        Integer isShop = (Integer) innerMap.get("isShop");
        String keywords = (String) innerMap.get("keywords");
        Integer productType = (Integer) innerMap.get("productType");
        q.eq(Product::getMallType, mallConfig.mallType);
        q.between(StringUtils.isNotEmpty(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), Product::getGmtCreate, startCreateDate, endCreateDate);
        q.select(Product.class, f -> {
            return !f.getProperty().equals("productDescribe") && !f.getProperty().equals("remarks");
        });
        if(productType != null){
            q.eq(Product::getProductType, productType);
        }
        q.eq(isShop == null || isShop == 0, Product::getState, 1);
        String shopId = (String) innerMap.get("shopId");
        if (StringUtils.isNotEmpty(shopId)) {
            q.eq(isShop != null && isShop == 1, Product::getShopId, shopId);
        } else {
            q.eq(isShop != null && isShop == 1, Product::getShopId, ThreadLocalUtil.getCurrentUser().getShopId());
        }
        /*if (mallConfig.mallType == 0) {
            q.eq(Product::getProductType, 0);
        }*/
        q.eq(Product::getIsDelete, 0);
        // 关键字 查询编码、物资名称、商品名称
        if (StringUtils.isNotEmpty(keywords)) {
            q.and(q1 -> {
                q1.like(Product::getSerialNum, keywords)
                        .or().like(Product::getProductName, keywords)
                .or().like(Product::getRelevanceName, keywords);
            });
        }
        // baseMapper.selectCount(q)
        /*// 商品类型
        if(productType != null){
            q.eq(Product::getProductType, productType);
        }else {
            // 查询每个 productType 的数量
            List<Map<String, Object>> result = productMapper.selectMaps(Wrappers.<Product>lambdaQuery()
                    .eq(Product::getIsDelete, 0)
                    .eq(Product::getMallType, mallConfig.mallType)
                    .groupBy(Product::getProductType)
                    .select(Product::getProductType));

            // 输出结果
            for (Map<String, Object> map : result) {
                Integer type = (Integer) map.get("productType");
                Long count1 = (Long) map.get("count");
                System.out.println("productType: " + type + ", count: " + count1);
            }
        }*/

        IPage<Product> page = this.page(new Query<Product>().getPage(jsonObject), q);
        List<String> labelTitle = Arrays.asList("零星物资", "大宗物资", "周转材料");
        List<Product> records = page.getRecords();
        List<Integer> count = new ArrayList<>(Arrays.asList(0, 0, 0));
        if(productType == null ){
            Integer type0 = productMapper.selectTypeCount(q,0);
            Integer type1 = productMapper.selectTypeCount(q,1);
            Integer type2 = productMapper.selectTypeCount(q,2);
             count = Arrays.asList(type0,type1,type2);
        } else {
            Integer type = productMapper.selectTypeCount(q, productType);
            if (productType >= 0 && productType <= 2) {
                count.set(productType, type);
            }
        }
        if (!CollectionUtils.isEmpty(records)){
            records.get(0).setLabelTitle(labelTitle);
            records.get(0).setCount(count);
        }
        if (!CollectionUtils.isEmpty(records)) {
            for (Product record : records) {
                List<ProductSku> sku = productSkuService.getProductSkuByProductId(record.getProductId(), null);
                if (sku != null && sku.size() > 0) {
                    record.setOriginalPrice(sku.get(0).getOriginalPrice());
                    record.setSellPrice(sku.get(0).getSellPrice());
                    record.setCostPrice(sku.get(0).getCostPrice());
                    if (sku.get(0).getSellPrice() != null && sku.get(0).getCostPrice() != null) {
                        record.setProfitPrice(sku.get(0).getSellPrice().subtract(sku.get(0).getCostPrice()));
                    }
                }
                if (StringUtils.isNotBlank(record.getShopId())) {
                    Shop shop = shopService.getById(record.getShopId());
                    if (shop != null) {
                        record.setShopName(shop.getShopName());
                    }
                }


            }
        }
            // 如果是物资
           /* if (mallConfig.mallType == 0) {
                // 查询1级分类
                LambdaQueryChainWrapper<ProductCategory> ql = productCategoryService.lambdaQuery().eq(ProductCategory::getProductType, 0).eq(ProductCategory::getParentId, 0).eq(ProductCategory::getMallType, mallConfig.mallType).eq(ProductCategory::getClassLevel, 1).select(ProductCategory::getClassId, ProductCategory::getClassName);
                if (isShop != null && isShop == 1) {
                    // 是店铺只查询状态启用的
                    ql.eq(ProductCategory::getState, 1);
                }
                List<ProductCategory> list = ql.list();
                if (CollectionUtils.isEmpty(list)) {
                    records.get(0).setLabelTitle(labelTitle);
                    records.get(0).setCount(count);
                } else {
                    for (ProductCategory productCategory : list) {
                        Integer count1 = lambdaQuery().eq(Product::getMallType, mallConfig.mallType).between(StringUtils.isNotEmpty(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), Product::getGmtCreate, startCreateDate, endCreateDate).eq(isShop != null && isShop == 1, Product::getShopId, ThreadLocalUtil.getCurrentUser().getShopId()).eq(Product::getProductType, 0).eq(isShop == null || isShop == 0, Product::getState, 1).like(Product::getClassPath, productCategory.getClassId()).count();
                        labelTitle.add(productCategory.getClassName());
                        count.add(count1);
                    }
                }
            }
            records.get(0).setLabelTitle(labelTitle);
            records.get(0).setCount(count);
        }*/
        return new PageUtils(page);
    }

    @Override
    public void getProductCountListExcel(JSONObject jsonObject, HttpServletResponse response) {
        LambdaQueryWrapper<Product> q = new LambdaQueryWrapper<>();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        Integer isShop = (Integer) innerMap.get("isShop");
        String keywords = (String) innerMap.get("keywords");
        Integer productType = (Integer) innerMap.get("productType");
        q.eq(Product::getMallType, mallConfig.mallType);
        q.between(StringUtils.isNotEmpty(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), Product::getGmtCreate, startCreateDate, endCreateDate);
        q.select(Product.class, f -> {
            return !f.getProperty().equals("productDescribe") && !f.getProperty().equals("remarks");
        });
        if(productType != null){
            q.eq(Product::getProductType, productType);
        }
        q.eq(isShop == null || isShop == 0, Product::getState, 1);
        String shopId = (String) innerMap.get("shopId");
        if (StringUtils.isNotEmpty(shopId)) {
            q.eq(isShop != null && isShop == 1, Product::getShopId, shopId);
        } else {
            q.eq(isShop != null && isShop == 1, Product::getShopId, ThreadLocalUtil.getCurrentUser().getShopId());
        }
        /*if (mallConfig.mallType == 0) {
            q.eq(Product::getProductType, 0);
        }*/
        q.eq(Product::getIsDelete, 0);
        // 关键字 查询编码、物资名称、商品名称
        if (StringUtils.isNotEmpty(keywords)) {
            q.and(q1 -> {
                q1.like(Product::getSerialNum, keywords)
                        .or().like(Product::getProductName, keywords)
                        .or().like(Product::getRelevanceName, keywords);
            });
        }
        // baseMapper.selectCount(q)
        /*// 商品类型
        if(productType != null){
            q.eq(Product::getProductType, productType);
        }else {
            // 查询每个 productType 的数量
            List<Map<String, Object>> result = productMapper.selectMaps(Wrappers.<Product>lambdaQuery()
                    .eq(Product::getIsDelete, 0)
                    .eq(Product::getMallType, mallConfig.mallType)
                    .groupBy(Product::getProductType)
                    .select(Product::getProductType));

            // 输出结果
            for (Map<String, Object> map : result) {
                Integer type = (Integer) map.get("productType");
                Long count1 = (Long) map.get("count");
                System.out.println("productType: " + type + ", count: " + count1);
            }
        }*/

        //IPage<Product> page = this.page(new Query<Product>().getPage(jsonObject), q);
        List<Product> records = baseMapper.selectList(q);
        List<String> labelTitle = Arrays.asList("零星物资", "大宗物资", "周转材料");
        //List<Product> records = page.getRecords();
        List<Integer> count = new ArrayList<>(Arrays.asList(0, 0, 0));
        if(productType == null ){
            Integer type0 = productMapper.selectTypeCount(q,0);
            Integer type1 = productMapper.selectTypeCount(q,1);
            Integer type2 = productMapper.selectTypeCount(q,2);
            count = Arrays.asList(type0,type1,type2);
        } else {
            Integer type = productMapper.selectTypeCount(q, productType);
            if (productType >= 0 && productType <= 2) {
                count.set(productType, type);
            }
        }
        if (!CollectionUtils.isEmpty(records)){
            records.get(0).setLabelTitle(labelTitle);
            records.get(0).setCount(count);
        }
        List<ProductOutPutVO> productOutPutVOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(records)) {
            for (Product record : records) {
                List<ProductSku> sku = productSkuService.getProductSkuByProductId(record.getProductId(), null);
                if (sku != null && sku.size() > 0) {
                    record.setOriginalPrice(sku.get(0).getOriginalPrice());
                    record.setSellPrice(sku.get(0).getSellPrice());
                    record.setCostPrice(sku.get(0).getCostPrice());
                    if (sku.get(0).getSellPrice() != null && sku.get(0).getCostPrice() != null) {
                        record.setProfitPrice(sku.get(0).getSellPrice().subtract(sku.get(0).getCostPrice()));
                    }
                }
                if (StringUtils.isNotBlank(record.getShopId())) {
                    Shop shop = shopService.getById(record.getShopId());
                    if (shop != null) {
                        record.setShopName(shop.getShopName());
                    }
                }
                // 格式转换
                ProductOutPutVO outPutVO = new ProductOutPutVO();
                outPutVO.setSerialNum(record.getSerialNum());
                outPutVO.setProductType(getProductTypeName(record.getProductType()));
                outPutVO.setRelevanceName(record.getRelevanceName());
                outPutVO.setProductName(record.getProductName());
                outPutVO.setSupplierName(record.getSupplierName());
                outPutVO.setNoRateOriginalPrice(getNoRate(record.getCostPrice(),record.getTaxRate()));
                outPutVO.setNoRateCostPrice(getNoRate(record.getCostPrice(),record.getTaxRate()));
                outPutVO.setNoRateProductMinPrice(getNoRate(record.getProductMinPrice(),record.getTaxRate()));
                outPutVO.setOriginalPrice(record.getCostPrice());
                outPutVO.setCostPrice(record.getCostPrice());
                outPutVO.setProductMinPrice(record.getProductMinPrice());
                outPutVO.setGmtCreate(getDateStr(record.getGmtCreate()));
                outPutVO.setPutawayDate(getDateStr(record.getPutawayDate()));
                productOutPutVOS.add(outPutVO);
            }
        }
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", productOutPutVOS);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "平台商品统计分析模板.xlsx", src, "平台商品统计分析.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }
    private String getProductTypeName(Integer productType) {
        if (productType == null) {
            return "未知类型";
        }
        switch (productType) {
            case 0:
                return "零星采购";
            case 1:
                return "大宗临购";
            case 2:
                return "周材材料";
            default:
                return "未知类型";
        }
    }
    private String getDateStr(Date date) {
        if(date == null){
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
    private String getNoRate(BigDecimal amount,BigDecimal taxRate) {
        if (amount == null || taxRate == null){
            return null;
        }else {
            // 将税率百分比转换为小数形式（3 -> 0.03）
            BigDecimal taxRateDecimal = taxRate.divide(BigDecimal.valueOf(100), 6, BigDecimal.ROUND_HALF_UP);
            // 计算不含税金额：含税金额 / (1 + 税率)
            BigDecimal divisor = BigDecimal.ONE.add(taxRateDecimal);
            BigDecimal noRateAmount = amount.divide(divisor, 2, BigDecimal.ROUND_HALF_UP);
            return String.valueOf(noRateAmount);
        }
    }
    /**
     * 计算商品均价定时任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void calculateProductAveragePrice() {
        try {
            List<Product> list = baseMapper.calculateProductAveragePrice();
            if (!list.isEmpty()) {
                for (Product product : list) {
                    LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
                    if (product.getBrandId() != null) {
                        updateWrapper.eq(Product::getBrandId, product.getBrandId());
                    } else {
                        updateWrapper.isNull(Product::getBrandId);
                    }
                    if (product.getSkuName() != null) {
                        updateWrapper.eq(Product::getSkuName, product.getSkuName());
                    } else {
                        updateWrapper.isNull(Product::getSkuName);
                    }
                    if (product.getRelevanceName() != null) {
                        updateWrapper.eq(Product::getRelevanceName, product.getRelevanceName());
                    } else {
                        updateWrapper.isNull(Product::getRelevanceName);
                    }
                    if (product.getClassPath() != null) {
                        updateWrapper.eq(Product::getClassPath, product.getClassPath());
                    } else {
                        updateWrapper.isNull(Product::getClassPath);
                    }
                    productService.update(updateWrapper.set(Product::getProductAvePrice, product.getProductAvePrice()));
                }
            }
        } catch (Exception e) {
            log.error("计算商品均价定时任务执行异常", e);
            throw e;
        }
    }

    @Override
    public void updateProductJcState(Product product) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String userName = user.getUserName();
        String userId = user.getUserId();
        if(product.getJcState() == 1 || product.getJcState() == 2){
            product.setJcFzrId(userId);
            product.setJcFzrName(userName);
            product.setJcFzrTime(new DateTime());
        }
        if(product.getJcState() == 3 || product.getJcState() == 4){
            product.setJcLdId(userId);
            product.setJcLdName(userName);
            product.setJcLdTime(new DateTime());
        }
        updateById(product);
    }
    @Override
    public PageUtils<Product> getStockUpInfo(JSONObject jsonObject) {
        String shopId = (String) jsonObject.get("shopId");
        String productName = (String) jsonObject.get("productName");
        String serialNum = (String) jsonObject.get("serialNum");
        String classId = (String) jsonObject.get("classId");
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<Product>();
        List<String> shopIds = new ArrayList<>();
        if (StringUtils.isBlank(shopId)) {
            shopIds.addAll(shopService.findTwoSupplierShopId());
        }else{
            shopIds.add(shopId);
        }
        if(!shopIds.isEmpty()){
            queryWrapper.in(Product::getShopId, shopIds);
        }
        queryWrapper.eq(Product::getState, 1);
        if (StringUtils.isNotBlank(productName)) {
            queryWrapper.like(Product::getProductName, productName);
        }
        if (StringUtils.isNotBlank(serialNum)) {
            queryWrapper.like(Product::getSerialNum, serialNum);
        }
        if (StringUtils.isNotBlank(classId)) {
            queryWrapper.eq(Product::getClassId, classId);
        }
        IPage<Product> page = this.page(new Query<Product>().getPage(jsonObject), queryWrapper);
        List<Product> records = page.getRecords();
        if (null != records && !records.isEmpty()) {
            for (Product product : records) {
                ProductCategory s = JSON.parseObject(stringRedisTemplate.opsForValue().get("CLASSID:" + product.getClassId()), ProductCategory.class);
                if (s != null) {
                    product.setClassPathName(s == null ? "" : s.getClassPath());
                } else {
                    ProductCategory byId = productCategoryService.lambdaQuery()
                            .eq(ProductCategory::getClassId, product.getClassId())
                            .select(ProductCategory::getClassPath).one();
                    product.setClassPathName(byId == null ? "" : byId.getClassPath());
                    stringRedisTemplate.opsForValue().set("CLASSID:" + product.getClassId(), JSON.toJSONString(byId));
                }
                List<ProductSku> skuList = productSkuService.lambdaQuery()
                        .eq(ProductSku::getProductId, product.getProductId())
                        .select(ProductSku::getStock, ProductSku::getCostPrice,
                                ProductSku::getOriginalPrice, ProductSku::getSellPrice).list();
                if (skuList != null && !skuList.isEmpty()) {
                    ProductSku sku = skuList.get(0);
                    product.setStock(sku.getStock());
                    product.setCostPrice(sku.getCostPrice());
                    product.setOriginalPrice(sku.getOriginalPrice());
                    product.setSellPrice(sku.getSellPrice());
                    product.setProfitPrice(sku.getSellPrice().subtract(sku.getCostPrice()));
                }
            }
        }

        return new PageUtils<>(page);
    }

    @Override
    public void oneClickStockUp(List<String> ids) {
        if(null == ids || ids.isEmpty()){
            throw new BusinessException("未选择需要铺货的商品");
        }
        LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(Product::getProductId, ids)
                .set(Product::getState, 0)
                .set(Product::getShopId, ThreadLocalUtil.getCurrentUser().getShopId())
                .set(Product::getSupperBy,ThreadLocalUtil.getCurrentUser().getEnterpriseId())
                .set(Product::getProductType,0)
                .set(Product::getGmtModified,new Date())
                .set(Product::getSupplierName, ThreadLocalUtil.getCurrentUser().getEnterpriseName());
        update(updateWrapper);

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateProductStock(Product product) {
        String productId = product.getProductId();
        BigDecimal newStock = product.getStock();
        Integer state = product.getState();

        if (productId == null || newStock == null || state == null) {
            throw new BusinessException("产品ID、状态或库存不能为空");
        }
        // 查询所有关联 SKU
        List<ProductSku> skuList = productSkuService.getProductSkuByProductId(productId, state);
        if (skuList.isEmpty()) {
            return;
        }
        // 批量更新库存
        skuList.forEach(sku -> sku.setStock(newStock));
        productSkuService.updateBatchById(skuList);
    }
    @Override
    public PageUtils statisticsByShopAndProductTypeWithSupplierPage(JSONObject jsonObject) {
        Map<String, Object> params = jsonObject.getInnerMap();
        Integer productType = (Integer) params.get("productType");
        IPage<ProductStatisticsVO> page = new Query<ProductStatisticsVO>().getPage(jsonObject);
        ArrayList<ProductStatisticsVO> result = productMapper.statisticsByShopAndProductTypeWithSupplier(page, params);
        page.setRecords(result);
        List<String> labelTitle = Arrays.asList("零星采购","大宗临购","周转材料");
        List<Integer> count = new ArrayList<>(Arrays.asList(0,0,0));
        if(productType == null){
            Integer type0 = productMapper.selectStatisticsTypeVoCount(params,0);
            Integer type1 = productMapper.selectStatisticsTypeVoCount(params,1);
            Integer type2 = productMapper.selectStatisticsTypeVoCount(params,2);
            count = Arrays.asList(type0,type1,type2);
        } else {
            Integer type = productMapper.selectStatisticsTypeVoCount(params,productType);
            if(productType >= 0 && productType <= 2){
                count.set(productType,type);
            }
        }
        if(!CollectionUtils.isEmpty(result)){
            result.get(0).setLabelTitle(labelTitle);
            result.get(0).setCount(count);
        }
        return new PageUtils<>(page);
    }
}


