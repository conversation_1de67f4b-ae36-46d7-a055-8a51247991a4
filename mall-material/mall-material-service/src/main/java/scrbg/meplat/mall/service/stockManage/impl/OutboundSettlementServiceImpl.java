package scrbg.meplat.mall.service.stockManage.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.common.constant.ProcessConstants;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.controller.stockManage.vo.OutboundSettlementVO;
import scrbg.meplat.mall.entity.OutboundSettlementManage;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.stockManage.InboundSettlementManageMapper;
import scrbg.meplat.mall.mapper.stockManage.OutboundSettlementManageMapper;
import scrbg.meplat.mall.service.ProcessConfigService;
import scrbg.meplat.mall.service.stockManage.OutboundSettlementService;
import scrbg.meplat.mall.util.DateUtil;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class OutboundSettlementServiceImpl extends ServiceImpl<OutboundSettlementManageMapper, OutboundSettlementManage> implements OutboundSettlementService {

    public static final String DATEFORMAT = "yyyyMM";

    private OutboundSettlementManageMapper outboundSettlementManageMapper;

    private InboundSettlementManageMapper inboundSettlementManageMapper;

    public MallConfig mallConfig;

    private ProcessConfigService processConfigService;

    @Autowired
    public void setOutboundSettlementManageMapper(OutboundSettlementManageMapper outboundSettlementManageMapper) {
        this.outboundSettlementManageMapper = outboundSettlementManageMapper;
    }

    @Autowired
    public void setInboundSettlementManageMapper(InboundSettlementManageMapper inboundSettlementManageMapper) {
        this.inboundSettlementManageMapper = inboundSettlementManageMapper;
    }

    @Autowired
    public void setMallConfig(MallConfig mallConfig) {
        this.mallConfig = mallConfig;
    }

    @Autowired
    public void setProcessConfigService(ProcessConfigService processConfigService) {
        this.processConfigService = processConfigService;
    }
    @Override
    public void saveSettlement(OutboundSettlementManage outboundSettlement) {
        outboundSettlement.setAuditStatus(0);
        outboundSettlement.setPeriodsNum(generateAccountPeriod());
        outboundSettlement.setReceiveName(ThreadLocalUtil.getCurrentUser().getUserName());
        outboundSettlement.setReceivePhone(ThreadLocalUtil.getCurrentUser().getUserMobile());
        outboundSettlement.setGmtCreate(new Date());
        save(outboundSettlement);
    }

    @Override
    public void updateSettlement(OutboundSettlementManage outboundSettlement) {
        updateById(outboundSettlement);
    }

    @Override
    public void saveAndSubmitSettlement(OutboundSettlementManage outboundSettlement) {
        outboundSettlement.setReceiveName(ThreadLocalUtil.getCurrentUser().getUserName());
        outboundSettlement.setReceivePhone(ThreadLocalUtil.getCurrentUser().getUserMobile());
        outboundSettlement.setGmtCreate(new Date());
        outboundSettlement.setPeriodsNum(generateAccountPeriod());
        outboundSettlement.setWarehouseId("1");
        outboundSettlement.setAuditStatus(1);
        save(outboundSettlement);
        processConfigService.myFunc(ProcessConstants.OUTBOUND_SETTLEMENT,
                ThreadLocalUtil.getCurrentUser(), 0, outboundSettlement.getId(), "");
    }

    @Override
    public void updateAndSubmitSettlement(OutboundSettlementManage outboundSettlementManage) {
        updateById(outboundSettlementManage);
        processConfigService.myFunc(ProcessConstants.OUTBOUND_SETTLEMENT,
                ThreadLocalUtil.getCurrentUser(), 0, outboundSettlementManage.getId(), "");
    }

    @Override
    public PageUtils<OutboundSettlementManage> queryPage(JSONObject jsonObject, LambdaQueryWrapper<OutboundSettlementManage> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String contractNo = (String) innerMap.get("contractNo");
        String purchasingOrgName = (String) innerMap.get("purchasingOrgName");
        Integer outboundType = (Integer) innerMap.get("outboundType");
        Integer auditStatus = (Integer) innerMap.get("auditStatus");
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(OutboundSettlementManage::getContractNo, keywords)
                        .or()
                        .like(OutboundSettlementManage::getPurchasingOrgName, keywords);

            });
        }
        if (!StringUtils.isEmpty(contractNo)) {
            queryWrapper.like(OutboundSettlementManage::getContractNo, contractNo);
        }
        if (null != outboundType) {
            queryWrapper.eq(OutboundSettlementManage::getOutboundType, outboundType);
        }
        if (null != auditStatus) {
            queryWrapper.eq(OutboundSettlementManage::getAuditStatus, auditStatus);
        }
        if (!StringUtils.isEmpty(purchasingOrgName)) {
            queryWrapper.like(OutboundSettlementManage::getPurchasingOrgName, purchasingOrgName);
        }
        queryWrapper.between(StringUtils.isNotBlank(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), OutboundSettlementManage::getGmtCreate, startCreateDate, endCreateDate);
        IPage<OutboundSettlementManage> page = this.page(
                new Query<OutboundSettlementManage>().getPage(jsonObject),
                queryWrapper
        );
        page.getRecords().forEach(item -> {
            item.setPeriodsNum(convertPeriod(item.getPeriodsNum()));
        });
        return new PageUtils<OutboundSettlementManage>(page);
    }

    @Override
    public void export(String reconciliationId, HttpServletResponse response) {
        OutboundSettlementManage manage = baseMapper.selectById(reconciliationId);
        try {
            Map<String, Object> dataMap = new HashMap<>();
            String src = mallConfig.templateFormUrl;
            dataMap.put("receiveOrgName", manage.getPurchasingOrgName());
            dataMap.put("supplierName", manage.getSupplierName());
            dataMap.put("gmtCreate", DateUtil.getyyymmddHHmmss(manage.getGmtCreate()));
            dataMap.put("invoiceNum", manage.getInvoiceNum());
            dataMap.put("accountPeriod", convertPeriod(manage.getPeriodsNum()));
            dataMap.put("contractNo", manage.getContractNo());
            dataMap.put("applyInvoiceTime", DateUtil.getyyymmddHHmmss(manage.getApplyInvoiceTime()));
            dataMap.put("ticketReceivingUnit", manage.getTicketReceivingUnit());
            dataMap.put("ticketReceivingUnitTaxNo", manage.getTicketReceivingUnitTaxNo());
            dataMap.put("ticketReceivingUnitAddress", manage.getTicketReceivingUnitAddress());
            dataMap.put("ticketReceivingUnitPhone", manage.getTicketReceivingUnitPhone());
            dataMap.put("ticketReceivingUnitBank", manage.getTicketReceivingUnitBank());
            dataMap.put("ticketReceivingUnitAccount", manage.getTicketReceivingUnitAccount());
            dataMap.put("projectName", manage.getProjectName());
            dataMap.put("projectAddress", manage.getProjectAddress());
            dataMap.put("supplierInvoiceStatus", manage.getSupplierInvoiceStatus() == 0 ? "否" : "是");
            if (StringUtils.isNotBlank(manage.getSettlementInfo())) {
                List<OutboundSettlementVO> list = JSON.parseArray(manage.getSettlementInfo(), OutboundSettlementVO.class);
                dataMap.put("sumQuantity", list.stream()
                        .map(OutboundSettlementVO::getQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                dataMap.put("sumCgTotalAmount", list.stream()
                        .map(OutboundSettlementVO::getCgTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                dataMap.put("sumCgNoRateAmount", list.stream()
                        .map(OutboundSettlementVO::getCgNoRateAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                dataMap.put("sumXsTotalAmount", list.stream()
                        .map(OutboundSettlementVO::getXsTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                dataMap.put("sumXsNoRateAmount", list.stream()
                        .map(OutboundSettlementVO::getXsTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                dataMap.put("dataList", list);
            }
            ExcelForWebUtil.exportExcel(response, dataMap, "物资出库结算单模板.xlsx", src, "物资出库结算单.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }

    private synchronized String generateAccountPeriod() {
        String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern(DATEFORMAT));
        AtomicInteger finalSerialNum = new AtomicInteger(1);
        Integer serialNumber = inboundSettlementManageMapper.selectMaxSerialNumber(currentDate, 2);
        if (null == serialNumber) {
            inboundSettlementManageMapper.insertSerial(currentDate,
                    finalSerialNum.get(), 2);
        } else {
            finalSerialNum.set(new AtomicInteger(serialNumber).incrementAndGet());
            inboundSettlementManageMapper.updateSerial(currentDate,
                    finalSerialNum.get(), 2);
        }
        return currentDate + finalSerialNum.get();
    }

    public static String convertPeriod(String period) {
        if (StringUtils.isBlank(period)) {
            return "";
        }
        // 使用正则表达式匹配格式
        if (period.matches("(\\d{4})(\\d{2})(\\d+)")) {
            String yearMonth = period.substring(0, 6);
            String issue = period.substring(6);

            return yearMonth + " 月 第" + issue + "期";
        }

        return "";
    }

    @Override
    public void updateState(String id, int state) {
        if(processConfigService.processIsFinished(id) || state == 3){
            OutboundSettlementManage manage = new OutboundSettlementManage();
            manage.setId(id);
            manage.setAuditStatus(state);
            updateById(manage);
        }
        processConfigService.myFunc(ProcessConstants.OUTBOUND_SETTLEMENT,
                ThreadLocalUtil.getCurrentUser(), --state, id,
                1 == state ? "【提交】":  2 == state ? "【同意】": "【驳回】");
    }

    @Override
    public void submitSettlement(String id) {
        this.updateState(id, 1);
    }
}
