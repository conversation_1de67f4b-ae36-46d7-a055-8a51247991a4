package scrbg.meplat.mall.controller.system;

import com.alibaba.fastjson.JSON;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.dto.system.SysRole2DTO;
import scrbg.meplat.mall.entity.system.SysJg;
import scrbg.meplat.mall.entity.system.SysRole2;
import scrbg.meplat.mall.service.system.Role2Service;

import java.util.List;

@RestController
@RequestMapping("/bgmanage/system/role")
public class SystemRoleController {
    @Autowired
    private Role2Service roleService;

    @GetMapping("/jglist")
    public List<SysJg> getJglistNames() {
        return roleService.getAllJglist();
    }

    // 分页查询角色列表
    @PostMapping("/list")
    public R<PageUtils<SysRole2>> getRoleList(@RequestBody SysRole2DTO role) {
        return R.success(roleService.getRoleList(role));
    }

    // 分页查询角色列表
    @PostMapping("/getAlllist")
    public R<List<SysRole2>> getRoleListAll(@RequestBody SysRole2DTO role) {
        return R.success(roleService.getRoleListAll(role));
    }

    // 新增角色
    @PostMapping("/add")
    public R<Boolean> addRole(@RequestBody SysRole2DTO role) {
        return roleService.addRole(role) ? R.success() : R.failed("新增角色失败");
    }

    // 编辑角色
    @PostMapping("/update")
    public R<Boolean> updateRole(@RequestBody SysRole2DTO role) {
        return roleService.updateRole(role) ? R.success() : R.failed("更新角色失败");
    }

    // 删除角色
    @PostMapping("/delete/{roleId}")
    public R<Boolean> deleteRole(@PathVariable String roleId) {
        return roleService.deleteRole(roleId) ? R.success() : R.failed("删除角色失败");
    }

    // 获取角色详情
    @GetMapping("/detail/{roleId}")
    public R<SysRole2> getRoleDetail(@PathVariable String roleId) {
        return R.success(roleService.getRoleDetail(roleId));
    }

    // 设置角色菜单权限
    @PostMapping("/setRoleMenus")
    public R<Boolean> setRoleMenus(@RequestParam String roleId,
                          @RequestParam String menuIds) {
        List<String> menuIdList = JSON.parseArray(menuIds, String.class);
        return roleService.setRoleMenus(roleId, menuIdList) ? R.success() : R.failed("设置菜单权限失败");
    }
}
