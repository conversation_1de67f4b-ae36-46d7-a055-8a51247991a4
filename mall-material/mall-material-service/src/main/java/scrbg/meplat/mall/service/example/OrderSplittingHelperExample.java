package scrbg.meplat.mall.service.example;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.service.OrdersService;

/**
 * 订单拆分辅助方法使用示例
 * 展示如何在OrdersServiceImpl中使用新增的私有辅助方法
 */
@Component
public class OrderSplittingHelperExample {

    @Autowired
    private OrdersService ordersService;

    /**
     * 示例1：订单拆分辅助方法的使用场景
     * 注意：processOrderSplittingHelper是OrdersServiceImpl中的私有方法
     */
    public void exampleProcessOrderSplittingHelper() {
        // 假设已有主订单和订单明细
        Orders mainOrder = getMainOrder(); // 获取主订单
        List<OrderItem> orderItems = getOrderItems(); // 获取订单明细列表
        BigDecimal mainTaxRate = mainOrder.getTaxRate(); // 获取主订单税率

        System.out.println("=== 订单拆分辅助方法使用示例 ===");
        System.out.println("主订单号: " + mainOrder.getOrderSn());
        System.out.println("订单类别: " + mainOrder.getOrderClass() + " (2=多供方订单)");
        System.out.println("该方法会按供应商分组，创建子订单并插入orders表，同时更新订单明细并插入order_item表");
        
        // 实际调用示例（在OrdersServiceImpl内部）：
        // processOrderSplittingHelper(mainOrder, orderItems, mainTaxRate);
        
        System.out.println("拆分完成后，每个供应商对应一个子订单（orderClass=3）");
    }

    /**
     * 示例2：在创建订单流程中使用拆分方法
     */
    public void exampleUseInOrderCreation() {
        // 模拟订单创建流程
        Orders mainOrder = createMainOrder(); // 创建主订单
        List<OrderItem> orderItems = createOrderItems(mainOrder); // 创建订单明细

        // 判断是否需要拆单（多供方订单）
        if (isMultiSupplierOrder(mainOrder)) {
            System.out.println("=== 检测到多供方订单，开始拆分 ===");
            System.out.println("主订单号: " + mainOrder.getOrderSn());
            System.out.println("订单类别: " + mainOrder.getOrderClass());
            
            // 在实际的OrdersServiceImpl中，会调用私有方法：
            // processOrderSplittingHelper(mainOrder, orderItems, mainOrder.getTaxRate());
            
            System.out.println("拆分完成，已按供应商创建子订单");
        }
    }

    /**
     * 示例3：展示拆分逻辑的工作原理
     */
    public void exampleSplittingLogic() {
        // 模拟数据
        Orders mainOrder = getMainOrder();
        List<OrderItem> orderItems = getOrderItems();
        
        System.out.println("=== 订单拆分逻辑演示 ===");
        System.out.println("主订单信息:");
        System.out.println("  订单号: " + mainOrder.getOrderSn());
        System.out.println("  订单类别: " + mainOrder.getOrderClass() + " (2=多供方订单)");
        
        // 模拟按供应商分组
        Map<String, List<OrderItem>> supplierGroups = orderItems.stream()
                .collect(Collectors.groupingBy(OrderItem::getSupplierId));
        
        System.out.println("按供应商分组结果:");
        for (Map.Entry<String, List<OrderItem>> entry : supplierGroups.entrySet()) {
            System.out.println("  供应商ID: " + entry.getKey());
            System.out.println("  订单项数量: " + entry.getValue().size());
        }
        
        System.out.println("拆分后将创建 " + supplierGroups.size() + " 个子订单");
        System.out.println("每个子订单的orderClass=3，parentOrderId=" + mainOrder.getOrderId());
    }

    /**
     * 示例4：辅助方法的特点说明
     */
    public void exampleHelperMethodFeatures() {
        System.out.println("=== OrdersServiceImpl中新增的辅助方法特点 ===");
        System.out.println("1. processOrderSplittingHelper - 完整的订单拆分流程");
        System.out.println("   - 按供应商分组订单明细");
        System.out.println("   - 为每个供应商创建子订单");
        System.out.println("   - 更新订单明细关联到子订单");
        System.out.println("   - 子订单和子订单项都会保存到数据库");
        
        System.out.println("2. splitSubOrdersHelper - 仅拆分子订单");
        System.out.println("   - 创建子订单但不更新订单明细");
        System.out.println("   - 返回子订单列表");
        
        System.out.println("3. splitSubOrderItemsHelper - 仅拆分子订单项");
        System.out.println("   - 筛选属于指定供应商的订单项");
        System.out.println("   - 更新订单明细关联到子订单");
        
        System.out.println("4. createSubOrderHelper - 创建子订单");
        System.out.println("   - 复制主订单属性");
        System.out.println("   - 设置供应商信息和税率");
        System.out.println("   - 计算子订单金额");
        System.out.println("   - 生成新的订单号");
        
        System.out.println("5. updateOrderItemsForSubOrderHelper - 更新订单明细");
        System.out.println("   - 设置父订单项ID");
        System.out.println("   - 更新价格为成本价");
        System.out.println("   - 重新计算税率相关字段");
    }

    // 模拟方法，实际使用时需要从数据库或其他地方获取
    private Orders getMainOrder() {
        Orders order = new Orders();
        order.setOrderId("main-order-id");
        order.setOrderSn("MO202501010001");
        order.setOrderClass(2); // 多供方订单
        order.setTaxRate(new BigDecimal("0.13"));
        return order;
    }

    private List<OrderItem> getOrderItems() {
        // 返回订单明细列表，模拟多个供应商的商品
        return List.of();
    }

    private Orders createMainOrder() {
        return getMainOrder();
    }

    private List<OrderItem> createOrderItems(Orders mainOrder) {
        return getOrderItems();
    }

    private boolean isMultiSupplierOrder(Orders mainOrder) {
        // 判断是否为多供方订单（orderClass == 2）
        return mainOrder.getOrderClass() != null && mainOrder.getOrderClass() == 2;
    }
}
