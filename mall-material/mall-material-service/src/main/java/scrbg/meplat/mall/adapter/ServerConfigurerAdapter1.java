package scrbg.meplat.mall.adapter;

import com.scrbg.common.utils.UserInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import scrbg.meplat.mall.interceptor.CheckTokenInterceptor1;
import scrbg.meplat.mall.interceptor.CheckTokenInterceptor2;

@Configuration
@ConditionalOnProperty(name = "neo.userManage.enable", havingValue = "true")
public class ServerConfigurerAdapter1 implements WebMvcConfigurer {

    @Autowired
    private CheckTokenInterceptor1 checkTokenInterceptor;

    @Autowired
    private CheckTokenInterceptor2 checkTokenInterceptor1;
    @Bean
    public HandlerInterceptor userInterceptor() {
        return new UserInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //registry.addInterceptor(userInterceptor());
        registry.addInterceptor(checkTokenInterceptor)
                        .addPathPatterns("/**");
//        // 拦截
        registry.addInterceptor(checkTokenInterceptor1)
                .addPathPatterns("/**")
//                .excludePathPatterns("/**")
                // 排除
                .excludePathPatterns("/w/**")
                //.excludePathPatterns("/outer/usedDevice/**")//这是装备业务，可以删除掉
                // 排除文档路径
                .excludePathPatterns("/doc.html","/webjars/**","/swagger-resources","/error","/favicon.ico");
    }
}
