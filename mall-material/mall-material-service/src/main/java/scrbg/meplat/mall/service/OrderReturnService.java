package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.OrderReturn;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.vo.user.userCenter.OrderReturnVo;

import java.math.BigDecimal;

/**
 * @描述： 服务类
 * @作者: sund
 * @日期: 2023-01-31
 */
public interface OrderReturnService extends IService<OrderReturn> {
    //分页查询所有退货订单项
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderReturn> queryWrapper);

    //添加退货订单
    void create(OrderReturn orderReturn);

    void update(OrderReturn orderReturn);

    OrderReturn getById(String id);

    OrderReturn getByIdAndTwo(String id);

    void delete(String id);

    //个人中心分页查询所有退货订单的订单项
    PageUtils getUserOrderReturnPageList(JSONObject jsonObject, QueryWrapper<OrderReturn> wrapper);

   //供应商查询退货单
    PageUtils shopOrderReturnList(JSONObject jsonObject, LambdaQueryWrapper<OrderReturn> orderReturnQueryWrapper);

    /**
     * 修改退货状态
     * @param orderReturn
     */
    void updateState(OrderReturn orderReturn);

    void updateState2(OrderReturn orderReturn);

    void updateTwoOrderItemState(OrderReturn orderReturn);


    void addDate(OrderReturnVo orderReturnVo);

    BigDecimal getOrderItem(String orderItemId);

    BigDecimal getRetuenSumByCount(String orderItemId);

    PageUtils getOrderItemListByOrderReturnId(String orderReturnId);

    /**
     * duo多供方退货记录查询（店铺）
     *
     * @param jsonObject
     * @param orderReturnLambdaQueryWrapper
     * @return
     */

    PageUtils twoListByEntity(JSONObject jsonObject, LambdaQueryWrapper<OrderReturn> orderReturnLambdaQueryWrapper);

    PageUtils getOrderItemTwoListByOrderReturnId(String orderReturnId);

    void createText(OrderReturn orderReturn);

    void createDataByOrders(OrderReturn orderReturn, Orders orders);


//    void deleteByLikeNo(String keyId);


//    PageUtils getReturnOrderList(JSONObject jsonObject, QueryWrapper<OrderReturnVo> orderReturnVoQueryWrapper);
}
