package scrbg.meplat.mall.service.order.example;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.service.order.OrderService;

/**
 * 订单拆分使用示例
 * 展示如何使用新增的拆分子订单和拆分子订单项方法
 */
@Component
public class OrderSplittingExample {

    @Autowired
    private OrderService orderService;

    /**
     * 示例1：拆分子订单
     * 根据主订单和订单明细，按供应商拆分成多个子订单
     */
    public void exampleSplitSubOrders() {
        // 假设已有主订单和订单明细
        Orders mainOrder = getMainOrder(); // 获取主订单
        List<OrderItem> orderItems = getOrderItems(); // 获取订单明细列表

        // 调用拆分子订单方法
        List<Orders> subOrders = orderService.splitSubOrders(mainOrder, orderItems);

        // 处理拆分后的子订单
        for (Orders subOrder : subOrders) {
            System.out.println("子订单ID: " + subOrder.getOrderId());
            System.out.println("供应商: " + subOrder.getSupplierName());
            System.out.println("订单金额: " + subOrder.getActualAmount());
        }
    }

    /**
     * 示例2：拆分子订单项
     * 根据子订单，筛选并更新对应的订单明细
     */
    public void exampleSplitSubOrderItems() {
        // 假设已有子订单和原订单明细
        Orders subOrder = getSubOrder(); // 获取子订单
        List<OrderItem> originalOrderItems = getOrderItems(); // 获取原订单明细列表

        // 调用拆分子订单项方法
        List<OrderItem> subOrderItems = orderService.splitSubOrderItems(subOrder, originalOrderItems);

        // 处理拆分后的子订单项
        for (OrderItem item : subOrderItems) {
            System.out.println("订单项ID: " + item.getOrderItemId());
            System.out.println("商品名称: " + item.getProductName());
            System.out.println("购买数量: " + item.getBuyCounts());
            System.out.println("父订单项ID: " + item.getParentOrderItemId());
        }
    }

    /**
     * 示例3：完整的订单拆分流程
     * 结合两个方法实现完整的订单拆分
     */
    public void exampleCompleteOrderSplitting() {
        // 1. 获取主订单和订单明细
        Orders mainOrder = getMainOrder();
        List<OrderItem> orderItems = getOrderItems();

        // 2. 拆分子订单
        List<Orders> subOrders = orderService.splitSubOrders(mainOrder, orderItems);

        // 3. 为每个子订单拆分对应的订单项
        for (Orders subOrder : subOrders) {
            List<OrderItem> subOrderItems = orderService.splitSubOrderItems(subOrder, orderItems);
            
            System.out.println("供应商【" + subOrder.getSupplierName() + "】的子订单:");
            System.out.println("  子订单号: " + subOrder.getOrderSn());
            System.out.println("  订单项数量: " + subOrderItems.size());
            System.out.println("  订单金额: " + subOrder.getActualAmount());
        }
    }

    // 模拟方法，实际使用时需要从数据库或其他地方获取
    private Orders getMainOrder() {
        // 返回主订单对象
        return new Orders();
    }

    private Orders getSubOrder() {
        // 返回子订单对象
        return new Orders();
    }

    private List<OrderItem> getOrderItems() {
        // 返回订单明细列表
        return List.of();
    }
}
