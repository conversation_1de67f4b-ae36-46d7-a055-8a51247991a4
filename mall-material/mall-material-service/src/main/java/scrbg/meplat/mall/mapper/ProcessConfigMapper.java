package scrbg.meplat.mall.mapper;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import scrbg.meplat.mall.entity.ProcessConfig;
import scrbg.meplat.mall.vo.processConfig.ProcessConfigDtlVO;

/**
 * 流程配置表 Mapper 接口
 * <AUTHOR>
 * @date: 2025年6月20日 上午9:47:33
 */
@Mapper
@Repository
public interface ProcessConfigMapper extends BaseMapper<ProcessConfig> {
    
    /**
     * 根据流程配置id获取流程配置详情
     *
     * @param processId
     * @return
     */
    ProcessConfigDtlVO getProcessConfigDtlById(@Param("processId") String processId);

    Integer getMaxSort();
}