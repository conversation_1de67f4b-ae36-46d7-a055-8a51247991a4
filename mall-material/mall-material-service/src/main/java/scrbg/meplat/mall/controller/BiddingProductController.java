package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.BiddingProductService;
import scrbg.meplat.mall.entity.BiddingProduct;

import java.math.BigDecimal;
import java.util.List;

/**
 * @描述：竞价商品信息控制类
 * @作者: ye
 * @日期: 2023-07-11
 */
@RestController
@RequestMapping("/biddingProduct")
@Api(tags = "竞价商品信息")
public class BiddingProductController {

    @Autowired
    public BiddingProductService biddingProductService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<BiddingProduct> listByEntity(@RequestBody JSONObject jsonObject) {
        PageUtils page = biddingProductService.queryPage(jsonObject, new LambdaQueryWrapper<BiddingProduct>());
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<BiddingProduct> findById(String id) {
        BiddingProduct biddingProduct = biddingProductService.getById(id);
        return R.success(biddingProduct);
    }


    @GetMapping("/selectBidingPurchaseByBidingId")
    @ApiOperation(value = "根据竞价Id查询物资清单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bidingId", value = "竞价Id", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<List<BiddingProduct>> selectBidingPurchaseByBidingId(String bidingId) {
        List<BiddingProduct> biddingProducts = biddingProductService.selectBidingPurchaseByBidingId(bidingId);
        return R.success(biddingProducts);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新增")
    public R save(@RequestBody BiddingProduct biddingProduct) {
        biddingProductService.create(biddingProduct);
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody BiddingProduct biddingProduct) {
        biddingProductService.update(biddingProduct);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        biddingProductService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")
    public R deleteBatch(@RequestBody List<String> ids) {
        biddingProductService.removeByIds(ids);
        return R.success();
    }

    @GetMapping("/getBiddingProductByStSn")
    @ApiOperation(value = "根据大宗清单编号查询供应商物资报价清单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stSn", value = "大宗清单编号", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<List<BiddingProduct>> getBiddingProductByStSn(String stSn) {
        List<BiddingProduct> biddingProducts = biddingProductService.getBiddingProductByStSn(stSn);
        return R.success(biddingProducts);
    }

    @GetMapping("/updateBiddingProductPrice")
    @ApiOperation(value = "根据id修改供应商竞价价格")
    public R updateBiddingProductPrice(String id, BigDecimal price, Integer state) {
        biddingProductService.updateBiddingProductPrice(id, price, state);
        return R.success();
    }
}

