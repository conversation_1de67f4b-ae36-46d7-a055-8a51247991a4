package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 人员基本信息表实体类
 */
@ApiModel(value = "pcwp人员")
@Data
@TableName("pcwp_personinfos")
public class PcwpUser implements Serializable {
    /**
     * 唯一值（主键）
     */
    @TableField("id")
    private String id;

    /**
     * 人员姓名
     */
    @TableField("pname")
    private String pname;

    /**
     * 人员编码
     */
    @TableField("pnumber")
    private String pnumber;

    /**
     * 所属机构（主要任职机构）
     */
    @TableField("orgnumber")
    private String orgnumber;

    /**
     * 性别
     */
    @TableField("gender")
    private String gender;

    /**
     * 部门（主要任职机构下的部门）
     */
    @TableField("deptnumber")
    private String deptnumber;

    /**
     * 年龄
     */
    @TableField("age")
    private Integer age;

    /**
     * 身份证号
     */
    @TableField("idcard")
    private String idcard;

    /**
     * 目前岗位
     */
    @TableField("gw")
    private String gw;

    /**
     * 学历
     */
    @TableField("xl")
    private String xl;

    /**
     * 工作年限
     */
    @TableField("gznx")
    private Float gznx;

    /**
     * 学制
     */
    @TableField("xz")
    private String xz;

    /**
     * 毕业时间
     */
    @TableField("bysj")
    private String bysj;

    /**
     * 毕业院校
     */
    @TableField("byyx")
    private String byyx;

    /**
     * 毕业专业
     */
    @TableField("byzy")
    private String byzy;

    /**
     * 联系电话
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 社保编码
     */
    @TableField("sbbm")
    private String sbbm;

    /**
     * 社保缴纳单位
     */
    @TableField("sbjndw")
    private String sbjndw;

    /**
     * 邮箱
     */
    @TableField("yx")
    private String yx;

    /**
     * 个人特长描述
     */
    @TableField("tc")
    private String tc;

    /**
     * 最后更新时间
     */
    @TableField("lastupdatetime")
    private String lastupdatetime;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    @TableField("mdmstate")
    private Integer mdmstate;


    @TableField(
            exist = false
    )
    private String orgName;
    @TableField(
            exist = false
    )
    private String orgId;

    @TableField(
            exist = false
    )
    private String userName;
}
