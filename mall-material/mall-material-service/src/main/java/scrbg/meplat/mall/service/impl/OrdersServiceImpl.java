package scrbg.meplat.mall.service.impl;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TreeSet;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.zip.ZipOutputStream;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;

import scrbg.meplat.mall.common.constant.ProcessConstants;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.rabbitMQ.TTRabbitMQConnectionTest;
import scrbg.meplat.mall.config.rabbitMQ.ToDoMessageBody;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.controller.website.userCenter.UserCenterOrderController;
import scrbg.meplat.mall.dto.ReturnProductUpdateOrderDTO;
import scrbg.meplat.mall.dto.bidding.BatchUpdateBiddingItemInfoDTO;
import scrbg.meplat.mall.dto.bidding.CreateBidingByOrderDTO;
import scrbg.meplat.mall.dto.contact.RequestJsonRPCDTO;
import scrbg.meplat.mall.dto.contact.SubmitContactDTO;
import scrbg.meplat.mall.dto.contact.SubmitContactInfoDTO;
import scrbg.meplat.mall.dto.contact.SubmitContactOrderDTO;
import scrbg.meplat.mall.dto.contact.RPCDTO.CBM_EquipOperationOrderDTO;
import scrbg.meplat.mall.dto.contact.RPCDTO.CBM_EquipOperationOrderDtlDTO;
import scrbg.meplat.mall.dto.order.BatchCreateTwoOrderDTO;
import scrbg.meplat.mall.dto.order.BatchShipmentsDTO;
import scrbg.meplat.mall.dto.order.BuySkuInfoDTO;
import scrbg.meplat.mall.dto.order.CreateMaterialOrderByCartIdsDTO;
import scrbg.meplat.mall.dto.order.CreateRepairOrderDTO;
import scrbg.meplat.mall.dto.order.MasterAffirmTwoOrderDTO;
import scrbg.meplat.mall.dto.order.MergeOrderDTO;
import scrbg.meplat.mall.dto.order.ProductBuyInfoDTO;
import scrbg.meplat.mall.dto.order.UpdateTwoOrderPriceDTO;
import scrbg.meplat.mall.dto.plan.SubmitMonthPlanOrderDTO;
import scrbg.meplat.mall.dto.plan.SubmitMonthPlanOrderItemDTO;
import scrbg.meplat.mall.dto.product.UpdateProductStateDTO;
import scrbg.meplat.mall.dto.product.restsServe.CreateRepairAttrValueDTO;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.dto.user.OrgAndSon;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.enums.product.OrderEnum;
import scrbg.meplat.mall.enums.product.ProductEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.*;
import scrbg.meplat.mall.service.BiddingBidRecordService;
import scrbg.meplat.mall.service.BiddingInvitationRelevanceService;
import scrbg.meplat.mall.service.BiddingProductService;
import scrbg.meplat.mall.service.BiddingPurchaseService;
import scrbg.meplat.mall.service.BrandService;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.InterfaceLogsService;
import scrbg.meplat.mall.service.MaterialMonthSupplyPlanDtlService;
import scrbg.meplat.mall.service.MaterialMonthSupplyPlanService;
import scrbg.meplat.mall.service.OrderItemService;
import scrbg.meplat.mall.service.OrderReturnItemService;
import scrbg.meplat.mall.service.OrderReturnService;
import scrbg.meplat.mall.service.OrderSelectContactService;
import scrbg.meplat.mall.service.OrderSelectPlanService;
import scrbg.meplat.mall.service.OrderShipDtlService;
import scrbg.meplat.mall.service.OrderShipService;
import scrbg.meplat.mall.service.OrdersService;
import scrbg.meplat.mall.service.ProcessConfigService;
import scrbg.meplat.mall.service.ProductAttributeValueService;
import scrbg.meplat.mall.service.ProductCategoryService;
import scrbg.meplat.mall.service.ProductCommentService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.service.ProductSkuService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.ShoppingCartService;
import scrbg.meplat.mall.service.SynthesizeTemporaryDtlService;
import scrbg.meplat.mall.service.SynthesizeTemporaryService;
import scrbg.meplat.mall.service.SystemParamService;
import scrbg.meplat.mall.service.UserAddressService;
import scrbg.meplat.mall.service.UserService;
import scrbg.meplat.mall.service.impl.plan.PlanServiceImpl;
import scrbg.meplat.mall.service.plan.PlanService;
import scrbg.meplat.mall.util.BeanCopyIgnoreNullUtils;
import scrbg.meplat.mall.util.BillNumberGenerator;
import scrbg.meplat.mall.util.CodeGenerator;
import scrbg.meplat.mall.util.DateUtil;
import scrbg.meplat.mall.util.LogUtil;
import scrbg.meplat.mall.util.MaterialLockUtils;
import scrbg.meplat.mall.util.OrderUtils;
import scrbg.meplat.mall.util.RestTemplateUtils;
import scrbg.meplat.mall.util.SpringBeanUtil;
import scrbg.meplat.mall.util.TaxCalculator;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.pageUtils;
import scrbg.meplat.mall.util.cart.CartValidationUtils;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.util.excel.excel2pdf.Excel2Pdf;
import scrbg.meplat.mall.util.excel.excel2pdf.ExcelObject;
import scrbg.meplat.mall.util.poi.exp.PoiExporter;
import scrbg.meplat.mall.util.zip.ZipUtil;
import scrbg.meplat.mall.vo.GetSynthesizeTemporaryPlanDetailItemVO;
import scrbg.meplat.mall.vo.GetSynthesizeTemporaryPlanDetailVO;
import scrbg.meplat.mall.vo.bidding.HitBidVo;
import scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPDataItemVO;
import scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPDataVO;
import scrbg.meplat.mall.vo.order.GetShopManageOrderOutZIPVO;
import scrbg.meplat.mall.vo.platform.PlatformShopCountVo;
import scrbg.meplat.mall.vo.platform.ShopCountVo;
import scrbg.meplat.mall.vo.product.ShoppingCartShopIdVO;
import scrbg.meplat.mall.vo.product.material.CategoryClassIdAndClassNameVO;
import scrbg.meplat.mall.vo.product.order.OrderCreateResultVO;
import scrbg.meplat.mall.vo.product.restsServe.SettleRepairVO;
import scrbg.meplat.mall.vo.shopManage.reportForms.ProductFromVo;
import scrbg.meplat.mall.vo.user.userCenter.LogisticsOrderVO;
import scrbg.meplat.mall.vo.user.userCenter.OrderDetailVO;
import scrbg.meplat.mall.vo.user.userCenter.OrderItemProductVO;
import scrbg.meplat.mall.vo.user.userCenter.OrderPlanDetail;
import scrbg.meplat.mall.vo.user.userCenter.SettleAccountProductCartChildVO;
import scrbg.meplat.mall.vo.user.userCenter.SettleAccountProductCartVO;
import scrbg.meplat.mall.vo.user.userCenter.SettleAccountProductVO;
import scrbg.meplat.mall.vo.user.userCenter.UserOrderPageListVO;

/**
 * @描述：商品订单 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
public class OrdersServiceImpl extends ServiceImpl<OrdersMapper, Orders> implements OrdersService {

    @Autowired
    ShoppingCartMapper shoppingCartMapper;

    @Autowired
    SystemParamService systemParamService;

    @Autowired
    UserAddressService userAddressService;

    @Autowired
    private UserService userService;

    @Autowired
    SynthesizeTemporaryService synthesizeTemporaryService;

    @Autowired
    SynthesizeTemporaryDtlService synthesizeTemporaryDtlService;

    @Autowired
    OrderSelectPlanService orderSelectPlanService;

    @Autowired
    ShoppingCartService shoppingCartService;

    @Autowired
    BrandService brandService;

    @Autowired
    private InterfaceLogsService interfaceLogsService;

    @Autowired
    MaterialMonthSupplyPlanDtlMapper materialMonthSupplyPlanDtlMapper;

    @Autowired
    MaterialMonthSupplyPlanDtlService materialMonthSupplyPlanDtlService;

    @Autowired
    MaterialMonthSupplyPlanService materialMonthSupplyPlanService;

    @Autowired
    OrderSelectContactService orderSelectContactService;

    @Autowired
    OrderReturnService orderReturnService;

    @Autowired
    ProductAttributeValueService productAttributeValueService;

    @Autowired
    ProductCommentService productCommentService;

    @Autowired
    ProductCategoryService productCategoryService;

    @Autowired
    ProductSkuService productSkuService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    ProductService productService;

    @Autowired
    ShopService shopService;

    @Autowired
    OrdersMapper ordersMapper;

    @Autowired
    MallConfig mallConfig;

    @Autowired
    OrderShipService orderShipService;

    @Autowired
    OrderShipDtlService orderShipDtlService;

    @Autowired
    OrderReturnItemService orderReturnItemService;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    RestTemplateUtils restTemplateUtils;

    @Autowired
    BiddingPurchaseService biddingPurchaseService;

    @Autowired
    BiddingProductService biddingProductService;

    @Autowired
    BiddingInvitationRelevanceService biddingInvitationRelevanceService;

    @Autowired
    CartValidationUtils cartValidationUtils;

    @Autowired
    PlanService planService;

    @Autowired
    PlanServiceImpl planServiceImpl;

    @Autowired
    RegionPriceMapper regionPriceMapper;

    @Autowired
    private ShopBusinessMapper shopBusinessMapper;

    @Autowired
    TTRabbitMQConnectionTest ttRabbitMQConnectionTest;

    /**
     * 根据参数分页查询订单信息列表（平台）
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listPlatformOrdersByParameters(JSONObject jsonObject, LambdaQueryWrapper<Orders> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        Integer state = (Integer) innerMap.get("state");
        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        String shopName = (String) innerMap.get("shopName");
        String enterpriseName = (String) innerMap.get("enterpriseName");
        String untitled = (String) innerMap.get("untitled");
        String orderSn = (String) innerMap.get("orderSn");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        Integer productType = (Integer) innerMap.get("productType");
        String okStartDate = (String) innerMap.get("okStartDate");
        String startSuccessDate = (String) innerMap.get("startSuccessDate");
        String endSuccessDate = (String) innerMap.get("endSuccessDate");
        String okEndDate = (String) innerMap.get("okEndDate");
        String parentOrderId = (String) innerMap.get("parentOrderId");
        String deliverGoodsStartDate = (String) innerMap.get("deliverGoodsStartDate");
        String deliverGoodsEndDate = (String) innerMap.get("deliverGoodsEndDate");
        Boolean isQueryTwoOrder = (Boolean) innerMap.get("isQueryTwoOrder");
        Integer priceType = (Integer) innerMap.get("priceType");
        q.like(StringUtils.isNotEmpty(shopName), Orders::getShopName, shopName);
        q.like(StringUtils.isNotEmpty(enterpriseName), Orders::getEnterpriseName, enterpriseName);
        q.like(StringUtils.isNotEmpty(untitled), Orders::getUntitled, untitled);
        q.like(StringUtils.isNotEmpty(orderSn), Orders::getOrderSn, orderSn);
        // 只查询一级订单
        if (isQueryTwoOrder == null || !isQueryTwoOrder) {
            q.isNull(Orders::getParentOrderId);
        }
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(Orders::getOrderSn, keywords)
                        .or()
                        .like(Orders::getUntitled, keywords)
                        .or()
                        .like(Orders::getShopName, keywords)
                        .or()
                        .like(Orders::getReceiverMobile, keywords);
            });
        }
        q.eq(orderClass != null, Orders::getOrderClass, orderClass);
        q.ge(StringUtils.isNotBlank(abovePrice), Orders::getActualAmount, abovePrice);
        // TODO 这里后期有可能变，需要等竞价业务确认后再决定这里怎么判断
        q.eq(priceType != null, Orders::getBillType, priceType);
        q.eq(StringUtils.isNotBlank(parentOrderId), Orders::getParentOrderId, parentOrderId);
        q.le(StringUtils.isNotBlank(belowPrice), Orders::getActualAmount, belowPrice);
        q.between(StringUtils.isNotEmpty(startSuccessDate) && StringUtils.isNotEmpty(endSuccessDate), Orders::getSuccessDate, startSuccessDate, endSuccessDate);
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), Orders::getGmtCreate, startDate, endDate);
        q.between(StringUtils.isNotEmpty(okStartDate) && StringUtils.isNotEmpty(okEndDate), Orders::getFlishTime, okStartDate, okEndDate);
        q.between(StringUtils.isNotEmpty(deliverGoodsStartDate) && StringUtils.isNotEmpty(deliverGoodsEndDate), Orders::getDeliveryTime, deliverGoodsStartDate, deliverGoodsEndDate);
        q.eq(state != null, Orders::getState, state);
        q.eq(productType != null, Orders::getProductType, productType);
        q.eq(Orders::getMallType, mallConfig.mallType);
        if (orderBy == 1) {
            q.orderByDesc(Orders::getGmtCreate);
        }
        if (orderBy == 2) {
            q.orderByDesc(Orders::getFlishTime);
        }
        if (orderBy == 3) {
            q.orderByDesc(Orders::getDeliveryTime);
        }
        IPage<Orders> page = this.page(
                new Query<Orders>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    @Override
    public void create(Orders orders) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(orders);
    }

    @Override
    public void update(Orders orders) {
        Orders byId = getById(orders.getOrderId());
        if (byId != null) {
            BeanCopyIgnoreNullUtils.copyPropertiesIgnoreNull(orders, byId);
            if (!StringUtils.isEmpty(orders.getDeliveryFlowId())) {
                byId.setDeliveryTime(new Date());
            }
            updateById(byId);
        } else {
            throw new BusinessException(400, "修改失败");
        }
    }


    @Override
    public Orders getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }


    /**
     * 根据参数查询个人订单信息分页列表
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listUserCenterOrdersByParameters(JSONObject jsonObject, LambdaQueryWrapper<Orders> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        q.eq(Orders::getEnterpriseId, enterpriseId);
//        q.eq(Orders::getUserId, userId);
        String keywords = (String) innerMap.get("keywords");
        Integer state = (Integer) innerMap.get("state");
        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
//        Integer productType = (Integer) innerMap.get("productType");
        q.eq(Orders::getMallType, mallConfig.mallType);
        q.orderByDesc(Orders::getGmtModified);
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(Orders::getOrderSn, keywords)
                        .or()
                        .like(Orders::getUntitled, keywords)
                        .or()
                        .like(Orders::getShopName, keywords);

            });
        }
        q.ge(StringUtils.isNotBlank(abovePrice), Orders::getActualAmount, abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), Orders::getActualAmount, belowPrice);
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), Orders::getGmtCreate, startDate, endDate);
        q.eq(state != null, Orders::getState, state);
//        q.eq(productType != null, Orders::getProductType, productType);
        q.eq(Orders::getProductType, PublicEnum.TYPE_MATERIAL.getCode());
        q.eq(Orders::getMallType, mallConfig.mallType);
        q.eq(Orders::getIsDelete, PublicEnum.IS_DELETE_NO.getCode());
        IPage<Orders> page = this.page(
                new Query<Orders>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    /**
     * 合并订单
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void mergeOrder(MergeOrderDTO dto) {
//        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
//        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
//        List<String> orderIds = dto.getOrderId();
//        String orderSn = dto.getOrderSn();
//        List<Orders> orders = lambdaQuery().in(Orders::getOrderId, orderIds).ne(Orders::getOrderSn, orderSn).list();
//        if (CollectionUtils.isEmpty(orders)) {
//            throw new BusinessException("订单不存在！");
//        }
//        Orders order = lambdaQuery()
//                .eq(Orders::getOrderSn, orderSn)
//                .eq(Orders::getIsDelete, PublicEnum.IS_DELETE_NO.getCode())
////                .eq(Orders::getUserId, userId)
//                .eq(Orders::getEnterpriseId, enterpriseId)
//                .one();
//        if (order == null) {
//            throw new BusinessException("要合并到的订单不存在！");
//        }
//        // 总金额
//        BigDecimal totalAmount = order.getTotalAmount();
//        // 实际支付金额
//        BigDecimal actualAmount = order.getActualAmount();
//        List<String> deleteOrderIds = new ArrayList<>();
//        for (Orders o : orders) {
//            //TODO 商品名称
//            totalAmount = totalAmount.add(o.getTotalAmount());
//            actualAmount = actualAmount.add(o.getActualAmount());
//            deleteOrderIds.add(o.getOrderId());
//        }
//        // 删除订单
//        this.removeLoginByIds(deleteOrderIds);
//
//        boolean update = lambdaUpdate().eq(Orders::getOrderId, order.getOrderId())
//                .set(Orders::getTotalAmount, totalAmount)
//                .set(Orders::getGmtModified, new Date())
//                .set(Orders::getActualAmount, actualAmount)
//                .update();
//        if (!update) {
//            throw new BusinessException("操作失败！");
//        }
//
//        // 操作订单项
//        List<OrderItem> orderItems = orderItemService.lambdaQuery()
//                .in(OrderItem::getOrderId, deleteOrderIds)
//                .eq(OrderItem::getIsDelete, PublicEnum.IS_DELETE_NO.getCode())
//                .select(OrderItem::getOrderItemId)
//                .list();
//        if (CollectionUtils.isEmpty(orderItems)) {
//            throw new BusinessException("订单商品不存在！");
//        }
//        List<String> collect = orderItems.stream().map((t) -> {
//            return t.getOrderItemId();
//        }).collect(Collectors.toList());
//        boolean update1 = orderItemService.lambdaUpdate()
//                .in(OrderItem::getOrderItemId, collect)
//                .set(OrderItem::getOrderId, order.getOrderId())
//                .set(OrderItem::getGmtModified, new Date())
//                .set(OrderItem::getOrderSn, order.getOrderSn())
//                .update();
//        if (!update1) {
//            throw new BusinessException("操作失败！");
//        }
    }

    /**
     * 批量逻辑删除
     *
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeLoginByIds(List<String> ids) {
        boolean update = lambdaUpdate()
                .in(Orders::getOrderId, ids)
                .set(Orders::getIsDelete, PublicEnum.IS_DELETE_YES.getCode())
                .set(Orders::getGmtModified, new Date())
                .update();
        if (!update) {
            throw new BusinessException("操作失败！");
        }
        boolean update1 = orderItemService.lambdaUpdate()
                .in(OrderItem::getOrderId, ids)
                .set(OrderItem::getIsDelete, PublicEnum.IS_DELETE_YES.getCode())
                .set(OrderItem::getGmtModified, new Date())
                .update();
        if (!update1) {
            throw new BusinessException("操作失败！");
        }
    }

    /**
     * 结算商品
     *
     * @param productId
     * @param buyNum
     * @param leaseNum
     * @return
     */
    @Override
    public SettleAccountProductVO settleAccountProduct(String productId, String buyNum, BigDecimal leaseNum) {
        if (leaseNum == null) leaseNum = new BigDecimal(1);
        SettleAccountProductVO vo = new SettleAccountProductVO();
        Product product = productService.getProductExcludeRemarkById(productId);
        if (product == null) {
            throw new BusinessException("商品不存在！");
        }
        if (product.getState() == ProductEnum.STATE_STAY_PUTAWAY.getCode() || product.getState() == ProductEnum.STATE_SOLD_OUT.getCode()) {
            throw new BusinessException("商品已下架！");
        }
        BeanUtils.copyProperties(product, vo);
//        ProductCategory productCategory = productCategoryService.getProductCategoryById(vo.getClassId(), null);
//        if (productCategory != null) vo.setClassName(productCategory.getClassName());

        //品牌名称
        Brand brand = brandService.getById(vo.getBrandId());
        if (brand != null) vo.setBrandName(brand.getName());
        // 获取店铺名称
        String shopName = shopService.getShopNameById(product.getShopId());
        vo.setShopName(shopName);
        // 查询sku
        ProductSku sku = productSkuService.lambdaQuery().eq(ProductSku::getProductId, product.getProductId()).one();
        if (sku != null) {
            BeanUtils.copyProperties(sku, vo);
            BigDecimal bigDecimal = new BigDecimal(buyNum);
            BigDecimal multiply = new BigDecimal(0);
            if (product.getProductType() == 2 || product.getProductType() == 5) {
                multiply = bigDecimal.multiply(sku.getSellPrice()).multiply(leaseNum);
            } else {
                multiply = bigDecimal.multiply(sku.getSellPrice()).setScale(2, RoundingMode.HALF_UP);
            }
            // 设置小计和总金额
            vo.setNumTotalPrice(multiply);
            vo.setTotalPrice(multiply);
        }
        return vo;
    }

    /**
     * 结算购物车
     *
     * @param cartIds
     * @return
     */
    @Override
    public List<SettleAccountProductCartVO> settleAccountProductCart(List<String> cartIds) {
        List<ShoppingCart> shoppingCarts = shoppingCartMapper.selectBatchIds(cartIds);
        if (CollectionUtils.isEmpty(shoppingCarts)) {
            throw new BusinessException("购物车数据不存在");
        }
        for (ShoppingCart shoppingCart : shoppingCarts) {
            String productId = shoppingCart.getProductId();
            // 查看购物车的商品是否还上架未删除
            Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
            if (product == null) {
                Product byId = productService.getProductExcludeRemarkById(productId);
                if (byId != null) {
                    throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
                } else {
                    throw new BusinessException("商品不存在！");
                }
            }
        }
        List<ShoppingCart> shopInfo = shoppingCarts.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ShoppingCart::getShopId))), ArrayList::new));
        List<SettleAccountProductCartVO> vos = new ArrayList<>();
        BigDecimal totalPrice = new BigDecimal(0);
        // 店铺被去重
        for (ShoppingCart cart : shopInfo) {
            SettleAccountProductCartVO vo = new SettleAccountProductCartVO();
            String shopName = shopService.getShopNameById(cart.getShopId());
            vo.setShopName(shopName);
            List<SettleAccountProductCartChildVO> childVOs = new ArrayList<>();
            for (ShoppingCart shoppingCart : shoppingCarts) {
                if (shoppingCart.getShopId().equals(cart.getShopId())) {
                    String productId = shoppingCart.getProductId();
                    SettleAccountProductCartChildVO childVO = new SettleAccountProductCartChildVO();
                    childVO.setCartId(shoppingCart.getCartId());
                    childVO.setCartNum(shoppingCart.getCartNum());
                    childVO.setLeaseNum(shoppingCart.getLeaseNum());
                    // 开始查询
                    Product product = productService.getProductExcludeRemarkById(productId);
                    BeanUtils.copyProperties(product, childVO);
                    //品牌名称
                    Brand brand = brandService.getById(childVO.getBrandId());
                    if (brand != null) childVO.setBrandName(brand.getName());
                    // 查询sku
                    ProductSku sku = productSkuService.lambdaQuery().eq(ProductSku::getProductId, childVO.getProductId()).one();
                    if (sku != null) {
                        BeanUtils.copyProperties(sku, childVO);
                        BigDecimal multiply = new BigDecimal(0);
                        if (product.getProductType() == 2 || product.getProductType() == 5) {
                            multiply = childVO.getCartNum().multiply(sku.getSellPrice()).multiply(childVO.getLeaseNum());
                        } else {
                            multiply = childVO.getCartNum().multiply(sku.getSellPrice()).setScale(2, RoundingMode.HALF_UP);
                        }
                        // 设置小计和总金额
                        childVO.setNumTotalPrice(multiply);
                        totalPrice = totalPrice.add(multiply);
                    }
                    childVOs.add(childVO);
                }
                vo.setCartChildVOS(childVOs);
            }
            vos.add(vo);
        }
        vos.get(0).setTotalPrice(totalPrice);
        return vos;
    }

    /**
     * 查询我的订单
     *
     * @param jsonObject
     * @return
     */
    @Override
    public PageUtils getUserOrderPageList(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
//        String keywords = (String) innerMap.get("keywords");
//        String startDate = (String) innerMap.get("startDate");
//        String endDate = (String) innerMap.get("endDate");
//        Integer state = (Integer) innerMap.get("state");
//        Integer isComment = (Integer) innerMap.get("isComment");
        Integer mallType = mallConfig.mallType;
        innerMap.put("mallType", mallType);
        // 现在通过机构查询订单
//        innerMap.put("userId", ThreadLocalUtil.getCurrentUser().getUserId());
//        innerMap.put("keywords", keywords);
//        innerMap.put("startDate", startDate);
//        innerMap.put("endDate", endDate);
//        innerMap.put("state", state);
//        innerMap.put("isComment", isComment);
        if (mallConfig.profilesActive.equals("dev")) {

            // 机构数据查看权限（1本机及子级2只看本级3指定）
            Integer dataSelect = jsonObject.getInteger("dataSelect");
            List dataScopes = (List) jsonObject.get("dataScope");
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            String roleName = "物资下单权限";
            List<MallRole> mallRoles = user.getMallRoles();
            if (CollectionUtils.isEmpty(mallRoles) || mallRoles.stream().noneMatch(t -> t.getName().equals(roleName))) {
                throw new BusinessException(500, "当前用户无物资下单权限！");
            }
            if (dataSelect == null) {
                dataSelect = 2;
            }
            MallRole role = mallRoles.stream().filter(t -> t.getName().equals(roleName)).findFirst().get();
            if (dataSelect == 1 && role.getOrgSearch() == 1) {
                // 本机及子级
                List<String> ids = user.getOrgAndSon().stream().map(OrgAndSon::getOrgId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(ids)) {
                    List<String> orgIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(ids);
                    innerMap.put("enterpriseIds", orgIds);
                }
            } else if (dataSelect == 2) {
                // 只看本级
                innerMap.put("enterpriseIds", Collections.singletonList(user.getEnterpriseId()));
            } else if (dataSelect == 3 && role.getOrgSearch() == 1) {
                // 指定
                if (!CollectionUtils.isEmpty(dataScopes)) {
                    List<String> orgIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(dataScopes);
                    if (org.springframework.util.CollectionUtils.isEmpty(orgIds)) {
                        throw new BusinessException("机构查询失败");
                    }
                    innerMap.put("enterpriseIds", orgIds);
                } else {
                    // 查看除自己之外的下级
                    List<String> ids = user.getOrgAndSon().stream().map(OrgAndSon::getOrgId).collect(Collectors.toList());
                    List<String> orgIds = ids.stream().filter(t -> !t.equals(user.getOrgId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(orgIds)) {
                        List<String> orgIds1 = enterpriseInfoService.getLocalEnterpriseByOrgIds(orgIds);
                        if (org.springframework.util.CollectionUtils.isEmpty(orgIds1)) {
                            throw new BusinessException("机构查询失败");
                        }
                        innerMap.put("enterpriseIds", orgIds1);
                    }
                }
            } else {
                //UserLogin user = ThreadLocalUtil.getCurrentUser();
                innerMap.put("enterpriseIds", Arrays.asList(user.getEnterpriseId()));
            }
        } else {
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            innerMap.put("enterpriseIds", Arrays.asList(user.getEnterpriseId()));
        }

        // 新增：根据productType参数转换为productTypes数组
        List<Integer> productTypes = null;

        // 从前端获取productType参数
        Integer productType = jsonObject.getInteger("productType");

        if (productType != null) {
            // 根据productType值转换为对应的productTypes数组
            if (productType == 10) {//零星采购
                productTypes = Arrays.asList(0, 10);
            } else if (productType == 13) {//大宗临购
                productTypes = Arrays.asList(1, 13);
            } else if (productType == 2) {//周转材料
                productTypes = Arrays.asList(2);
            } else {
                // 如果是其他值，可以设置默认处理或者直接使用原值
                productTypes = Arrays.asList(productType);
            }
        } else {
            // 如果没有传递productType参数，使用默认值
            productTypes = Arrays.asList(1, 10); // 或者根据业务需求设置默认值
        }

        innerMap.put("productTypes", productTypes);
//        innerMap.put("orderSn",innerMap.get("keywords"));
//        innerMap.put("billNo",innerMap.get("keywords"));
//        innerMap.put("enterpriseName",innerMap.get("keywords"));

        int count = baseMapper.listUserOrderPageListCount(innerMap);
        pageUtils.pageDispose(jsonObject, count);
        Page<UserOrderPageListVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<UserOrderPageListVO> vos = baseMapper.listUserOrderPageList(pages, innerMap);
        for (UserOrderPageListVO vo : vos) {
            if (vo.getReturnState() == null) {
                OrderReturnItem one = orderReturnItemService.lambdaQuery().eq(OrderReturnItem::getOrderItemId, vo.getOrderItemId())
                        .orderByDesc(OrderReturnItem::getGmtCreate)
                        .last("LIMIT " + 1)
                        .select(OrderReturnItem::getOrderReturnId)
                        .one();
                if (one != null) {
                    OrderReturn byId = orderReturnService.getById(one.getOrderReturnId());
                    if (byId != null) {
                        vo.setReturnState(byId.getState() + "");
                    }
                }
            }
        }

        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    /**
     * 查询我的订单评价
     *
     * @param jsonObject
     * @return
     */
    @Override
    public PageUtils getUserOrderComment(JSONObject jsonObject) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
//        String keywords = (String) innerMap.get("keywords");
//        String startDate = (String) innerMap.get("startDate");
//        String endDate = (String) innerMap.get("endDate");
//        Integer state = (Integer) innerMap.get("state");
//        Integer isComment = (Integer) innerMap.get("isComment");
        Integer mallType = mallConfig.mallType;
        innerMap.put("mallType", mallType);
        // 现在通过机构查询订单
//        innerMap.put("userId", ThreadLocalUtil.getCurrentUser().getUserId());
//        innerMap.put("keywords", keywords);
//        innerMap.put("startDate", startDate);
//        innerMap.put("endDate", endDate);
//        innerMap.put("state", state);
//        innerMap.put("isComment", isComment);
        if (mallConfig.profilesActive.equals("dev")) {

            // 机构数据查看权限（1本机及子级2只看本级3指定）
            Integer dataSelect = jsonObject.getInteger("dataSelect");
            List dataScopes = (List) jsonObject.get("dataScope");
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            String roleName = "物资下单权限";
            List<MallRole> mallRoles = user.getMallRoles();
            if (CollectionUtils.isEmpty(mallRoles) || mallRoles.stream().noneMatch(t -> t.getName().equals(roleName))) {
                throw new BusinessException(500, "当前用户无物资下单权限！");
            }
            if (dataSelect == null) {
                dataSelect = 2;
            }
            MallRole role = mallRoles.stream().filter(t -> t.getName().equals(roleName)).findFirst().get();
            if (dataSelect == 1 && role.getOrgSearch() == 1) {
                // 本机及子级
                List<String> ids = user.getOrgAndSon().stream().map(OrgAndSon::getOrgId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(ids)) {
                    List<String> orgIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(ids);
                    innerMap.put("enterpriseIds", orgIds);
                }
            } else if (dataSelect == 2) {
                // 只看本级
                innerMap.put("enterpriseIds", Collections.singletonList(user.getEnterpriseId()));
            } else if (dataSelect == 3 && role.getOrgSearch() == 1) {
                // 指定
                if (!CollectionUtils.isEmpty(dataScopes)) {
                    List<String> orgIds = enterpriseInfoService.getLocalEnterpriseByOrgIds(dataScopes);
                    if (org.springframework.util.CollectionUtils.isEmpty(orgIds)) {
                        throw new BusinessException("机构查询失败");
                    }
                    innerMap.put("enterpriseIds", orgIds);
                } else {
                    // 查看除自己之外的下级
                    List<String> ids = user.getOrgAndSon().stream().map(OrgAndSon::getOrgId).collect(Collectors.toList());
                    List<String> orgIds = ids.stream().filter(t -> !t.equals(user.getOrgId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(orgIds)) {
                        List<String> orgIds1 = enterpriseInfoService.getLocalEnterpriseByOrgIds(orgIds);
                        if (org.springframework.util.CollectionUtils.isEmpty(orgIds1)) {
                            throw new BusinessException("机构查询失败");
                        }
                        innerMap.put("enterpriseIds", orgIds1);
                    }
                }
            } else {
                //UserLogin user = ThreadLocalUtil.getCurrentUser();
                innerMap.put("enterpriseIds", Arrays.asList(user.getEnterpriseId()));
            }
        } else {
            UserLogin user = ThreadLocalUtil.getCurrentUser();
            innerMap.put("enterpriseIds", Arrays.asList(user.getEnterpriseId()));
        }

        // 新增：根据productType参数转换为productTypes数组
        List<Integer> productTypes = null;

        // 从前端获取productType参数
        Integer productType = jsonObject.getInteger("productType");

        if (productType != null) {
            // 根据productType值转换为对应的productTypes数组
            if (productType == 10) {
                productTypes = Arrays.asList(0, 10);
            } else if(productType == 13){
                productTypes = Arrays.asList(1, 13);
                // 如果是其他值，可以设置默认处理或者直接使用原值
            }else{
                productTypes = Arrays.asList(productType);
            }
        } else {
            // 如果没有传递productType参数，使用默认值
            productTypes = Arrays.asList(1, 10); // 或者根据业务需求设置默认值
        }

        innerMap.put("productTypes", productTypes);

        int count = baseMapper.listUserOrderCommentPageListCount(innerMap);
        pageUtils.pageDispose(jsonObject, count);
        Page<UserOrderPageListVO> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<UserOrderPageListVO> vos = baseMapper.listUserOrderCommentPageList(pages, innerMap);
        pages.setRecords(vos);
        return new PageUtils(pages);
    }

    /**
     * 购物车结算主方法
     *
     * @return 订单创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Orders> createMaterialOrderByCartIds(List<ShoppingCartShopIdVO> cartVOS, Plan plan, int orderStatus) {

        // 1. 生成订单
        List<Orders> orders = createOrders(cartVOS, plan,orderStatus);

        // 2. 生成订单项
        createOrderItems(orders, cartVOS);

        return orders;
    }

    /**
     * 第二部分：生成订单
     *
     * @param cartVOS 购物车信息
     * @param plan 计划信息
     * @return 生成的订单列表
     */
    private List<Orders> createOrders(List<ShoppingCartShopIdVO> cartVOS, Plan plan,int orderStatus) {
        List<Orders> orders = new ArrayList<>();
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();

        // 都验证通过开始保存订单
        for (ShoppingCartShopIdVO cartVO : cartVOS) {
            Orders order = new Orders();
            order.setEnterpriseId(currentUser.getEnterpriseId());
            order.setEnterpriseName(currentUser.getEnterpriseName());

            // 生成订单号
            order.setOrderSn(BillNumberGenerator.generateOrderNumber(0, false));
            // 设置计划ID和编号
            if (plan != null) {
                order.setPlanId(plan.getBillId());
                order.setPlanNo(plan.getBillNo());
            }
            // 用户ID
            String userId = currentUser.getUserId();
            order.setUserId(userId);
            order.setOrgId(currentUser.getOrgId());
            order.setEnterpriseId(currentUser.getEnterpriseId());
            order.setEnterpriseName(currentUser.getEnterpriseName());
            //收货人信息
            order.setReceiverName(cartVO.getReceiverName());
            order.setReceiverMobile(cartVO.getReceiverMobile());
            order.setReceiverAddress(cartVO.getReceiverAddress());
            // 店铺名称
            order.setShopId(cartVO.getShopId());
            String shopName = shopService.getShopNameById(cartVO.getShopId());
            order.setShopName(shopName);
            // 计算金额
            BigDecimal actualAmount2 = BigDecimal.ZERO;
            BigDecimal totalAmount2 = BigDecimal.ZERO;
            BigDecimal costPriceTotal2 = BigDecimal.ZERO;
            List<String> productNames = new ArrayList<>();
            int productType = 0;
            LambdaQueryWrapper<ShopBusiness> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ShopBusiness::getShopId, cartVO.getShopId()) // 假设ShopBusiness实体中有shopId字段，关联Shop表
                    .eq(ShopBusiness::getState, 1); // 假设有status状态字段
            List<ShopBusiness> list = shopBusinessMapper.selectList(wrapper);//自营店
            for (ShoppingCart cart : cartVO.getShoppingCarts()) {
//                ProductSku sku = productSkuService.getById(cart.getSkuId());
                Product product = productService.getById(cart.getProductId());

                productType = product.getProductType();
                productNames.add(product.getProductName());
                order.setUntitled(product.getProductName().substring(0, product.getProductName().length() - 1));
                // 判断sku价格是否为null或0，如果为null或0则不累加
//                if (sku.getSellPrice() != null && sku.getSellPrice().compareTo(BigDecimal.ZERO) != 0) {
//                    actualAmount2 = actualAmount2.add(sku.getSellPrice().multiply(cart.getCartNum()).setScale(2, RoundingMode.HALF_UP));
//                }
//                if (sku.getOriginalPrice() != null && sku.getOriginalPrice().compareTo(BigDecimal.ZERO) != 0) {
//                    totalAmount2 = totalAmount2.add(sku.getOriginalPrice().multiply(cart.getCartNum()).setScale(2, RoundingMode.HALF_UP));
//                }
//                if (sku.getCostPrice() != null && sku.getCostPrice().compareTo(BigDecimal.ZERO) != 0) {
//                    costPriceTotal2 = costPriceTotal2.add(sku.getCostPrice().multiply(cart.getCartNum()).setScale(2, RoundingMode.HALF_UP));
//                }
                totalAmount2 = totalAmount2.add(cart.getProductPrice().multiply(cart.getCartNum()).setScale(2, RoundingMode.HALF_UP));
                if(list.size() >0 && product.getProductSource() != null && product.getProductSource()==2) {//自营店上架商品来自二级供应商
                    LambdaQueryWrapper<RegionPrice> regionPriceWrapper = new LambdaQueryWrapper<>();
                    regionPriceWrapper
                            .eq(RegionPrice::getProductId, cart.getProductId())
                            .like(RegionPrice::getArea, cart.getZoneAddr())
                            .eq(RegionPrice::getAccountPeriod, cart.getPaymentPeriod());
                    RegionPrice regionPrices = regionPriceMapper.selectOne(regionPriceWrapper);
                    costPriceTotal2 = costPriceTotal2.add(regionPrices.getTaxInPrice().multiply(cart.getCartNum()).setScale(2, RoundingMode.HALF_UP));
                }else if(list.size() >0 && product.getProductSource() != null && product.getProductSource()==3) {//自营店上架商品来自自营店自营商品
                    costPriceTotal2 = costPriceTotal2.add(product.getPurchasePrice().multiply(cart.getCartNum()).setScale(2, RoundingMode.HALF_UP));
                }
                order.setSupplierId(product.getSupperBy());
                order.setSupplierName(product.getSupplierName());
            }
            // 商品名称拼接
            order.setUntitled(String.join(",", productNames));
            // 设置金额
            order.setTotalAmount(totalAmount2);
            order.setActualAmount(totalAmount2);
            order.setCostPriceTotal(costPriceTotal2);
            order.setProfitPriceTotal(totalAmount2.subtract(costPriceTotal2));
            order.setFlishTime(new Date());
            order.setState(OrderEnum.STATE_FINISH.getCode());
            order.setOrderFreight(new BigDecimal("0"));
            order.setTaxRate(cartVO.getShoppingCarts().get(0).getTaxRate());
            if (list.size() > 0) {//自营店
                order.setOrderClass(2);//多供方订单
            } else {//第三方店铺
                order.setOrderClass(1);//一般订单
            }
            // 商品类型：0 低值易耗品 1大宗临采 2、周转材料
//            Integer productType = cartVO.getShoppingCarts().get(0).getProductType();
            order.setProductType(productType);
            // 发票状态
            order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());

            boolean save = this.save(order);
            if (!save) {
                throw new BusinessException("订单保存失败！");
            }
            orders.add(order);
        }

        return orders;
    }

    /**
     * 第三部分：生成订单项并处理库存
     *
     * @param orders  订单列表
     * @param cartVOS 购物车信息
     */
    private void createOrderItems(List<Orders> orders, List<ShoppingCartShopIdVO> cartVOS) {

        List<String> cartIds = cartVOS.stream()
                .map(ShoppingCartShopIdVO::getShoppingCarts)
                .filter(carts -> carts != null)
                .flatMap(List::stream)
                .map(ShoppingCart::getCartId)
                .collect(Collectors.toList());

        for (int i = 0; i < orders.size(); i++) {
            Orders order = orders.get(i);
            ShoppingCartShopIdVO cartVO = cartVOS.get(i);

            List<ShoppingCart> shoppingCarts = cartVO.getShoppingCarts();
            ArrayList<OrderItem> orderItems = new ArrayList<>();
            ArrayList<ProductSku> productSkus = new ArrayList<>();
            List<String> outProductId = new ArrayList<>();
            List<OrderSelectPlan> orderSelectPlans = new ArrayList<>();

            for (ShoppingCart cart : shoppingCarts) {
                Product product = productService.getById(cart.getProductId());
                ProductSku productSku = productSkuService.getProductSkuById(cart.getSkuId(), PublicEnum.STATE_OPEN.getCode());

                // 创建订单项
                OrderItem orderItem = new OrderItem();
                orderItem.setUnit(productSku.getUnit());
                orderItem.setOrderId(order.getOrderId());
                orderItem.setOrderSn(order.getOrderSn());
                orderItem.setProductId(cart.getProductId());
                orderItem.setProductSn(product.getSerialNum());
                orderItem.setProductName(product.getProductName().trim());
                orderItem.setProductImg(productSku.getSkuImg());
                orderItem.setSkuId(cart.getSkuId());
                orderItem.setSkuName(productSku.getSkuName() == null ? null : productSku.getSkuName().trim());
                orderItem.setRelevanceName(product.getRelevanceName().trim());
                orderItem.setRelevanceNo(product.getRelevanceNo());
                orderItem.setProductPrice(cart.getProductPrice());
                orderItem.setBuyCounts(cart.getCartNum());
                orderItem.setProductType(product.getProductType());
                // 设置含税总金额
                BigDecimal totalAmount = cart.getProductPrice().multiply(cart.getCartNum());
                orderItem.setTotalAmount(totalAmount);
                orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
                orderItem.setCostPrice(productSku.getCostPrice());
                orderItem.setOriginalPrice(productSku.getOriginalPrice());
                if (productSku.getCostPrice() != null &&
                        productSku.getCostPrice().compareTo(BigDecimal.ZERO) > 0) {
                    orderItem.setProfitPrice(productSku.getSellPrice().subtract(productSku.getCostPrice()));
                }
                if(order.getOrderClass() ==2) {//多供方订单
                    orderItem.setState(1);//已分配
                }
                orderItem.setSupplierId(product.getSupperBy());
                orderItem.setClassId(product.getClassId());
                orderItem.setClassPathName(product.getClassPathName());
                orderItem.setBrandId(product.getBrandId());
                orderItem.setBrandName(product.getBrandName());
                orderItem.setTaxRate(cart.getTaxRate());

                // 计算不含税单价和不含税总金额
                BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(cart.getProductPrice(), cart.getTaxRate());
                orderItem.setNoRatePrice(noRatePrice);
                BigDecimal noRateAmount = TaxCalculator.noTarRateItemAmount(totalAmount, noRatePrice, cart.getCartNum(), cart.getTaxRate());
                orderItem.setNoRateAmount(noRateAmount);
//                orderItem.setNoRatePrice(product.getCostPrice());
//                orderItem.setNoRateAmount("");
                orderItems.add(orderItem);

                // 创建关联计划信息
                OrderSelectPlan orderSelectPlan = createOrderSelectPlan(order, orderItem, cart);
                orderSelectPlans.add(orderSelectPlan);

                // 减去库存
                String skuId = cart.getSkuId();
                BigDecimal newStock = productSku.getStock().subtract(cart.getCartNum());
                ProductSku productSku2 = new ProductSku();
                productSku2.setSkuId(skuId);
                productSku2.setStock(newStock);
                productSkus.add(productSku2);

                // 判断库存是否等于0如果等于0则下架商品
                if (newStock.compareTo(BigDecimal.ZERO) == 0) {
                    outProductId.add(cart.getProductId());
                }
            }

            // 批量保存订单项
            boolean f = orderItemService.saveBatch(orderItems);
            if (!f) {
                throw new BusinessException("订单保存失败！");
            }

            if(order.getOrderClass() ==2) {//多供方订单
                //生成子订单和子订单项插入
                processOrderSplittingHelper(order, orderItems, order.getTaxRate());
            }

            // 批量保存关联计划信息
            if (!CollectionUtils.isEmpty(orderSelectPlans)) {
                boolean planSaved = orderSelectPlanService.saveBatch(orderSelectPlans);
                if (!planSaved) {
                    throw new BusinessException("订单关联计划保存失败！");
                }
            }
        }
    }

    /**
     * 创建订单关联计划信息
     *
     * @param order     订单信息
     * @param orderItem 订单项信息
     * @param cart      购物车信息
     * @return 订单关联计划
     */
    private OrderSelectPlan createOrderSelectPlan(Orders order, OrderItem orderItem, ShoppingCart cart) {

        OrderSelectPlan orderSelectPlan = new OrderSelectPlan();
        orderSelectPlan.setOrderId(order.getOrderId());
        orderSelectPlan.setOrderSn(order.getOrderSn());
        orderSelectPlan.setOrderItemId(orderItem.getOrderItemId());
        orderSelectPlan.setEquipmentName(orderItem.getProductName());
        orderSelectPlan.setCount(orderItem.getBuyCounts());
        orderSelectPlan.setProductType(orderItem.getProductType());
        orderSelectPlan.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
        orderSelectPlan.setOrgName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
        orderSelectPlan.setPrice(orderItem.getProductPrice());
        orderSelectPlan.setAccount(orderItem.getTotalAmount());
        if ("0".equals(orderItem.getProductType())) { //1低值易耗品
            orderSelectPlan.setPlanType(1);
        } else if ("1".equals(orderItem.getProductType())) {//2大宗临购
            orderSelectPlan.setPlanType(2);
        } else if ("2".equals(orderItem.getProductType())) {//3、周转材料
            orderSelectPlan.setPlanType(3);
        }
        return orderSelectPlan;
    }

    /**
     * 商品信息的商品类型转为订单的商品类型
     *
     * @param productType
     * @return
     */
    private int getOrderProductType(Integer productType) {
        if (productType == null) {
            return 10; // 默认为零星采购
        }
        switch (productType) {
            case 0:
                return 10; // 低值易耗品 → 零星采购（低值易耗品）
            case 1:
                return 13; // 大宗临采 → 大宗临购
            case 2:
                return 14; // 周转材料 → 周转材料
            default:
                return 10; // 默认为零星采购
        }
    }


//    public OrderCreateResultVO createMaterialOrderByCartIds(CreateMaterialOrderByCartIdsDTO dto) {
//        List<ShoppingCartProductInfoVO> cartVOS = shoppingCartMapper.listProductIdAndCartByCartIds(dto.getCartIds(), PublicEnum.TYPE_MATERIAL.getCode(), ThreadLocalUtil.getCurrentUser().getUserId());
//        if(CollectionUtils.isEmpty(cartVOS)){
//            throw new BusinessException("商品不存在或购物车信息不存在！");
//        }
//        OrderCreateResultVO vo = new OrderCreateResultVO();
//        String untitled = "";
//        // 实际总金额
//        BigDecimal actualAmount = new BigDecimal(0);
//        // 订单总金额
//        BigDecimal totalAmount = new BigDecimal(0);
//        //检查数据库
//        for (ShoppingCartProductInfoVO cartVO : cartVOS) {
//            String productId = cartVO.getProductId();
//            // 查看购物车的商品是否还上架未删除
//            Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
//            if (product == null) {
//                Product byId = productService.getProductExcludeRemarkById(productId);
//                if (byId != null) {
//                    throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
//                } else {
//                    throw new BusinessException(OrderEnum.RESULT_CODE_500201.getCode(), OrderEnum.RESULT_CODE_500201.getRemark());
//                }
//            } else {
//                // 商品存在
//                List<ShoppingCart> shoppingCarts = cartVO.getShoppingCarts();
//                for (ShoppingCart cart : shoppingCarts) {
//                    // 查询已上架未删除
//                    ProductSku productSku = productSkuService.getProductSkuById(cart.getSkuId(), PublicEnum.STATE_OPEN.getCode());
//                    if (productSku == null) {
//                        // 直接查询
//                        ProductSku byId = productSkuService.getById(cart.getSkuId());
//                        if (byId != null) {
//                            throw new BusinessException("商品名为：【" + product.getProductName() + "】商品的【" + byId.getSkuName() + "】规格已下架或已被删除！");
//                        } else {
//                            throw new BusinessException("订单保存失败！");
//                        }
//                    } else {
//                        // 如果库存不足
//                        int i = cart.getCartNum().compareTo(productSku.getStock());
//                        if (i == 1) {
//                            throw new BusinessException("【" + product.getProductName() + "】商品的【" + productSku.getSkuName() + "】库存不足!");
//                        }
//                        //获取每个商品名称，以,分割，拼接成字符串
//                        untitled = untitled + product.getProductName() + ",";
//
//                        actualAmount = actualAmount.add(productSku.getSellPrice().multiply(cart.getCartNum()));
//                        totalAmount = totalAmount.add(productSku.getOriginalPrice().multiply(cart.getCartNum()));
//                        cart.setActualAmount(productSku.getSellPrice().multiply(cart.getCartNum()));
//                        cart.setTotalAmount(productSku.getOriginalPrice().multiply(cart.getCartNum()));
//                    }
//                }
//            }
//        }
//        // 验价
//        if (Math.abs(dto.getPayPrice().subtract(actualAmount).doubleValue()) >= 0.01) {
//            throw new BusinessException("订单商品价格发生变化，请确认后再次提交！");
//        }
//
//
//        // 都验证通过开始保存订单
//        for (ShoppingCartProductInfoVO cartVO : cartVOS) {
//            String productId = cartVO.getProductId();
//            Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
//            Orders order = new Orders();
////            // TODO 获取内外部
////            Integer userType = 0;
////            Integer shopType = 1;
////            Integer shopSupport = 1;
////            dealTypeAndPayWay(order, userType, shopType, shopSupport);
//            // 暂时都是前端传入内部结算
//
//            // 生成订单号
//            String orderSn = OrderUtils.getOrder();
//            order.setOrderSn(orderSn);
//
//            BeanUtils.copyProperties(dto, order);
//
//            // 用户ID
//            String userId = ThreadLocalUtil.getCurrentUser().getUserId();
//            order.setUserId(userId);
//            // 商品名称拼接
////            order.setUntitled(untitled.substring(0, untitled.length() - 1));
//            order.setUntitled(product.getProductName());
//
//            // 店铺名称
//            order.setShopId(product.getShopId());
//            String shopName = shopService.getShopNameById(product.getShopId());
//            order.setShopName(shopName);
//            // 金额
//            // 现在商品是一对一
//            order.setTotalAmount(cartVO.getShoppingCarts().get(0).getTotalAmount());
//            order.setActualAmount(cartVO.getShoppingCarts().get(0).getActualAmount());
//            order.setState(OrderEnum.STATE_FINISH.getCode());
//            order.setOrderFreight(new BigDecimal("0"));
//            order.setProductType(product.getProductType());
//
//            // 发票状态
//            order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());
//            boolean save = this.save(order);
//            if (!save) {
//                throw new BusinessException("订单保存失败！");
//            }
//            vo.setOrderId(order.getOrderId());
//            vo.setOrderSn(order.getOrderSn());
//            vo.setTotalAmount(order.getTotalAmount());
//            vo.setUntitled(order.getUntitled());
//            List<ShoppingCart> shoppingCarts = cartVO.getShoppingCarts();
//            ArrayList<OrderItem> orderItems = new ArrayList<>();
//            ArrayList<ProductSku> productSkus = new ArrayList<>();
//            // 要下架的商品id
//            List<String> outProductId = new ArrayList<>();
//            for (ShoppingCart cart : shoppingCarts) {
//                ProductSku productSku = productSkuService.getProductSkuById(cart.getSkuId(), PublicEnum.STATE_OPEN.getCode());
//                // 订单项
//                OrderItem orderItem = new OrderItem();
//                orderItem.setOrderId(order.getOrderId());
//                orderItem.setOrderSn(order.getOrderSn());
//                orderItem.setProductId(cartVO.getProductId());
//                orderItem.setProductSn(product.getSerialNum());
//                orderItem.setProductName(product.getProductName());
//                orderItem.setProductImg(productSku.getSkuImg());
//                orderItem.setSkuId(cart.getSkuId());
//                orderItem.setSkuName(productSku.getSkuName().trim());
//                orderItem.setProductPrice(productSku.getSellPrice());
//                orderItem.setBuyCounts(cart.getCartNum());
//                orderItem.setProductType(product.getProductType());
//                orderItem.setTotalAmount(productSku.getSellPrice().multiply(cart.getCartNum()));
//                orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
//                orderItem.setCostPrice(productSku.getCostPrice());
//                orderItems.add(orderItem);
//
//                // TODO 减库存放在保存订单处
//                String skuId = cart.getSkuId();
//                BigDecimal newStock = productSku.getStock().subtract(cart.getCartNum());
//                ProductSku productSku2 = new ProductSku();
//                productSku2.setSkuId(skuId);
//                productSku2.setStock(newStock);
//                productSkus.add(productSku2);
//
//                // 判断库存是否等于0如果等于0则下架商品
//                if (newStock.compareTo(BigDecimal.ZERO) == 0) {
//                    outProductId.add(cart.getProductId());
//                }
//            }
//            boolean f = orderItemService.saveBatch(orderItems);
//            if (!f) {
//                throw new BusinessException("订单保存失败！");
//            }
////            boolean b = productSkuService.updateBatchById(productSkus);
////            if (!b) {
////                throw new BusinessException(OrderEnum.RESULT_CODE_500200.getCode(), OrderEnum.RESULT_CODE_500200.getRemark());
////            }
//            shoppingCartMapper.removeRealByIds(dto.getCartIds());
//            // 下架商品
//            if (!CollectionUtils.isEmpty(outProductId)) {
//                UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
//                updateProductStateDTO.setState(2);
//                updateProductStateDTO.setProductIds(outProductId);
//                productService.updateProductState(updateProductStateDTO);
//            }
//        }
//        return vo;
//    }

    /**
     * 判断对内对外
     *
     * @param order
     * @param userType
     * @param shopType
     * @param shopSupport
     */
    public void dealTypeAndPayWay(Orders order, Integer userType, Integer shopType, Integer shopSupport) {
        // 如果都是外部
        if (userType == shopType && userType == 0) {
            // 外部店铺-外部用户
            order.setDealType(OrderEnum.DEAL_TYPE_WD_WY.getCode());
            // 线上支付
            order.setPayWay(OrderEnum.PAY_WAY_ON_LINE.getCode());
        } else {
            // 店铺支持内部结算
            if (shopSupport == 1) {
                test();// TODO 保存 计划
                if (shopType == 1 && userType == 0) {
                    // 内部店铺-外部用户
                    order.setDealType(OrderEnum.DEAL_TYPE_ND_WU.getCode());
                    order.setPayWay(OrderEnum.PAY_WAY_INTERIOR_SETTLEMENT.getCode());
                }
                if (shopType == 0 && userType == 1) {
                    // 外部店铺-内部用户
                    order.setDealType(OrderEnum.DEAL_TYPE_WD_NU.getCode());
                    order.setPayWay(OrderEnum.PAY_WAY_INTERIOR_SETTLEMENT.getCode());
                }
                if (shopType == 1 && userType == 1) {
                    // 内部店铺-内部用户
                    order.setDealType(OrderEnum.DEAL_TYPE_ND_NU.getCode());
                    order.setPayWay(OrderEnum.PAY_WAY_INTERIOR_SETTLEMENT.getCode());
                }
            } else {
                throw new BusinessException("该订单不支持内部结算!");
            }
        }
    }

    public void test() {

    }

    /**
     * 直接下单
     *
     * @param dto
     */
    public OrderCreateResultVO createMaterialOrderByproduct(CreateMaterialOrderByCartIdsDTO dto) {
        String productId = dto.getProductId();
        OrderCreateResultVO vo = new OrderCreateResultVO();
        String untitled = "";

        // 实际总金额
        BigDecimal actualAmount = new BigDecimal(0);
        // 不含税
        BigDecimal noRateActualAmount = new BigDecimal(0);
        // 订单总金额
        BigDecimal totalAmount = new BigDecimal(0);
        // 总成本价
        BigDecimal costPriceTotal = new BigDecimal(0);
        Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
        if (product == null) {
            Product byId = productService.getProductExcludeRemarkById(productId);
            if (byId != null) {
                throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
            } else {
                throw new BusinessException("商品不存在！");
            }
        }
        Shop shopOne = shopService.lambdaQuery().eq(Shop::getShopId, product.getShopId())
                .select(Shop::getEnterpriseId).one();
        String enterpriseId = shopOne.getEnterpriseId();
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate).one();
        String enterpriseName = enterpriseInfo.getEnterpriseName();
        BigDecimal taxRate = enterpriseInfo.getTaxRate();
        if (taxRate == null) {
            throw new BusinessException("【" + enterpriseName + "】供应商未设置利率！");
        }

        ProductCategory productCategory = productCategoryService.getById(product.getClassId());
        if (productCategory == null) {
            throw new BusinessException("商品类别不存在");
        } else {
            String classId = productCategory.getClassId();
            List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(classId);
            if (!categoryParentPath.get(0).getClassName().equals("办公用品")) {
                throw new BusinessException("商品名为：【" + product.getProductName() + "】不是办公用品商品不能直接下单！");
            }
        }
        // 商品存在
        List<BuySkuInfoDTO> buySkuInfoDTOS = dto.getBuySkuInfoDTOS();
        for (BuySkuInfoDTO buySkuInfoDTO : buySkuInfoDTOS) {
            // 查询已上架未删除
            ProductSku productSku = productSkuService.getProductSkuById(buySkuInfoDTO.getSkuId(), PublicEnum.STATE_OPEN.getCode());
            if (productSku == null) {
                // 直接查询
                ProductSku byId = productSkuService.getById(buySkuInfoDTO.getSkuId());
                if (byId != null) {
                    throw new BusinessException("商品名为：【" + product.getProductName() + "】商品的【" + byId.getSkuName() + "】规格已下架或已被删除！");
                } else {
                    throw new BusinessException("商品不存在！");
                }
            } else {
                // 如果库存不足
                int i = buySkuInfoDTO.getBuyNum().compareTo(productSku.getStock());
                if (i == 1) {
                    throw new BusinessException("【" + product.getProductName() + "】商品的【" + productSku.getSkuName() + "】库存不足!");
                }
                //获取每个商品名称，以,分割，拼接成字符串
                untitled = untitled + product.getProductName() + ",";
                actualAmount = actualAmount.add(productSku.getSellPrice().multiply(buySkuInfoDTO.getBuyNum()).setScale(2, RoundingMode.HALF_UP));
                BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(productSku.getSellPrice(), taxRate);
//                noRateActualAmount = noRateActualAmount.add(noRatePrice.multiply(buySkuInfoDTO.getBuyNum()).setScale(2, RoundingMode.HALF_UP));
                BigDecimal noRateAmount = TaxCalculator.noTarRateItemAmount(productSku.getSellPrice().multiply(buySkuInfoDTO.getBuyNum()).setScale(2, RoundingMode.HALF_UP),
                        noRatePrice, buySkuInfoDTO.getBuyNum(), taxRate);
                noRateActualAmount = noRateActualAmount.add(noRateAmount);
                totalAmount = totalAmount.add(productSku.getOriginalPrice().multiply(buySkuInfoDTO.getBuyNum()).setScale(2, RoundingMode.HALF_UP));
                costPriceTotal = costPriceTotal.add(productSku.getCostPrice().multiply(buySkuInfoDTO.getBuyNum()).setScale(2, RoundingMode.HALF_UP));
            }
        }
        // 验价
        if (Math.abs(dto.getPayPrice().subtract(actualAmount).doubleValue()) >= 0.01) {
            // 验价不通过
            throw new BusinessException("订单商品价格发生变化，请确认后再次提交!");
        }

        Orders order = new Orders();
        order.setEnterpriseId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        order.setEnterpriseName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
//            Integer userType = 0;
//            Integer shopType = 1;
//            Integer shopSupport = 1;
//            dealTypeAndPayWay(order, userType, shopType, shopSupport);

        // 暂时都是前端传入内部结算
        BeanUtils.copyProperties(dto, order);
        // 店铺名称
        order.setShopId(product.getShopId());
        String shopName = shopService.getShopNameById(product.getShopId());
        order.setShopName(shopName);
        // 用户ID
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        order.setUserId(userId);
        order.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
        // 商品名称拼接
        order.setUntitled(untitled.substring(0, untitled.length() - 1));
        // 设置总金额
        order.setTotalAmount(totalAmount);
        order.setActualAmount(actualAmount);
        order.setSupplierId(enterpriseId);
        order.setSupplierName(enterpriseName);

        if (mallConfig.isNotRateAmount == 1) {
            order.setNoRateAmount(noRateActualAmount);
        } else {
            order.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(actualAmount, taxRate));
        }
        order.setTaxRate(taxRate);
        order.setCostPriceTotal(costPriceTotal);
        order.setProfitPriceTotal(actualAmount.subtract(costPriceTotal));
        order.setFlishTime(new Date());
        order.setState(OrderEnum.STATE_FINISH.getCode());
        order.setOrderFreight(new BigDecimal("0"));
//            order.setProductType(product.getProductType());
        // 默认办公用品
        order.setProductType(11);
        // 发票状态
        order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());
        // 生成订单号
//            String orderSn = UUID.randomUUID().toString().replace("-", "");
        String orderSn = OrderUtils.getOrder();
        order.setOrderSn(orderSn);
        boolean save = this.save(order);
        if (!save) {
            throw new BusinessException("订单保存失败！");
        }
        // 返回数据
        vo.setOrderId(order.getOrderId());
        vo.setOrderSn(order.getOrderSn());
        vo.setTotalAmount(order.getTotalAmount());
        vo.setUntitled(order.getUntitled());
        List<OrderItem> orderItems = new ArrayList<>();
        List<ProductSku> productSkus = new ArrayList<>();
        // 库存不存要下架的商品id
        List<String> outProductId = new ArrayList<>();
        for (BuySkuInfoDTO buySkuInfoDTO : buySkuInfoDTOS) {
            BigDecimal buyNum = buySkuInfoDTO.getBuyNum();
            ProductSku productSku = productSkuService.getProductSkuById(buySkuInfoDTO.getSkuId(), PublicEnum.STATE_OPEN.getCode());
            String skuId = productSku.getSkuId();
            // 订单项
            OrderItem orderItem = new OrderItem();
            orderItem.setUnit(productSku.getUnit());
            orderItem.setOrderId(order.getOrderId());
            orderItem.setOrderSn(order.getOrderSn());
            orderItem.setProductId(productSku.getProductId());
            orderItem.setProductSn(product.getSerialNum());
            orderItem.setProductName(product.getProductName().trim());
            // sku图片
            orderItem.setTaxRate(enterpriseInfo.getTaxRate());
            orderItem.setNoRatePrice(TaxCalculator.calculateNotTarRateAmount(productSku.getSellPrice(), enterpriseInfo.getTaxRate()));
            orderItem.setTotalAmount(productSku.getSellPrice().multiply(buyNum).setScale(2, RoundingMode.HALF_UP));
            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(productSku.getSellPrice(), enterpriseInfo.getTaxRate());
            BigDecimal noRateAmount = TaxCalculator.noTarRateItemAmount(orderItem.getTotalAmount(), noRatePrice, buyNum, taxRate);
//            orderItem.setNoRateAmount(noRatePrice.multiply(buyNum).setScale(2, RoundingMode.HALF_UP));
            orderItem.setNoRateAmount(noRateAmount);

            orderItem.setProductImg(productSku.getSkuImg());
            orderItem.setSkuId(productSku.getSkuId());
            orderItem.setRelevanceName(product.getRelevanceName().trim());
            orderItem.setRelevanceNo(product.getRelevanceNo());
            orderItem.setSkuName(productSku.getSkuName() == null ? null : productSku.getSkuName().trim());
            orderItem.setProductPrice(productSku.getSellPrice());
            orderItem.setBuyCounts(buyNum);
            orderItem.setProductType(product.getProductType());
            orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
            orderItem.setCostPrice(productSku.getCostPrice());
            orderItem.setOriginalPrice(productSku.getOriginalPrice());
            orderItem.setSupplierId(product.getSupperBy());
            if (productSku.getCostPrice() != null) {
                orderItem.setProfitPrice(productSku.getSellPrice().subtract(productSku.getCostPrice()));
            }
            orderItem.setClassId(product.getClassId());
            orderItem.setBrandId(product.getBrandId());
            orderItem.setBrandName(product.getBrandName());
            orderItems.add(orderItem);

            BigDecimal newStock = productSku.getStock().subtract(buyNum);
            ProductSku productSkuUpdate = new ProductSku();
            productSkuUpdate.setSkuId(skuId);
            productSkuUpdate.setStock(newStock);
            productSkus.add(productSkuUpdate);

            // 判断库存是否等于0如果等于0则下架商品
            if (newStock.compareTo(BigDecimal.ZERO) == 0) {
                outProductId.add(buySkuInfoDTO.getProductId());
            }
        }
        boolean f = orderItemService.saveBatch(orderItems);
        if (!f) {
            throw new BusinessException("订单保存失败！");
        }
        boolean b = productSkuService.updateBatchById(productSkus);
        if (!b) {
            throw new BusinessException("订单保存失败！");
        }
        // 下架商品
        if (!CollectionUtils.isEmpty(outProductId)) {
            UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
            updateProductStateDTO.setState(2);
            updateProductStateDTO.setProductIds(outProductId);
            productService.updateProductState(updateProductStateDTO);
        }
        return vo;
    }

    @Override
    public Orders findOrderByorderSn(String orderSn) {
        Orders orders = baseMapper.findOrderByorderSn(orderSn);
        return orders;
    }

    /**
     * 获取订单详情
     *
     * @param orderSn
     * @return
     */
    @Override
    public OrderDetailVO getOrderDetail(String orderSn) {
        OrderDetailVO vo = new OrderDetailVO();
        Orders orders = lambdaQuery().eq(Orders::getOrderSn, orderSn)
                .eq(Orders::getMallType, mallConfig.mallType).one();
        if (orders == null) return vo;
        BeanUtils.copyProperties(orders, vo);
        vo.setBillNo(orders.getPlanNo());
        Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, orders.getShopId())
                .select(Shop::getLinkMan, Shop::getContactNumber)
                .one();
        if (shop != null) {
            vo.setLinkMan(shop.getLinkMan());
            vo.setContactNumber(shop.getContactNumber());
        }
        List<OrderItem> orderItems = orderItemService.lambdaQuery().eq(OrderItem::getOrderSn, orderSn).list();
        if (CollectionUtils.isEmpty(orderItems)) return vo;
        List<OrderItemProductVO> productVOS = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            OrderItemProductVO productVO = new OrderItemProductVO();
            BeanUtils.copyProperties(orderItem, productVO);
            ProductSku sku = productSkuService.lambdaQuery()
                    .eq(ProductSku::getSkuId, productVO.getSkuId())
                    .select(ProductSku::getSkuName, ProductSku::getUnit)
                    .one();
            if (sku != null) {
                productVO.setSkuName(sku.getSkuName());
                productVO.setUnit(sku.getUnit());
                Product p = productService.lambdaQuery()
                        .eq(Product::getProductId, productVO.getProductId())
                        .select(Product::getBrandId).one();
                if (p != null) {
                    Brand b = brandService.lambdaQuery()
                            .eq(Brand::getBrandId, p.getBrandId())
                            .select(Brand::getName)
                            .one();
                    if (b != null) {
                        productVO.setBrandName(b.getName());
                    }
                }
            }

            productVOS.add(productVO);
        }
        vo.setOrderProduct(productVOS);

        List<OrderSelectContact> orderSelectContacts = orderSelectContactService.lambdaQuery()
                .eq(OrderSelectContact::getOrderSn, vo.getOrderSn()).list();
        vo.setOrderContactList(orderSelectContacts);
//        List<OrderSelectPlan> list = orderSelectPlanService.lambdaQuery().eq(OrderSelectPlan::getOrderSn, vo.getOrderSn()).list();
//        vo.setPlanInfoList(list);
        LambdaQueryChainWrapper<OrderSelectPlan> last = orderSelectPlanService.lambdaQuery()
                .eq(OrderSelectPlan::getOrderId, vo.getOrderId())
                .last("limit 1");
        if (last.list().size()>0) {
            OrderSelectPlan orderSelectPlan = last.list().get(0);
            if (orderSelectPlan.getProductType() == 10) {
                vo.setBillNo(orderSelectPlan.getBillNo());
            }
            if (orderSelectPlan.getProductType() == 12) {
                vo.setBillNo(orderSelectPlan.getContractNo());
            }
            if (orderSelectPlan.getProductType() == 13) {
                vo.setBillNo(orderSelectPlan.getBillNo());
            }
        }
//        OrderSelectPlan orderSelectPlan = orderSelectPlanService.lambdaQuery()
//                .eq(OrderSelectPlan::getOrderId, vo.getOrderId())
//                .last("limit 1")
//                .list().get(0);


        vo.setState(orders.getState());
        return vo;
    }

    /**
     * 新增物资订单
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderCreateResultVO createMaterialOrder(CreateMaterialOrderByCartIdsDTO dto) {
        UserLogin userLogin = ThreadLocalUtil.getCurrentUser();
        if (userLogin.getIsInterior() == 1) {
            Integer isSubmitOrder = ThreadLocalUtil.getCurrentUser().getIsSubmitOrder();
            if (isSubmitOrder == null || isSubmitOrder == 0) {
                throw new BusinessException(400, "没有下单权限！");
            }
        }
        List<String> cartIds = dto.getCartIds();
        if (CollectionUtils.isEmpty(cartIds)) {
            // 直接购买
            return this.createMaterialOrderByproduct(dto);
        } else {
            List<ShoppingCartShopIdVO> cartVOS = shoppingCartMapper.listShopIdAndCartByCartIds(
                    dto.getCartIds(), ThreadLocalUtil.getCurrentUser().getUserId());

            // 为每个购物车VO赋值收货人信息
            for (ShoppingCartShopIdVO cartVO : cartVOS) {
                cartVO.setReceiverName(dto.getReceiverName());
                cartVO.setReceiverMobile(dto.getReceiverMobile());
                cartVO.setReceiverAddress(dto.getReceiverAddress());
            }

            //购物车购买-立即下单
            List<Orders> createdOrders = null;
            Plan createdPlan = null;
            int orderStatus;//订单是否生效
            if (userLogin.getIsInterior() == 0) {//外部用户

                orderStatus = 0;//订单立即生效

                // 1. 校验购物车和商品信息
                cartValidationUtils.validateCompleteCart(cartVOS);

                // 2. 生成计划表
                createdPlan = planServiceImpl.createMaterialPlanByCartIds(cartVOS);

                // 3. 生成订单
                createdOrders = createMaterialOrderByCartIds(cartVOS, createdPlan,orderStatus);
            } else {//内部用户直接下单，只生成计划，TT通知审核人员

                // 1. 校验购物车和商品信息
                cartValidationUtils.validateCompleteCart(cartVOS);

                // 2. 生成计划表（内部用户只生成计划，不直接下单）
                createdPlan = planServiceImpl.createMaterialPlanByCartIds(cartVOS);

                orderStatus = -1;//个人中心计划审核通过后，订单生效

                // 3. 生成订单
                createdOrders = createMaterialOrderByCartIds(cartVOS, createdPlan,orderStatus);
            }

            // 购物车立即下订单后置处理
            processOrderPostActions(cartVOS);
            // 4.推送TT代
            ttRabbitMQConnectionTest.testConnection();
//            String EMPLOYEE_NUMBER = "036529";
//            String USER_ID = "391E2FB8-F295-4045-8CDC-340AD3DE6700";
//            // 4-1. 创建待办消息体
//            ToDoMessageBody todo = TTToDoRabbitMQUtil.createTodoBody(
//                    "计划编号"+createdPlan.getPBillId(), // 待办唯一ID
//                    EMPLOYEE_NUMBER,                                 // 员工号：036529
//                    USER_ID,                                        // TT用户ID
//                    "物资采购计划审核",                               // 标题
//                    "物资采购计划审核",                     // 描述信息
//                    ""                              // 跳转URL
//            );
//
//            // 4-2. 推送TT代办
//            TTToDoRabbitMQUtil.sendSingleToDo(todo);
//
//            // 返回提示信息
            OrderCreateResultVO result = new OrderCreateResultVO();
//            // 计算总金额
//            BigDecimal totalAmount = cartValidationUtils.calculateCartTotalAmount(cartVOS);
//            result.setTotalAmount(totalAmount);
//
//            // 获取商品名称拼接
//            Map<String, String> untitledMap = cartValidationUtils.validateCartItems(cartVOS);
//            StringBuilder allUntitled = new StringBuilder();
//            for (String untitled : untitledMap.values()) {
//                if (allUntitled.length() > 0) {
//                    allUntitled.append(",");
//                }
//                allUntitled.append(untitled);
//            }
//            result.setUntitled(allUntitled.toString());
//
//            // 获取商品类型（从购物车中获取实际类型）
//            Integer productType = getProductTypeFromCart(cartVOS);
//            result.setProductType(productType);
//            // 设置订单信息（如果创建了订单）
//            if (createdOrders != null && !createdOrders.isEmpty()) {
//                // 如果有多个订单，返回第一个订单的信息
//                Orders firstOrder = createdOrders.get(0);
//                result.setOrderId(firstOrder.getOrderId());
//                result.setOrderSn(firstOrder.getOrderSn());
//            }
            return result;
        }
    }

    /**
     * 从购物车中获取商品类型
     *
     * @param cartVOS 购物车信息列表
     * @return 商品类型
     */
    private Integer getProductTypeFromCart(List<ShoppingCartShopIdVO> cartVOS) {
        if (CollectionUtils.isEmpty(cartVOS)) {
            return 11; // 默认为办公用品
        }

        for (ShoppingCartShopIdVO cartVO : cartVOS) {
            List<ShoppingCart> shoppingCarts = cartVO.getShoppingCarts();
            if (!CollectionUtils.isEmpty(shoppingCarts)) {
                // 返回第一个商品的类型
                return shoppingCarts.get(0).getProductType();
            }
        }

        return 11; // 默认为办公用品
    }

    /**
     * 购物车立即下订单后置处理方法
     */
    @Transactional(rollbackFor = Exception.class)
    public void processOrderPostActions(List<ShoppingCartShopIdVO> cartVOS) {

        List<String> cartIds = new ArrayList<>();
        ArrayList<ProductSku> productSkus = new ArrayList<>();
        List<String> outProductIds = new ArrayList<>();
        try {
            for (ShoppingCartShopIdVO cartVO : cartVOS) {
                // 获取店铺和供应商信息
                for (ShoppingCart cart : cartVO.getShoppingCarts()) {
                    ProductSku productSku = productSkuService.getById(cart.getSkuId());
                    // 减去库存
                    String skuId = cart.getSkuId();
                    BigDecimal newStock = productSku.getStock().subtract(cart.getCartNum());
                    ProductSku productSku2 = new ProductSku();
                    productSku2.setSkuId(skuId);
                    productSku2.setStock(newStock);
                    productSkus.add(productSku2);

                    // 判断库存是否等于0如果等于0则下架商品
                    if (newStock.compareTo(BigDecimal.ZERO) == 0) {
                        outProductIds.add(cart.getProductId());
                    }
                    productSkus.add(productSku);
                    cartIds.add(cart.getCartId());
                }
            }

            // 1. 批量更新库存
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(productSkus)) {
                boolean updateResult = productSkuService.updateBatchById(productSkus);
                if (!updateResult) {
                    throw new BusinessException(400, "订单保存失败!");
                }
            }

            // 2. 清理购物车
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(cartIds)) {
                shoppingCartMapper.removeRealByIds(cartIds);
            }

            // 3. 下架商品
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(outProductIds)) {
                UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
                updateProductStateDTO.setState(2); // 2表示下架状态
                updateProductStateDTO.setProductIds(outProductIds);
                productService.updateProductState(updateProductStateDTO);
            }

        } catch (Exception e) {
            log.error("订单后置处理失败", e);
            throw new BusinessException(500, "订单后置处理失败: " + e.getMessage());
        }
    }


    /**
     * 新增装备订单
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object createDeviceOrder(CreateMaterialOrderByCartIdsDTO dto) {
//        Integer isInterior = ThreadLocalUtil.getCurrentUser().getIsInterior();
//        if (isInterior == 1) {
//            Integer isSubmitOrder = ThreadLocalUtil.getCurrentUser().getIsSubmitOrder();
//            if (isSubmitOrder == null || isSubmitOrder == 0) {
//                throw new BusinessException(400, "没有下单权限！");
//            }
//        }
//        List<String> cartIds = dto.getCartIds();
//        if (CollectionUtils.isEmpty(cartIds)) {
//            return this.createDeviceOrderByproduct(dto);
//        } else {
//            return this.createDeviceOrderByCartIds(dto);
//        }
        return null;
    }

    /**
     * 获取操作计划订单详情
     *
     * @param orderSn
     * @return
     */
    @Override
    public List<OrderPlanDetail> getOrderPlanDetail(String orderSn) {
        ArrayList<OrderPlanDetail> vos = new ArrayList<>();
        List<OrderItem> orderItems = orderItemService.lambdaQuery().eq(OrderItem::getOrderSn, orderSn).list();
        if (CollectionUtils.isEmpty(orderItems)) {
            return vos;
        }
        for (OrderItem orderItem : orderItems) {
            OrderPlanDetail vo = new OrderPlanDetail();
            BeanUtils.copyProperties(orderItem, vo);
            Product one = productService.lambdaQuery().eq(Product::getProductId, vo.getProductId())
                    .select(Product::getRelevanceName, Product::getShopId).one();
            if (one != null) {
                vo.setRelevanceName(one.getRelevanceName());
            }
            // 查询出店铺对应的供应商信用代码
            String shopId = one.getShopId();
            Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, shopId)
                    .select(Shop::getEnterpriseId).one();
            if (shop == null) {
                throw new BusinessException(400, "店铺不存在！");
            }
            vo.setShopId(shopId);
            EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, shop.getEnterpriseId())
                    .select(EnterpriseInfo::getSocialCreditCode, EnterpriseInfo::getEnterpriseName).one();
            vo.setSocialCreditCode(enterpriseInfo.getSocialCreditCode());
            vo.setOrgName(enterpriseInfo.getEnterpriseName());
            vos.add(vo);
        }
        return vos;
    }

    /**
     * 根据参数分页查询订单信息（店铺）
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils<Orders> listShopManageOrdersByParameters(JSONObject jsonObject, LambdaQueryWrapper<Orders> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        Integer state = (Integer) innerMap.get("state");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        String enterpriseName = (String) innerMap.get("enterpriseName");
        String startDate = (String) innerMap.get("startDate");
        String parentOrderId = (String) innerMap.get("parentOrderId");
        String endDate = (String) innerMap.get("endDate");
        String okStartDate = (String) innerMap.get("okStartDate");
        String okEndDate = (String) innerMap.get("okEndDate");
        String deliverGoodsStartDate = (String) innerMap.get("deliverGoodsStartDate");
        String deliverGoodsEndDate = (String) innerMap.get("deliverGoodsEndDate");
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        String startSuccessDate = (String) innerMap.get("startSuccessDate");
        String endSuccessDate = (String) innerMap.get("endSuccessDate");
        Integer productType = (Integer) innerMap.get("productType");
        String shopName = (String) innerMap.get("shopName");
        String untitled = (String) innerMap.get("untitled");
        String orderSn = (String) innerMap.get("orderSn");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        Boolean isQueryTwoOrder = (Boolean) innerMap.get("isQueryTwoOrder");
        Boolean showAll = (Boolean) innerMap.get("showAll");

        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        q.eq(showAll!=null&&(!showAll), Orders::getAssigneeId, userId);
        // 只查询一级订单
        if (isQueryTwoOrder == null || !isQueryTwoOrder) {
            q.isNull(Orders::getParentOrderId);
        }
        // 默认订单是通过店铺id查询，如果是大宗使用本地机构id
        // TODO 这里不知道为什么
        if (productType != null && productType == 1) {
            q.eq(Orders::getSupplierId, ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        } else {
            q.eq(Orders::getShopId, ThreadLocalUtil.getCurrentUser().getShopId());
        }
        q.between(StringUtils.isNotEmpty(startSuccessDate) && StringUtils.isNotEmpty(endSuccessDate), Orders::getSuccessDate, startSuccessDate, endSuccessDate);
        q.like(StringUtils.isNotEmpty(shopName), Orders::getShopName, shopName);
        q.like(StringUtils.isNotEmpty(untitled), Orders::getUntitled, untitled);
        q.like(StringUtils.isNotEmpty(orderSn), Orders::getOrderSn, orderSn);
        q.like(StringUtils.isNotEmpty(enterpriseName), Orders::getEnterpriseName, enterpriseName);
        q.eq(StringUtils.isNotEmpty(parentOrderId), Orders::getParentOrderId, parentOrderId);
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(Orders::getOrderSn, keywords)
                        .or()
                        .like(Orders::getUntitled, keywords)
                        .or()
                        .like(Orders::getShopName, keywords)
                        .or()
                        .like(Orders::getEnterpriseName, keywords)
                        .or()
                        .like(Orders::getReceiverMobile, keywords);
            });
        }
        q.ge(StringUtils.isNotBlank(abovePrice), Orders::getActualAmount, abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), Orders::getActualAmount, belowPrice);
        q.eq(orderClass != null, Orders::getOrderClass, orderClass);
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), Orders::getGmtCreate, startDate, endDate);
        q.between(StringUtils.isNotEmpty(okStartDate) && StringUtils.isNotEmpty(okEndDate), Orders::getFlishTime, okStartDate, okEndDate);
        q.between(StringUtils.isNotEmpty(deliverGoodsStartDate) && StringUtils.isNotEmpty(deliverGoodsEndDate), Orders::getDeliveryTime, deliverGoodsStartDate, deliverGoodsEndDate);
        q.eq(state != null, Orders::getState, state);
        if (productType != null) {
            q.eq( Orders::getProductType, productType);
        }else {
            // 防止脏数据
            q.in( Orders::getProductType, 0,1,2);
        }
        q.eq(Orders::getMallType, mallConfig.mallType);
        q.eq(Orders::getIsDelete, PublicEnum.IS_DELETE_NO.getCode());
        if (orderBy == null) {
            orderBy = 1;
        }
        if (orderBy == 1) {
            q.orderByDesc(Orders::getGmtCreate);
        }
        if (orderBy == 2) {
            q.orderByDesc(Orders::getFlishTime);
        }
        if (orderBy == 3) {
            q.orderByDesc(Orders::getDeliveryTime);
        }
        IPage<Orders> page = this.page(
                new Query<Orders>().getPage(jsonObject),
                q
        );
        List<Orders> records = page.getRecords();
        if (records != null && records.size() > 0) {
            for (Orders record : records) {
                if (record.getOrderClass() == 3) {
                    Orders one = lambdaQuery().eq(Orders::getOrderId, record.getParentOrderId()).select(Orders::getSupplierName).one();
                    record.setTwoEnterpriseName(one.getSupplierName());
                } else {
                    record.setTwoEnterpriseName(record.getEnterpriseName());
                }
                //获取二级供应商项目部的名称（物资公司）

            }
        }

        return new PageUtils<>(page);
    }

    /**
     * 查询我的物流订单
     *
     * @param jsonObject
     * @return
     */
    @Override
    public PageUtils getUserLogisticsOrder(JSONObject jsonObject) {
        LambdaQueryWrapper<Orders> q = Wrappers.lambdaQuery(Orders.class);
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        q.eq(Orders::getEnterpriseId, enterpriseId);
//        q.eq(Orders::getUserId, ThreadLocalUtil.getCurrentUser().getUserId());
//        q.eq(Orders::getState, OrderEnum.STATE_YES_FREIGHT.getCode());
        q.in(Orders::getState, 8, 9);
        q.orderByDesc(Orders::getDeliveryTime);
        q.eq(Orders::getMallType, mallConfig.mallType);
        IPage<Orders> page = this.page(
                new Query<Orders>().getPage(jsonObject),
                q
        );
        List<Orders> records = page.getRecords();
        if (records.size() == 0) return new PageUtils(page);
        List<LogisticsOrderVO> vos = new ArrayList<>();
        for (Orders orders : records) {
            LogisticsOrderVO vo = new LogisticsOrderVO();
            BeanUtils.copyProperties(orders, vo);
            OrderItem oi = orderItemService.lambdaQuery()
                    .eq(OrderItem::getOrderId, vo.getOrderId())
                    .select(OrderItem::getProductImg).last("limit 1").one();
            if (oi != null) {
                vo.setProductImg(oi.getProductImg());
            }
            vos.add(vo);
        }
        PageUtils pageUtils = new PageUtils(page);
        pageUtils.setList(vos);
        return pageUtils;
    }

    /**
     * 据参数分页查询订单信息（平台订单统计）
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils getPlatformOrdersCount(JSONObject jsonObject, LambdaQueryWrapper<Orders> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();

        String keywords = (String) innerMap.get("keywords");
        String date = (String) innerMap.get("date");
        String orderCountOrProfitPriceTotal = (String) innerMap.get("orderCountOrProfitPriceTotal");

        Integer isShop = (Integer) innerMap.get("isShop");
        Integer productType = (Integer) innerMap.get("productType");

        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");

        // 查询条件：排除订单类型为3的订单
        q.ne(Orders::getOrderClass, 3);
        innerMap.put("orderClass", 3);

        // 店铺筛选逻辑
        if (isShop != null && isShop == 1) {
            String shopId = (String) innerMap.get("shopId");
            if (StringUtils.isNotBlank(shopId)) {
                q.eq(Orders::getShopId, shopId);
                innerMap.put("shopId", shopId);
            } else {
                String currentShopId = ThreadLocalUtil.getCurrentUser().getShopId();
                q.eq(Orders::getShopId, currentShopId);
                innerMap.put("shopId", currentShopId);
            }
        }

        // 商品类型筛选
        if (productType != null) {
            q.eq(Orders::getProductType, productType);
            innerMap.put("productType", productType);
        }

        // 模糊搜索
        if (StringUtils.isNotBlank(keywords)) {
            q.and(t -> t.like(Orders::getOrderSn, keywords)
                    .or().like(Orders::getUntitled, keywords)
                    .or().like(Orders::getEnterpriseName, keywords)
                    .or().like(Orders::getSupplierName, keywords)
                    .or().like(Orders::getShopName, keywords));
            innerMap.put("keywords", keywords);
        }

        // 时间范围筛选
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            q.between(Orders::getFlishTime, startDate, endDate);
        }

        // 固定条件
        q.eq(Orders::getMallType, mallConfig.mallType);
        q.eq(Orders::getState, 10);
        innerMap.put("mallType", mallConfig.mallType);

        // 分页查询
        IPage<Orders> page = this.page(new Query<Orders>().getPage(jsonObject), q);
        List<Orders> records = page.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            return new PageUtils(page);
        }

        // 根据时间维度处理数据
        processDateDimension(records, date, orderCountOrProfitPriceTotal, innerMap);

        return new PageUtils(page);
    }
    @Override
    public void getPlatformOrdersCountExcel(JSONObject jsonObject, HttpServletResponse response) {
        LambdaQueryWrapper<Orders> q = Wrappers.lambdaQuery(Orders.class);
        Map<String, Object> innerMap = jsonObject.getInnerMap();

        String keywords = (String) innerMap.get("keywords");
        String date = (String) innerMap.get("date");
        String orderCountOrProfitPriceTotal = (String) innerMap.get("orderCountOrProfitPriceTotal");

        Integer isShop = (Integer) innerMap.get("isShop");
        Integer productType = (Integer) innerMap.get("productType");

        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");

        // 查询条件：排除订单类型为3的订单
        q.ne(Orders::getOrderClass, 3);
        innerMap.put("orderClass", 3);

        // 店铺筛选逻辑
        if (isShop != null && isShop == 1) {
            String shopId = (String) innerMap.get("shopId");
            if (StringUtils.isNotBlank(shopId)) {
                q.eq(Orders::getShopId, shopId);
                innerMap.put("shopId", shopId);
            } else {
                String currentShopId = ThreadLocalUtil.getCurrentUser().getShopId();
                q.eq(Orders::getShopId, currentShopId);
                innerMap.put("shopId", currentShopId);
            }
        }

        // 商品类型筛选
        if (productType != null) {
            q.eq(Orders::getProductType, productType);
            innerMap.put("productType", productType);
        }

        // 模糊搜索
        if (StringUtils.isNotBlank(keywords)) {
            q.and(t -> t.like(Orders::getOrderSn, keywords)
                    .or().like(Orders::getUntitled, keywords)
                    .or().like(Orders::getEnterpriseName, keywords)
                    .or().like(Orders::getSupplierName, keywords)
                    .or().like(Orders::getShopName, keywords));
            innerMap.put("keywords", keywords);
        }

        // 时间范围筛选
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            q.between(Orders::getFlishTime, startDate, endDate);
        }

        // 固定条件
        q.eq(Orders::getMallType, mallConfig.mallType);
        q.eq(Orders::getState, 10);
        innerMap.put("mallType", mallConfig.mallType);
        List<Orders> orders = baseMapper.selectList(q);
        if(orders.isEmpty()){

        }
        for (Orders order : orders){
            order.setProductTypeStr(getProductTypeName(order.getProductType()));
            order.setGmtCreateStr(getDateStr(order.getGmtCreate()));
            order.setSuccessDateStr(getDateStr(order.getSuccessDate()));
            order.setNoRateCostPriceTotalStr(getNoRate(order.getCostPriceTotal(), order.getTaxRate()));
            order.setNoRateActualAmountStr(getNoRate(order.getActualAmount(), order.getTaxRate()));
            order.setNoRateProfitPriceTotalStr(getNoRate(order.getProfitPriceTotal(), order.getTaxRate()));
        }
        // 根据时间维度处理数据
        //processDateDimension(orders, date, orderCountOrProfitPriceTotal, innerMap);
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", orders);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "平台订单统计分析模板（订单号维度）.xlsx", src, "平台订单统计分析（订单号维度）.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }

    }
    private String getProductTypeName(Integer productType) {
        if (productType == null) {
            return "未知类型";
        }
        switch (productType) {
            case 0:
                return "零星采购";
            case 1:
                return "大宗临购";
            case 2:
                return "周材材料";
            default:
                return "未知类型";
        }
    }
    private String getDateStr(Date date) {
        if(date == null){
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
    private String getNoRate(BigDecimal amount,BigDecimal taxRate) {
        if (amount == null || taxRate == null){
            return null;
        }else {
            // 将税率百分比转换为小数形式（3 -> 0.03）
            BigDecimal taxRateDecimal = taxRate.divide(BigDecimal.valueOf(100), 6, BigDecimal.ROUND_HALF_UP);
            // 计算不含税金额：含税金额 / (1 + 税率)
            BigDecimal divisor = BigDecimal.ONE.add(taxRateDecimal);
            BigDecimal noRateAmount = amount.divide(divisor, 2, BigDecimal.ROUND_HALF_UP);
            return String.valueOf(noRateAmount);
        }
    }
    private void processDateDimension(List<Orders> records, String dateType, String orderCountOrProfitPriceTotal, Map<String, Object> innerMap) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        if ("week".equals(dateType)) {
            List<LocalDate> last7Days = getLastNDays(7);
            List<Integer> orderCounts = new ArrayList<>();
            List<BigDecimal> profitTotals = new ArrayList<>();
            List<String> labels = new ArrayList<>();

            for (LocalDate date : last7Days) {
                String startOfDay = date.atStartOfDay().format(formatter);
                String endOfDay = date.atTime(23, 59, 59).format(formatter);
                labels.add(date.format(DateTimeFormatter.ofPattern("MM月dd日")));

                if ("orderCount".equals(orderCountOrProfitPriceTotal)) {
                    orderCounts.add(getOrderCountList(startOfDay, endOfDay, innerMap));
                } else {
                    profitTotals.add(getOrderProfitPriceTotal(startOfDay, endOfDay, innerMap));
                }
            }

            Orders firstRecord = records.get(0);
            firstRecord.setLabelTitle(labels);
            if (!orderCounts.isEmpty()) {
                firstRecord.setCount(orderCounts);
            } else {
                firstRecord.setProfitPriceTotals(profitTotals);
            }

        } else if ("month".equals(dateType)) {
            List<String> monthRanges = getRecentWeekRangesWithTime(5); // 示例：获取最近5周
            List<Integer> orderCounts = new ArrayList<>();
            List<BigDecimal> profitTotals = new ArrayList<>();
            List<String> labels = new ArrayList<>();

            for (String range : monthRanges) {
                String[] parts = range.split("~");
                String start = parts[0].split(" ")[0];
                String end = parts[1].split(" ")[0];

                labels.add(start + "~" + end);

                if ("orderCount".equals(orderCountOrProfitPriceTotal)) {
                    orderCounts.add(getOrderCountList(start, end, innerMap));
                } else {
                    profitTotals.add(getOrderProfitPriceTotal(start, end, innerMap));
                }
            }

            Orders firstRecord = records.get(0);
            firstRecord.setLabelTitle(labels);
            if (!orderCounts.isEmpty()) {
                firstRecord.setCount(orderCounts);
            } else {
                firstRecord.setProfitPriceTotals(profitTotals);
            }

        } else if ("year".equals(dateType)) {
            List<String> yearRanges = getRecentMonths(12);
            List<Integer> orderCounts = new ArrayList<>();
            List<BigDecimal> profitTotals = new ArrayList<>();
            List<String> labels = new ArrayList<>();

            for (String range : yearRanges) {
                String[] parts = range.split("~");
                String start = parts[0];
                String end = parts[1];

                LocalDate startDate = LocalDate.parse(start, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String label = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));

                labels.add(label);

                if ("orderCount".equals(orderCountOrProfitPriceTotal)) {
                    orderCounts.add(getOrderCountList(start, end, innerMap));
                } else {
                    profitTotals.add(getOrderProfitPriceTotal(start, end, innerMap));
                }
            }

            Orders firstRecord = records.get(0);
            firstRecord.setLabelTitle(labels);
            if (!orderCounts.isEmpty()) {
                firstRecord.setCount(orderCounts);
            } else {
                firstRecord.setProfitPriceTotals(profitTotals);
            }
        }
    }
    private List<LocalDate> getLastNDays(int n) {
        LocalDate today = LocalDate.now();
        List<LocalDate> dates = new ArrayList<>();
        for (int i = n - 1; i >= 0; i--) {
            dates.add(today.minusDays(i));
        }
        return dates;
    }
    private Integer getOrderCountList(String startDate,String entDate, Map<String, Object> innerMap) {
        innerMap.remove("startDate");
        innerMap.remove("endDate");
        innerMap.put("startDate", startDate);
        innerMap.put("endDate", entDate);
        return baseMapper.getPlatformWeekOrdersCount(innerMap);
    }
    private BigDecimal getOrderProfitPriceTotal(String startDate,String entDate, Map<String, Object> innerMap) {
        innerMap.remove("startDate");
        innerMap.remove("endDate");
        innerMap.put("startDate", startDate);
        innerMap.put("endDate", entDate);
        return baseMapper.getPlatformWeekOrdersProfitPriceTotal(innerMap);
    }
    /**
     * 获取从当前日期往前推 n 个月的月份列表
     * @param monthsBack 要往前推的月数
     * @return 月份列表，格式为 "yyyy年MM月"
     */
    private List<String> getRecentMonths(int monthsBack) {
        List<String> monthRanges = new ArrayList<>();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 获取当前时间
        LocalDate currentDate = LocalDate.now();

        for (int i = 0; i < monthsBack; i++) {
            // 计算当前往前推 i 个月的日期
            LocalDate date = currentDate.minusMonths(i);

            // 获取该月的第一天
            LocalDate startOfMonth = date.with(TemporalAdjusters.firstDayOfMonth());
            // 获取该月的最后一天
            LocalDate endOfMonth = date.with(TemporalAdjusters.lastDayOfMonth());

            // 设置时间为当天的开始和结束
            LocalDateTime startOfDay = startOfMonth.atStartOfDay(); // 00:00:00
            LocalDateTime endOfDay = endOfMonth.atTime(23, 59, 59); // 23:59:59

            // 格式化为字符串
            String monthRange = startOfDay.format(dateTimeFormatter) + "~" + endOfDay.format(dateTimeFormatter);
            monthRanges.add(monthRange);
        }

        return monthRanges;
    }
    /**
     * 获取从当前日期往前推 n 周的每周起止日期段
     * @param weeksBack 要往前推的周数
     * @return 每周的起止日期段列表
     */
    private List<String> getRecentWeekRangesWithTime(int weeksBack) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        List<String> weekRanges = new ArrayList<>();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 获取每周的起始（周一 00:00:00）和结束时间（周日 23:59:59）
        WeekFields weekFields = WeekFields.of(Locale.getDefault());

        for (int i = weeksBack; i > 0; i--) {
            // 计算当前周往前推 i 周的日期
            LocalDate date = currentDate.minusWeeks(i);

            // 获取该周的周一
            LocalDate startOfWeek = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            // 获取该周的周日
            LocalDate endOfWeek = date.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

            // 转换为当天的开始时间和结束时间
            LocalDateTime startOfDay = startOfWeek.atStartOfDay(); // 00:00:00
            LocalDateTime endOfDay = endOfWeek.atTime(23, 59, 59); // 23:59:59

            // 格式化为 "yyyy-MM-dd HH:mm:ss ~ yyyy-MM-dd HH:mm:ss"
            String weekRange = startOfDay.format(dateTimeFormatter) + "~" + endOfDay.format(dateTimeFormatter);
            weekRanges.add(weekRange);
        }

        return weekRanges;
    }
    /**
     * 结算其他服务
     *
     * @param productId
     * @return
     */
    @Override
    public SettleRepairVO settleRepair(String productId) {
        SettleRepairVO vo = new SettleRepairVO();
        // 查询商品信息
        Product product = productService.lambdaQuery().eq(Product::getProductId, productId)
                .select(Product.class, f -> {
                    return !f.getProperty().equals("productDescribe");
                }).one();
        if (product == null) return vo;
        BeanUtils.copyProperties(product, vo);
        ProductAttributeValue productAttributeValue = productAttributeValueService.lambdaQuery()
                .eq(ProductAttributeValue::getProductId, productId).one();
        if (productAttributeValue != null) {
            String attributeValue = productAttributeValue.getAttributeValue();
            CreateRepairAttrValueDTO data = JSONObject.parseObject(attributeValue, CreateRepairAttrValueDTO.class);
            BeanUtils.copyProperties(data, vo);
        }
        vo.setNumTotalPrice(product.getProductMinPrice());
        vo.setTotalPrice(product.getProductMinPrice());
        return vo;
    }

    /**
     * 新增维修服务订单
     *
     * @param dto
     */
    @Override
    public void createRepairOrder(CreateRepairOrderDTO dto) {
//        String productId = dto.getProductId();
//        // 实际总金额
//        BigDecimal actualAmount = null;
//        // 订单总金额
//        BigDecimal totalAmount = null;
//        Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
//        if (product == null) {
//            Product byId = productService.getProductExcludeRemarkById(productId);
//            if (byId != null) {
//                throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
//            } else {
//                throw new BusinessException("商品不存在！");
//            }
//        } else {
//            //获取每个商品名称，以,分割，拼接成字符串
//            actualAmount = product.getProductMinPrice();
//            totalAmount = product.getProductMinPrice();
//            if (Math.abs(dto.getPayPrice().subtract(actualAmount).doubleValue()) >= 0.01) {
//                // 验价不通过
//                throw new BusinessException("订单商品价格发生变化，请确认后再次提交!");
//            }
//            Orders order = new Orders();
//            order.setEnterpriseId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
//            order.setOrderRemark(dto.getOrderRemark());
//            // 店铺名称
//            order.setShopId(product.getShopId());
//            String shopName = shopService.getShopNameById(product.getShopId());
//            order.setShopName(shopName);
//            // 用户ID
//            String userId = ThreadLocalUtil.getCurrentUser().getUserId();
//            order.setUserId(userId);
//            order.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
//            // 商品名称
//            order.setUntitled(product.getProductName());
//            // 设置总金额
//            order.setTotalAmount(totalAmount);
//            order.setActualAmount(actualAmount);
//
//            order.setState(OrderEnum.STATE_FINISH.getCode());
//            order.setFlishTime(new Date());
//
//            order.setProductType(product.getProductType());
//            // 发票状态
//            order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());
//            // 生成订单号
//            String orderSn = OrderUtils.getOrder();
//            order.setOrderSn(orderSn);
//            boolean save = this.save(order);
//            if (!save) {
//                throw new BusinessException("订单保存失败！");
//            }
//            // 订单项
//            OrderItem orderItem = new OrderItem();
//            orderItem.setOrderId(order.getOrderId());
//            orderItem.setOrderSn(order.getOrderSn());
//            orderItem.setProductId(productId);
//            orderItem.setProductSn(product.getSerialNum());
//            orderItem.setProductName(product.getProductName());
//            orderItem.setRelevanceName(product.getRelevanceName().trim());
//            orderItem.setProductImg(product.getProductMinImg());
//            orderItem.setSkuName(product.getProductName());
//            orderItem.setProductPrice(product.getProductMinPrice());
//            orderItem.setBuyCounts(new BigDecimal(1));
//            orderItem.setProductType(product.getProductType());
//            orderItem.setTotalAmount(totalAmount);
//            orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
//            List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(product.getClassId());
//            String classPath = "";
//            if (CollectionUtils.isEmpty(categoryParentPath)) {
//                throw new BusinessException("分类不存在！");
//            } else {
//                if (categoryParentPath.size() == 1) {
//                    orderItem.setClassPathName(categoryParentPath.get(0).getClassName());
//                } else {
//                    for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
//                        if (!org.springframework.util.StringUtils.isEmpty(categoryClassIdAndClassNameVO.getClassName())) {
//                            classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
//                        }
//                    }
//                    orderItem.setClassPathName(classPath.substring(0, classPath.length() - 1));
//                }
//            }
//            boolean f = orderItemService.save(orderItem);
//            if (!f) {
//                throw new BusinessException("订单保存失败！");
//            }
//        }
    }

    /**
     * 批量修改
     *
     * @param orders
     */
    @Override
    public void updateBatch(List<Orders> orders) {
        for (Orders order : orders) {
            Orders byId = getById(order.getOrderId());
            if (byId != null) {
                BeanCopyIgnoreNullUtils.copyPropertiesIgnoreNull(order, byId);
                if (!StringUtils.isEmpty(order.getDeliveryFlowId())) {
                    byId.setDeliveryTime(new Date());
                }
                updateById(byId);
            }
        }
    }


    /**
     * 直接下单
     *
     * @param dto
     */
//    public OrderCreateResultVO createDeviceOrderByproduct(@NotNull CreateMaterialOrderByCartIdsDTO dto) {
//        String productId = dto.getProductId();
//        OrderCreateResultVO vo = new OrderCreateResultVO();
//        String untitled = "";
//        // 实际总金额
//        BigDecimal actualAmount = new BigDecimal(0);
//        // 订单总金额
//        BigDecimal totalAmount = new BigDecimal(0);
//        // 总成本价
//        BigDecimal costPriceTotal = new BigDecimal(0);
//        Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
//        if (product == null) {
//            Product byId = productService.getProductExcludeRemarkById(productId);
//            if (byId != null) {
//                throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
//            } else {
//                throw new BusinessException("商品不存在！");
//            }
//        } else {
//            // 商品存在
//            List<BuySkuInfoDTO> buySkuInfoDTOS = dto.getBuySkuInfoDTOS();
//            for (BuySkuInfoDTO buySkuInfoDTO : buySkuInfoDTOS) {
//                // 查询已上架未删除
//                ProductSku productSku = productSkuService.getProductSkuById(buySkuInfoDTO.getSkuId(), PublicEnum.STATE_OPEN.getCode());
//                if (productSku == null) {
//                    // 直接查询
//                    ProductSku byId = productSkuService.getById(buySkuInfoDTO.getSkuId());
//                    if (byId != null) {
//                        throw new BusinessException("商品名为：【" + product.getProductName() + "】商品的【" + byId.getSkuName() + "】规格已下架或已被删除！");
//                    } else {
//                        throw new BusinessException("商品不存在！");
//                    }
//                } else {
//                    // 如果库存不足
//                    int i = buySkuInfoDTO.getBuyNum().compareTo(productSku.getStock());
//                    if (i == 1) {
//                        throw new BusinessException("【" + product.getProductName() + "】商品的【" + productSku.getSkuName() + "】库存不足!");
//                    }
//                    //获取每个商品名称，以,分割，拼接成字符串
//                    untitled = untitled + product.getProductName() + ",";
//                    actualAmount = actualAmount.add(productSku.getSellPrice().multiply(buySkuInfoDTO.getBuyNum()).multiply(buySkuInfoDTO.getLeaseNum()));
//                    if (productSku.getOriginalPrice() != null) {
//                        totalAmount = totalAmount.add(productSku.getOriginalPrice().multiply(buySkuInfoDTO.getBuyNum()).multiply(buySkuInfoDTO.getLeaseNum()));
//                    }
//                    if (productSku.getCostPrice() != null) {
//                        costPriceTotal = costPriceTotal.add(productSku.getCostPrice().multiply(buySkuInfoDTO.getBuyNum()).multiply(buySkuInfoDTO.getLeaseNum()));
//                    }
//                }
//            }
//            // 验价
//            if (Math.abs(dto.getPayPrice().subtract(actualAmount).doubleValue()) >= 0.01) {
//                // 验价不通过
//                throw new BusinessException("订单商品价格发生变化，请确认后再次提交!");
//            }
//
//            Orders order = new Orders();
//            order.setEnterpriseId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
////            Integer userType = 0;
////            Integer shopType = 1;
////            Integer shopSupport = 1;
////            dealTypeAndPayWay(order, userType, shopType, shopSupport);
//
//            // 暂时都是前端传入内部结算
//            BeanUtils.copyProperties(dto, order);
//            // 店铺名称
//            order.setShopId(product.getShopId());
//            String shopName = shopService.getShopNameById(product.getShopId());
//            order.setShopName(shopName);
//            // 用户ID
//            String userId = ThreadLocalUtil.getCurrentUser().getUserId();
//            order.setUserId(userId);
//            order.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
//            // 商品名称拼接
//            order.setUntitled(untitled.substring(0, untitled.length() - 1));
//            // 设置总金额
//            order.setTotalAmount(totalAmount);
//            order.setActualAmount(actualAmount);
//            order.setCostPriceTotal(costPriceTotal);
//            order.setProfitPriceTotal(actualAmount.subtract(costPriceTotal));
//
//            if (ThreadLocalUtil.getCurrentUser().getIsInterior() == 1) {
//                order.setState(OrderEnum.STATE_DRAFT.getCode());
//            } else {
//                order.setState(OrderEnum.STATE_FINISH.getCode());
//            }
//
//            order.setOrderFreight(new BigDecimal("0"));
//            order.setProductType(product.getProductType());
//            // 发票状态
//            order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());
//            // 生成订单号
////            String orderSn = UUID.randomUUID().toString().replace("-", "");
//            String orderSn = OrderUtils.getOrder();
//            order.setOrderSn(orderSn);
//            boolean save = this.save(order);
//            if (!save) {
//                throw new BusinessException("订单保存失败！");
//            }
//            // 返回数据
//            vo.setOrderId(order.getOrderId());
//            vo.setOrderSn(order.getOrderSn());
//            vo.setTotalAmount(order.getTotalAmount());
//            vo.setUntitled(order.getUntitled());
//            vo.setProductType(order.getProductType());
//            List<OrderItem> orderItems = new ArrayList<>();
//            List<ProductSku> productSkus = new ArrayList<>();
//            // 库存不存要下架的商品id
//            List<String> outProductId = new ArrayList<>();
//            for (BuySkuInfoDTO buySkuInfoDTO : buySkuInfoDTOS) {
//                BigDecimal buyNum = buySkuInfoDTO.getBuyNum();
//                ProductSku productSku = productSkuService.getProductSkuById(buySkuInfoDTO.getSkuId(), PublicEnum.STATE_OPEN.getCode());
//                // 订单项
//                OrderItem orderItem = new OrderItem();
//                orderItem.setOrderId(order.getOrderId());
//                orderItem.setOrderSn(order.getOrderSn());
//                orderItem.setProductId(productSku.getProductId());
//                orderItem.setProductSn(product.getSerialNum());
//                orderItem.setProductName(product.getProductName());
//                orderItem.setRelevanceName(product.getRelevanceName().trim());
//                // sku图片
//                orderItem.setProductImg(productSku.getSkuImg());
//                orderItem.setSkuId(productSku.getSkuId());
//                orderItem.setSkuName(productSku.getSkuName().trim());
//                orderItem.setUnit(productSku.getUnit());
//                orderItem.setProductPrice(productSku.getSellPrice());
//                orderItem.setBuyCounts(buyNum);
//                orderItem.setProductType(product.getProductType());
//                orderItem.setTotalAmount(productSku.getSellPrice().multiply(buyNum).multiply(buySkuInfoDTO.getLeaseNum()));
//                orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
//                orderItem.setCostPrice(productSku.getCostPrice());
//                orderItem.setOriginalPrice(productSku.getOriginalPrice());
//                if (productSku.getCostPrice() != null) {
//                    orderItem.setProfitPrice(productSku.getSellPrice().subtract(productSku.getCostPrice()));
//                }
//                Integer productType = product.getProductType();
//                if (productType == 2 || productType == 5) {
//                    orderItem.setLeaseUnit(productSku.getLeaseUnit());
//                    orderItem.setLeaseNum(dto.getBuySkuInfoDTOS().get(0).getLeaseNum());
//                }
//                orderItems.add(orderItem);
//
//                // TODO 减库存放在保存订单处
//                BigDecimal newStock = productSku.getStock().subtract(buyNum);
////                ProductSku productSkuUpdate = new ProductSku();
////                productSkuUpdate.setSkuId(skuId);
////                productSkuUpdate.setStock(newStock);
////                productSkus.add(productSkuUpdate);
//
//                // 判断库存是否等于0如果等于0则下架商品
//                if (newStock.compareTo(BigDecimal.ZERO) == 0) {
//                    outProductId.add(buySkuInfoDTO.getProductId());
//                }
//            }
//            boolean f = orderItemService.saveBatch(orderItems);
//            if (!f) {
//                throw new BusinessException("订单保存失败！");
//            }
////            boolean b = productSkuService.updateBatchById(productSkus);
////            if (!b) {
////                throw new BusinessException("订单保存失败！");
////            }
//            // 下架商品
////            if (!CollectionUtils.isEmpty(outProductId)) {
////                UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
////                updateProductStateDTO.setState(2);
////                updateProductStateDTO.setProductIds(outProductId);
////                productService.updateProductState(updateProductStateDTO);
////            }
//        }
//        return vo;
//    }


    /**
     * 购物车结算
     *
     * @param
     */
//    public Object createDeviceOrderByCartIds(CreateMaterialOrderByCartIdsDTO dto) {
//        ArrayList<OrderCreateResultVO> vos = new ArrayList<>();
//        // 只能同一个种类型结算
//        List<String> cartIds = dto.getCartIds();
//        List<ShoppingCart> list = shoppingCartService.lambdaQuery()
//                .in(ShoppingCart::getCartId, cartIds)
//                .eq(ShoppingCart::getUserId, ThreadLocalUtil.getCurrentUser().getUserId())
//                .select(ShoppingCart::getProductType, ShoppingCart::getProductId).list();
//        if (CollectionUtils.isEmpty(list)) {
//            throw new BusinessException(400, "购物车数据不存在！");
//        }
//        long count = list.stream().map(t -> {
//            return t.getProductType();
//        }).distinct().count();
//        if (count > 1) {
//            throw new BusinessException(400, "只能结算同一种类型的商品，请检查后再试！");
//        }
//        List<ShoppingCartShopIdVO> cartVOS = shoppingCartMapper.listShopIdAndCartByCartIds(dto.getCartIds(), ThreadLocalUtil.getCurrentUser().getUserId());
//        if (CollectionUtils.isEmpty(cartVOS)) {
//            throw new BusinessException("商品不存在或购物车信息不存在！");
//        }
//        Map<String, String> untitledMap = new HashMap<>();
//
//        // 实际总金额
//        BigDecimal actualAmount = new BigDecimal(0);
////        // 订单总金额
////        BigDecimal totalAmount = new BigDecimal(0);
//
//
//        //检查数据库
//        for (ShoppingCartShopIdVO cartVO : cartVOS) {
//            BigDecimal actualAmount2 = new BigDecimal(0);
//            BigDecimal totalAmount2 = new BigDecimal(0);
//            BigDecimal costPriceTotal2 = new BigDecimal(0);
//            String untitled = "";
//            List<ShoppingCart> shoppingCarts1 = cartVO.getShoppingCarts();
//            for (ShoppingCart cart : shoppingCarts1) {
//                String productId = cart.getProductId();
//                // 查看购物车的商品是否还上架未删除
//                Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
//                if (product == null) {
//                    Product byId = productService.getProductExcludeRemarkById(productId);
//                    if (byId != null) {
//                        throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
//                    } else {
//                        throw new BusinessException(OrderEnum.RESULT_CODE_500201.getCode(), OrderEnum.RESULT_CODE_500201.getRemark());
//                    }
//                } else {
//                    // 商品存在
//                    // 查询已上架未删除
//                    ProductSku productSku = productSkuService.getProductSkuById(cart.getSkuId(), PublicEnum.STATE_OPEN.getCode());
//                    if (productSku == null) {
//                        // 直接查询
//                        ProductSku byId = productSkuService.getById(cart.getSkuId());
//                        if (byId != null) {
//                            throw new BusinessException("商品名为：【" + product.getProductName() + "】商品的【" + byId.getSkuName() + "】规格已下架或已被删除！");
//                        } else {
//                            throw new BusinessException("订单保存失败！");
//                        }
//                    } else {
//                        // 如果库存不足
//                        int i = cart.getCartNum().compareTo(productSku.getStock());
//                        if (i == 1) {
//                            throw new BusinessException("【" + product.getProductName() + "】商品的【" + productSku.getSkuName() + "】库存不足!");
//                        }
//                        //获取每个商品名称，以,分割，拼接成字符串
//                        untitled = untitled + product.getProductName() + ",";
//
//                        actualAmount = actualAmount.add(productSku.getSellPrice().multiply(cart.getCartNum()).multiply(cart.getLeaseNum()));
////                        totalAmount = totalAmount.add(productSku.getOriginalPrice().multiply(cart.getCartNum()).multiply(cart.getLeaseNum()));
////                        cart.setActualAmount(productSku.getSellPrice().multiply(cart.getCartNum()).multiply(cart.getLeaseNum()));
////                        cart.setTotalAmount(productSku.getOriginalPrice().multiply(cart.getCartNum()).multiply(cart.getLeaseNum()));
//                        actualAmount2 = actualAmount2.add(productSku.getSellPrice().multiply(cart.getCartNum()).multiply(cart.getLeaseNum()));
//                        if (productSku.getCostPrice() != null) {
//                            costPriceTotal2 = costPriceTotal2.add(productSku.getCostPrice().multiply(cart.getCartNum()).multiply(cart.getLeaseNum()));
//                        }
//                        if (productSku.getOriginalPrice() != null) {
//                            totalAmount2 = totalAmount2.add(productSku.getOriginalPrice().multiply(cart.getCartNum()).multiply(cart.getLeaseNum()));
//                        }
//                    }
//                }
//            }
//            cartVO.setActualAmount(actualAmount2);
//            cartVO.setTotalAmount(totalAmount2);
//            cartVO.setCostPriceTotal(costPriceTotal2);
//            untitledMap.put(cartVO.getShopId(), untitled);
//        }
//        // 验价
//        if (Math.abs(dto.getPayPrice().subtract(actualAmount).doubleValue()) >= 0.01) {
//            throw new BusinessException("订单商品价格发生变化，请确认后再次提交！");
//        }
//
//
//        // 都验证通过开始保存订单
//        for (ShoppingCartShopIdVO cartVO : cartVOS) {
//            OrderCreateResultVO vo = new OrderCreateResultVO();
////            String productId = cartVO.getProductId();
////            Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
//            Orders order = new Orders();
//            order.setEnterpriseId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
//            order.setProductType(cartVO.getShoppingCarts().get(0).getProductType());
//            // 暂时都是前端传入内部结算
//            // 生成订单号
//            String orderSn = OrderUtils.getOrder();
//            order.setOrderSn(orderSn);
//
//            BeanUtils.copyProperties(dto, order);
//
//            // 用户ID
//            String userId = ThreadLocalUtil.getCurrentUser().getUserId();
//            order.setUserId(userId);
//            order.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
//            String sUn = untitledMap.get(cartVO.getShopId());
//            // 商品名称拼接
//            order.setUntitled(sUn.substring(0, sUn.length() - 1));
//
//            // 店铺名称
//            order.setShopId(cartVO.getShopId());
//            String shopName = shopService.getShopNameById(cartVO.getShopId());
//            order.setShopName(shopName);
//            // 金额
////            BigDecimal actualAmount2 = new BigDecimal(0);
////            BigDecimal totalAmount2 = new BigDecimal(0);
////            for (ShoppingCart cart : cartVO.getShoppingCarts()) {
////                actualAmount2 = actualAmount2.add(cart.getProductPrice().multiply(cart.getCartNum()));
////            }
//            // 金额
////            order.setTotalAmount(cartVO.getShoppingCarts().get(0).getTotalAmount());
//            order.setActualAmount(cartVO.getActualAmount());
//            order.setTotalAmount(cartVO.getTotalAmount());
//            order.setCostPriceTotal(cartVO.getCostPriceTotal());
//            order.setProfitPriceTotal(cartVO.getActualAmount().subtract(cartVO.getCostPriceTotal()));
//            if (ThreadLocalUtil.getCurrentUser().getIsInterior() == 1) {
//                order.setState(OrderEnum.STATE_DRAFT.getCode());
//            } else {
//                order.setState(OrderEnum.STATE_FINISH.getCode());
//            }
//            order.setOrderFreight(new BigDecimal("0"));
////            // TODO 订单不保存productTpe
////            order.setProductType(0);
//            // 发票状态
//            order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());
//            boolean save = this.save(order);
//            if (!save) {
//                throw new BusinessException("订单保存失败！");
//            }
//            vo.setOrderSn(order.getOrderSn());
//            vo.setOrderId(order.getOrderId());
//            vo.setTotalAmount(actualAmount);
//            vo.setUntitled(order.getUntitled());
//            vo.setProductType(order.getProductType());
//            vos.add(vo);
//            List<ShoppingCart> shoppingCarts = cartVO.getShoppingCarts();
//            ArrayList<OrderItem> orderItems = new ArrayList<>();
//            ArrayList<ProductSku> productSkus = new ArrayList<>();
//            // 要下架的商品id
//            List<String> outProductId = new ArrayList<>();
//            for (ShoppingCart cart : shoppingCarts) {
//                Product product = productService.getById(cart.getProductId());
//                ProductSku productSku = productSkuService.getProductSkuById(cart.getSkuId(), PublicEnum.STATE_OPEN.getCode());
//                // 订单项
//                OrderItem orderItem = new OrderItem();
//                orderItem.setUnit(productSku.getUnit());
//                orderItem.setOrderId(order.getOrderId());
//                orderItem.setOrderSn(order.getOrderSn());
//                orderItem.setProductId(cart.getProductId());
//                orderItem.setProductSn(product.getSerialNum());
//                orderItem.setProductName(product.getProductName());
//                orderItem.setProductImg(productSku.getSkuImg());
//                orderItem.setSkuId(cart.getSkuId());
//                orderItem.setRelevanceName(product.getRelevanceName().trim());
//                orderItem.setSkuName(productSku.getSkuName().trim());
//                orderItem.setProductPrice(productSku.getSellPrice());
//                orderItem.setBuyCounts(cart.getCartNum());
//                orderItem.setProductType(product.getProductType());
//                orderItem.setTotalAmount(productSku.getSellPrice().multiply(cart.getCartNum()).multiply(cart.getLeaseNum()));
//                orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
//                orderItem.setCostPrice(productSku.getCostPrice());
//                orderItem.setOriginalPrice(productSku.getOriginalPrice());
//                if (productSku.getCostPrice() != null) {
//                    orderItem.setProfitPrice(productSku.getSellPrice().subtract(productSku.getCostPrice()));
//                }
//                orderItem.setLeaseNum(cart.getLeaseNum());
//                orderItem.setLeaseUnit(cart.getLeaseUnit());
//                orderItems.add(orderItem);
//
//                // TODO 减库存
//                String skuId = cart.getSkuId();
//                BigDecimal newStock = productSku.getStock().subtract(cart.getCartNum());
////                ProductSku productSku2 = new ProductSku();
////                productSku2.setSkuId(skuId);
////                productSku2.setStock(newStock);
////                productSkus.add(productSku2);
//
//                // 判断库存是否等于0如果等于0则下架商品
//                if (newStock.compareTo(BigDecimal.ZERO) == 0) {
//                    outProductId.add(cart.getProductId());
//                }
//            }
//            boolean f = orderItemService.saveBatch(orderItems);
//            if (!f) {
//                throw new BusinessException("订单保存失败！");
//            }
////            boolean b = productSkuService.updateBatchById(productSkus);
////            if (!b) {
////                throw new BusinessException(OrderEnum.RESULT_CODE_500200.getCode(), OrderEnum.RESULT_CODE_500200.getRemark());
////            }
//            shoppingCartMapper.removeRealByIds(dto.getCartIds());
//            // 下架商品
////            if (!CollectionUtils.isEmpty(outProductId)) {
////                UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
////                updateProductStateDTO.setState(2);
////                updateProductStateDTO.setProductIds(outProductId);
////                productService.updateProductState(updateProductStateDTO);
////            }
//        }
//        return vos;
//    }
    @Override
    public PlatformShopCountVo getPlatformShopOrderCount(JSONObject jsonObject, QueryWrapper<ShopCountVo> shopCountVoQueryWrapper) {
        QueryWrapper<PlatformShopCountVo> q = new QueryWrapper<>();
        QueryWrapper<ShopCountVo> wrapper = new QueryWrapper<>();
        Integer mallType = mallConfig.mallType;
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String startCreateDate = (String) innerMap.get("startDate");
        String endCreateDate = (String) innerMap.get("endDate");
        String shopName = (String) innerMap.get("homeName");
        Integer productType= (Integer) innerMap.get("productType");
        wrapper.ne("o.order_class", 3);
        q.ne("order_class", 3);
        if (startCreateDate != null || endCreateDate != null) {
            q.between("gmt_create", startCreateDate, endCreateDate);
            wrapper.between("o.gmt_create", startCreateDate, endCreateDate);
        }
        if (shopName != null) {
            wrapper.like("s.shop_name", shopName);
        }

        q.eq("mall_type", mallType);
        q.eq("product_type",productType);
        PlatformShopCountVo vo = ordersMapper.getPlatformOrderTotalCount(q);
        IPage<ShopCountVo> page = new Query<ShopCountVo>().getPage(jsonObject);
        wrapper.eq("o.mall_type", mallType);
        wrapper.groupBy("o.shop_id");
        List<ShopCountVo> list = ordersMapper.getPlatformShopOrderCount(page, wrapper);
        vo.setShopCountVoList(list);
        vo.setTotalCount(page.getTotal());
        vo.setPageSize(page.getSize());
        vo.setCurrentPage(page.getCurrent());
        return vo;
    }

    /**
     * 提交合同
     *
     * @param dtos
     */
    @Override
    public void submitContactAndOrder(List<SubmitContactDTO> dtos) {
        System.out.println("直接购买提交合同参数：-----------------------------------");
        String s1 = JSON.toJSONString(dtos);
        System.out.println(s1);
        if (CollectionUtils.isEmpty(dtos) || dtos.get(0).getContact() == null) {
            throw new BusinessException(400, "请求数据不完整！");
        }
        RequestJsonRPCDTO rqDTO = new RequestJsonRPCDTO();
        rqDTO.setJsonrpc("2.0");
        rqDTO.setMethod("CBM.ToEquipMaterialService.OrderToReverseWrite");
        rqDTO.setId(1);
        HashMap<String, String> tags = new HashMap<>();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        tags.put("userid", user.getFarUserId());
        tags.put("username", user.getOriginalUserName());
        tags.put("orgid", user.getOrgId());
        tags.put("orgname", user.getEnterpriseName());
        tags.put("companycode", "1000");
        tags.put("auth_client_id", "test");
        tags.put("auth_token", "test");
        tags.put("platformid", "1");
        rqDTO.setTags(tags);

        // 开始组织参数

        // 拿到合同，因为合同和订单是一对一的关系, 他们对应的合同都一样
        SubmitContactInfoDTO contact = dtos.get(0).getContact();
        SubmitContactOrderDTO orderOne = dtos.get(0).getOrder();


        List<CBM_EquipOperationOrderDtlDTO> dtls = new ArrayList<>();
        // 遍历选择的合同设备清单
        List<ProductSku> productSkus = new ArrayList<>();
        // 库存不存要下架的商品id
        List<String> outProductId = new ArrayList<>();
        // 保存合同和订单关联关系
        List<OrderSelectContact> orderSelectContacts = new ArrayList<>();
        for (SubmitContactDTO dto : dtos) {
            CBM_EquipOperationOrderDtlDTO dtl = new CBM_EquipOperationOrderDtlDTO();
            // 获取订单、合同信息
            SubmitContactOrderDTO order = dto.getOrder();
            // 装载详情
            dtl.setBillId(order.getOrderId());
            dtl.setOrderDtlId(order.getOrderItemId());
            dtl.setContractDtlId(dto.getDtlId());
            dtl.setItemId(dto.getItemId());
            dtl.setItemName(dto.getItemName());
            dtl.setSize(dto.getSize());
            dtl.setUnit(dtl.getUnit());
            dtl.setThisOrderQty(dto.getCount());
            dtl.setThisOrderPrice(order.getProductPrice());
            dtl.setThisOrderAmount(dto.getCount().multiply(order.getProductPrice()).setScale(2, RoundingMode.HALF_UP));
            dtls.add(dtl);


            // 储存库存、下架
            String skuId = order.getSkuId();
            ProductSku productSku = productSkuService.lambdaQuery().eq(ProductSku::getSkuId, skuId)
                    .select(ProductSku::getStock).one();
            if (productSku == null) {
                throw new BusinessException(400, "商品【" + order.getProductName() + "】已下架或不存在！");
            } else {
                BigDecimal buyCounts = order.getBuyCounts();
                // 如果库存不足
                int i = buyCounts.compareTo(productSku.getStock());
                if (i == 1) {
                    throw new BusinessException("【" + order.getProductName() + "】商品库存不足!");
                }
                ProductSku productSku1 = new ProductSku();
                BigDecimal newStock = productSku.getStock().subtract(buyCounts);

                // 判断库存是否等于0如果等于0则下架商品
                if (newStock.compareTo(BigDecimal.ZERO) == 0) {
                    outProductId.add(productSku.getProductId());
                }
                productSku1.setSkuId(skuId);
                productSku1.setStock(newStock);
                productSkus.add(productSku1);
            }


            // 保存订单合同关联关系
            OrderSelectContact orderSelectContact = new OrderSelectContact();
            orderSelectContact.setOrderId(order.getOrderId());
            orderSelectContact.setOrderSn(order.getOrderSn());
            orderSelectContact.setOrderItemId(order.getOrderItemId());
            orderSelectContact.setDtlId(dto.getDtlId());
            orderSelectContact.setItemName(dto.getItemName());
            orderSelectContact.setCount(dto.getCount());
            orderSelectContact.setBillId(contact.getBillId());
            orderSelectContact.setBillNo(contact.getBillNo());
            orderSelectContact.setBillName(contact.getBillName());
            orderSelectContact.setProductType(order.getProductType());
            orderSelectContact.setSize(dto.getSize());
            orderSelectContact.setOrgId(user.getOrgId());

            orderSelectContacts.add(orderSelectContact);
        }
        List<CBM_EquipOperationOrderDTO> params = new ArrayList<>();
        CBM_EquipOperationOrderDTO param = new CBM_EquipOperationOrderDTO();
        // 设置明细
        param.setDetails(dtls);
        param.setContractId(contact.getBillId());
        param.setType(contact.getType());
        param.setSupplierName(orderOne.getOrgName());
        param.setCreditCode(orderOne.getSocialCreditCode());
        param.setOrderId(orderOne.getOrderId());
        param.setShopId(orderOne.getShopId());
        param.setOrgId(user.getOrgId());
        param.setOrgName(user.getEnterpriseName());
//        param.setCreateTime(new Date());
        param.setRecorder(user.getOriginalUserName());
        param.setRecorderId(user.getFarUserId());
        params.add(param);

        HashMap<String, Object> p = new HashMap<>();
        p.put("Orders", params);
        rqDTO.setParams(p);
        String content = JSON.toJSONString(rqDTO);

        // 发送请求
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<String> request = new HttpEntity<>(content, headers);
        String url = mallConfig.pcwp1ContactUrl + "/json.rpc";
        Map rMap = null;
        try {
            // 中文乱码，主要是 StringHttpMessageConverter的默认编码为ISO导致的
            List<HttpMessageConverter<?>> list = restTemplate.getMessageConverters();
            for (HttpMessageConverter converter : list) {
                if (converter instanceof StringHttpMessageConverter) {
                    ((StringHttpMessageConverter) converter).setDefaultCharset(Charset.forName("UTF-8"));
                    break;
                }
            }
            rMap = restTemplate.postForObject(url, request, Map.class);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException(400, "提交到合同失败！");
        }
        boolean result = (boolean) rMap.get("result");
        if (result) {
            // 如果提交成功

            // 保存合同相关信息
            orderSelectContactService.saveBatch(orderSelectContacts);

            // 订单状态
            Orders orders2 = lambdaQuery().eq(Orders::getOrderSn, orderOne.getOrderSn()).one();
            orders2.setState(OrderEnum.STATE_FINISH.getCode());
            orders2.setFlishTime(new Date());
            updateById(orders2);

            // 扣减库存
            if (!CollectionUtils.isEmpty(productSkus)) {
                productSkuService.updateBatchById(productSkus);
            }

            // 库存为0下架商品
            if (!CollectionUtils.isEmpty(outProductId)) {
                if (!CollectionUtils.isEmpty(outProductId)) {
                    UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
                    updateProductStateDTO.setState(2);
                    updateProductStateDTO.setProductIds(outProductId);
                    productService.updateProductState(updateProductStateDTO);
                }
            }
        } else {
            throw new BusinessException(400, "提交到合同失败！");
        }
    }

    /**
     * 批量发货
     *
     * @param dtos
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchShipments(List<BatchShipmentsDTO> dtos) {
        for (BatchShipmentsDTO dto : dtos) {
            Orders byId = getById(dto.getOrderId());
            if (byId != null) {
                BeanUtils.copyProperties(dto, byId);
                byId.setDeliveryTime(new Date());
                byId.setState(OrderEnum.STATE_YES_FREIGHT.getCode());
                updateById(byId);
            }
        }
    }

    /**
     * 个人中心确认收货
     *
     * @param orderId
     */
    @Override
    public void confirmReceipt(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            throw new BusinessException(400, "未携带订单id");
        }
        HashMap<Object, Object> dto = new HashMap<>();
        dto.put("jsonrpc", "2.0");
        dto.put("method", "CBM.ToEquipMaterialService.OrderConfirmReceive");
        dto.put("id", 1);
        HashMap<String, String> tags = new HashMap<>();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        tags.put("userid", user.getFarUserId());
        tags.put("username", user.getOriginalUserName());
        tags.put("orgid", user.getOrgId());
        tags.put("orgname", user.getEnterpriseName());
        tags.put("companycode", "1000");
        tags.put("auth_client_id", "test");
        tags.put("auth_token", "test");
        tags.put("platformid", "1");
        dto.put("tags", tags);
        HashMap<String, String> params = new HashMap<>();
        params.put("OrderId", orderId);
        dto.put("params", params);
        String content = JSON.toJSONString(dto);
        // 发送请求
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<String> request = new HttpEntity<>(content, headers);
        String url = mallConfig.pcwp1ContactUrl + "/json.rpc";
        Map rMap = restTemplate.postForObject(url, request, Map.class);
        System.out.println("交易金额返回：" + rMap);
        boolean result = (boolean) rMap.get("result");
        if (result) {
            Orders orders = getById(orderId);
            orders.setState(OrderEnum.STATE_OK.getCode());
            orders.setSuccessDate(new Date());
            boolean b = updateById(orders);
            if (!b) {
                log.error("确认收货失败，请求参数：" + orderId);
            }
        }

    }


    /**
     * 计划详情生成订单
     *
     * @param dtos
     * @param shopId
     * @param account
     * @param map
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void createMaterialOrder(List<ProductBuyInfoDTO> dtos, String shopId, BigDecimal account, HashMap<Object, Object> map) {
        // 实际总金额
        BigDecimal actualAmount = new BigDecimal(0);
        // 总金额
        BigDecimal totalAmount = new BigDecimal(0);
        // 总成本价
        BigDecimal costPriceTotal = new BigDecimal(0);

        Shop shopOne = shopService.lambdaQuery()
                .eq(Shop::getShopId, shopId)
                .select(Shop::getShopName, Shop::getShopClass, Shop::getEnterpriseId).one();
        if (shopOne == null) {
            throw new BusinessException("店铺不存在！");
        }
        HashSet<Object> supplierIdSet = new HashSet<>();
        // 判断是不是多供方订单
        boolean flagOrderClass = false;
        String untitled = "";
        for (ProductBuyInfoDTO dto : dtos) {
            String productId = dto.getProductId();
            // 查看购物车的商品是否还上架未删除
            Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
            if (product == null) {
                Product byId = productService.getProductExcludeRemarkById(productId);
                if (byId != null) {
                    throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
                } else {
                    throw new BusinessException(OrderEnum.RESULT_CODE_500201.getRemark());
                }
            } else {
                String supperBy = product.getSupperBy();
                // 装入set集合
                supplierIdSet.add(supperBy);
                ProductSku productSku = productSkuService.getProductSkuByProductId(productId, null).get(0);
//                // 如果计划库存低于现有库存
                int i = dto.getCartNum().compareTo(productSku.getStock());
                if (i == 1) {
                    // 大于
                    throw new BusinessException("商品名为：【" + product.getProductName() + "】的商品库存不足！");
                }
                //获取每个商品名称，以,分割，拼接成字符串
                untitled = untitled + product.getProductName() + ",";
                actualAmount = actualAmount.add(productSku.getSellPrice().multiply(dto.getCartNum()));
                totalAmount = totalAmount.add(productSku.getOriginalPrice().multiply(dto.getCartNum()));
                costPriceTotal = costPriceTotal.add(productSku.getCostPrice().multiply(dto.getCartNum()));
            }
        }
        // 验价
        if (Math.abs(account.subtract(actualAmount).doubleValue()) >= 0.01) {
            throw new BusinessException("【" + untitled.substring(0, untitled.length() - 1) + "】订单总价发生变化！");
        }


        // 供应商的id不只是1个 或者 不包含本身机构 （废弃，只要支持多供方店铺，所有的商品都是多供方）
//        if (supplierIdSet.size() != 1 || !supplierIdSet.contains(enterpriseId)) {
//            flagOrderClass = true;
//        }


        Orders order = new Orders();
        order.setEnterpriseId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        ProductBuyInfoDTO dtox = dtos.get(0);
        order.setReceiverName(dtox.getReceiverName());
        order.setPayWay(dtox.getPayWay());
        order.setReceiverMobile(dtox.getReceiverMobile());
        order.setReceiverAddress(dtox.getReceiverAddress());
        order.setOrderRemark(dtox.getOrderRemark());

        // 生成订单号
        order.setOrderSn(OrderUtils.getOrder());
        // 用户ID
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        order.setUserId(userId);
        order.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
        // 商品名称拼接
        order.setUntitled(untitled.substring(0, untitled.length() - 1));

        // 店铺名称
        order.setShopId(shopId);

        // 多供方订单 （废弃，只要支持多供方店铺，所有的商品都是多供方）
//        if (flagOrderClass && shopOne.getShopClass() == 2) {
//            order.setOrderClass(2);
//        }
        if (shopOne.getShopClass() == 2) {
            order.setOrderClass(2);
        } else {
            // 不是多供方，普通订单，保存商品的供应商
            Product product = productService.getProductById(dtos.get(0).getProductId(), null);
            if (product != null) {
                order.setSupplierId(product.getSupperBy());
                order.setSupplierName(product.getSupplierName());
            }
        }

        order.setShopName(shopOne.getShopName());
        // 金额
        order.setTotalAmount(totalAmount);
        order.setActualAmount(actualAmount);
        order.setCostPriceTotal(costPriceTotal);
        order.setProfitPriceTotal(actualAmount.subtract(costPriceTotal));
        order.setFlishTime(new Date());
        order.setState(OrderEnum.STATE_FINISH.getCode());
        order.setOrderFreight(new BigDecimal("0"));
        //  低值易耗品
        order.setProductType(10);
        // 发票状态
        order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());
        boolean save = this.save(order);
        if (!save) {
            throw new BusinessException("订单保存失败！");
        }
        ArrayList<ProductSku> productSkus = new ArrayList<>();
        ArrayList<OrderSelectPlan> orderSelectPlans = new ArrayList<>();
        ArrayList<Map<String, Object>> retPlanList = new ArrayList<>();
        // 要下架的商品id
        List<String> outProductId = new ArrayList<>();
        // 处理拆单
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        // 拆单
        Integer shopClass = shopOne.getShopClass();
        for (ProductBuyInfoDTO dto : dtos) {
            Product product = productService.getProductById(dto.getProductId(), null);
            ProductSku productSku = productSkuService.getProductSkuByProductId(dto.getProductId(), null).get(0);
            // 订单项
            OrderItem orderItem = new OrderItem();
            orderItem.setUnit(productSku.getUnit());
            orderItem.setOrderId(order.getOrderId());
            orderItem.setOrderSn(order.getOrderSn());
            orderItem.setProductId(product.getProductId());
            orderItem.setProductSn(product.getSerialNum());
            orderItem.setProductName(product.getProductName());
            orderItem.setProductImg(productSku.getSkuImg());
            orderItem.setSkuId(productSku.getSkuId());
            orderItem.setSkuName(productSku.getSkuName().trim());
            orderItem.setProductPrice(productSku.getSellPrice());
            orderItem.setBuyCounts(dto.getCartNum());
            orderItem.setProductType(10);
            // 如果是多供方
            if (order.getOrderClass() == 2) {
                orderItem.setState(1);
            }
            orderItem.setTotalAmount(productSku.getSellPrice().multiply(dto.getCartNum()));
            orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
            orderItem.setCostPrice(productSku.getCostPrice());
            orderItem.setOriginalPrice(productSku.getOriginalPrice());
            orderItem.setCostAmount(productSku.getCostPrice().multiply(dto.getCartNum()));
            orderItem.setSupplierId(product.getSupperBy());
            orderItem.setClassId(product.getClassId());
            orderItem.setBrandId(product.getBrandId());
            orderItem.setBrandName(product.getBrandName());
            if (productSku.getCostPrice() != null) {
                orderItem.setProfitPrice(productSku.getSellPrice().subtract(productSku.getCostPrice()));
            }
            List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(product.getClassId());
            String classPath = "";
            if (CollectionUtils.isEmpty(categoryParentPath)) {
                throw new BusinessException("分类不存在！");
            } else {
                if (categoryParentPath.size() == 1) {
                    orderItem.setClassPathName(categoryParentPath.get(0).getClassName());
                } else {
                    for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
                        if (!org.springframework.util.StringUtils.isEmpty(categoryClassIdAndClassNameVO.getClassName())) {
                            classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
                        }
                    }
                    orderItem.setClassPathName(classPath.substring(0, classPath.length() - 1));
                }
            }
            orderItemService.save(orderItem);

            // 拆单所需数据
            if (shopClass == 2) {
//                orderItem.setOriginalPriceSum(productSku.getOriginalPrice().multiply(dto.getCartNum()));
                orderItem.setCostPriceSum(productSku.getCostPrice().multiply(dto.getCartNum()));
                orderItems.add(orderItem);
            }

            // 扣减库存
            String skuId = productSku.getSkuId();
            BigDecimal newStock = productSku.getStock().subtract(dto.getCartNum());
            ProductSku productSku2 = new ProductSku();
            productSku2.setSkuId(skuId);
            productSku2.setStock(newStock);
            productSkus.add(productSku2);

            // 判断库存是否等于0如果等于0则下架商品
            if (newStock.compareTo(BigDecimal.ZERO) == 0) {
                outProductId.add(dto.getProductId());
            }

            // 保存计划订单
            OrderSelectPlan orderSelectPlan = new OrderSelectPlan();
            orderSelectPlan.setOrderSn(order.getOrderSn());
            orderSelectPlan.setOrderItemId(orderItem.getOrderItemId());
            orderSelectPlan.setDtlId(dto.getDtlId());
            orderSelectPlan.setEquipmentName(product.getProductName());
            orderSelectPlan.setCount(dto.getCartNum());
            orderSelectPlan.setBillId(dto.getBillId());
            orderSelectPlan.setBillNo(dto.getBillNo());
            orderSelectPlan.setProductType(10);
            orderSelectPlan.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
            orderSelectPlan.setOrgName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
            orderSelectPlan.setPrice(dto.getPrice());
            orderSelectPlan.setAccount(dto.getPrice().multiply(dto.getCartNum()));
            orderSelectPlan.setStorageId(dto.getStorageId());
            orderSelectPlan.setStorageName(dto.getStorageName());
            orderSelectPlan.setShortCode(dto.getShortCode());
            orderSelectPlan.setCreditCode(dto.getCreditCode());
            orderSelectPlan.setStorageOrgId(dto.getStorageOrgId());
            orderSelectPlan.setPlanType(1);
            // 如果信用代码为空表示是内部供应商
            if (StringUtils.isEmpty(dto.getCreditCode())) {
                orderSelectPlan.setSupplierType(2);
            } else {
                orderSelectPlan.setSupplierType(1);
            }
            orderSelectPlans.add(orderSelectPlan);

            // 组装反写计划
            HashMap<String, Object> onePlanMap = new HashMap<>();
            onePlanMap.put("dtlId", dto.getDtlId());
            onePlanMap.put("billId", dto.getDtlId());
            BigDecimal multiply = productSku.getSellPrice().multiply(dto.getCartNum());
            BigDecimal resultAmount = multiply.setScale(2, RoundingMode.HALF_UP);
            onePlanMap.put("amount", resultAmount);
            onePlanMap.put("number", dto.getCartNum());
            retPlanList.add(onePlanMap);
        }
        // 处理拆单
        if (shopClass == 2) {
            // 如果商品没有供应不拆单，，（废弃）都有供应商
//            List<OrderItem> collect = orderItems.stream().filter(orderItem -> orderItem.getSupplierId() != null).collect(Collectors.toList());

            //（废弃） 新增商品我会保存id
//            for (OrderItem orderItem : orderItems) {
//                if (orderItem.getSupplierId() == null) {
//                    // 如果商品供方没有数据，则表示自己为供方
//                    orderItem.setSupplierId(shopOne.getEnterpriseId());
//                }
//            }

            // 生成订单号
            Map<String, List<OrderItem>> collectOrderItems = orderItems.stream()
                    .collect(Collectors.groupingBy(OrderItem::getSupplierId));
            collectOrderItems.forEach((supplierId, newOrderItems) -> {
                // 保存订单、计算金额
                Orders orders = new Orders();
                BeanUtils.copyProperties(order, orders);
                orders.setOrderId(null);
                orders.setUntitled(null);
                orders.setOrderSn(null);
                orders.setEnterpriseId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());

                // 子订单没有这些金额
                // 实际总金额
//                BigDecimal actualAmount2 = new BigDecimal(0);
                // 总金额
//                BigDecimal totalAmount2 = new BigDecimal(0);
                // 总成本价
                BigDecimal costPriceTotal2 = new BigDecimal(0);
                String untitled2 = "";
                for (OrderItem newOrderItem : newOrderItems) {
//                    actualAmount2 = actualAmount2.add(newOrderItem.getTotalAmount());
//                    totalAmount2 = totalAmount2.add(newOrderItem.getOriginalPriceSum());
                    costPriceTotal2 = costPriceTotal2.add(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()));
                    untitled2 = untitled2 + newOrderItem.getProductName() + ",";
                }

                orders.setUntitled(untitled2.substring(0, untitled2.length() - 1));
                // 理论上，在主订单包含商品多供方的情况下，如果将主订单拆分成多个子订单，那么每个子订单的销售价应
                // 该等于对应供应商提供商品的成本价，这与主订单的成本价是相同的。
                orders.setActualAmount(costPriceTotal2);
//                orders.setActualAmount(actualAmount2);
//                orders.setTotalAmount(totalAmount2);
                orders.setTotalAmount(BigDecimal.ZERO);
                orders.setCostPriceTotal(BigDecimal.ZERO);
                orders.setOrderClass(3);
                orders.setParentOrderId(order.getOrderId());
                orders.setOrderSourceType(1);
                orders.setOrderSourceId(orders.getParentOrderId());
                EnterpriseInfo enterpriseInfoOne = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, supplierId)
                        .select(EnterpriseInfo::getEnterpriseName).one();
                if (enterpriseInfoOne != null) {
                    orders.setSupplierName(enterpriseInfoOne.getEnterpriseName());
                } else {
                    throw new BusinessException("供应商不存在！");
                }
                orders.setSupplierId(supplierId);
                orders.setOrderSn(OrderUtils.getOrder());
                save(orders);
                // 保存好了订单项，
                for (OrderItem newOrderItem : newOrderItems) {
                    newOrderItem.setOrderId(orders.getOrderId());
                    newOrderItem.setOrderSn(orders.getOrderSn());
                    newOrderItem.setProductPrice(newOrderItem.getCostPrice());
                    newOrderItem.setTotalAmount(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()));
                    newOrderItem.setCostPrice(BigDecimal.ZERO);
                    newOrderItem.setCostAmount(BigDecimal.ZERO);
                    newOrderItem.setOriginalPrice(BigDecimal.ZERO);
                    // 保存父明细id
                    newOrderItem.setParentOrderItemId(newOrderItem.getOrderItemId());
                    newOrderItem.setOrderItemId(null);
                }
                orderItemService.saveBatch(newOrderItems);
            });
        }

        // 调用反写接口
        HashMap<Object, Object> subPlanDDTO = new HashMap<>();
        HashMap<String, String> tags = new HashMap<>();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        HashMap<Object, Object> params = new HashMap<>();
        params.put("data", retPlanList);
        subPlanDDTO.put("params", params);
        subPlanDDTO.put("keyId", params);
        String content = JSON.toJSONString(subPlanDDTO);
        log.warn("反写计划接口请求参数：" + content);
        // 发送请求
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<String> request = new HttpEntity<>(content, headers);
        String url = mallConfig.prodPcwp2Url02 + "/thirdapi/sporadicPurchasePlan/updateShopDtl";
        Map rMap = null;
        try {
//            rMap = restTemplateUtils.postPCWP2(url, request, Map.class);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException("远程反写计划服务异常！");
        }
        Map error = (Map) rMap.get("error");
        if (error != null) {
            log.error("推送计划报错：" + error);
            throw new BusinessException("远程反写计划接口错误！");
        }
        String result = (String) rMap.get("result");
        if (result != null && result.equals("操作成功")) {

        } else {
            log.error("反写计划接口错误！返回：" + rMap);
            throw new BusinessException("远程反写计划服务异常");
        }


        // 保存关联计划信息
        orderSelectPlanService.saveBatch(orderSelectPlans);

        // 修改库存
        productSkuService.updateBatchById(productSkus);

        // 下架商品
        if (!CollectionUtils.isEmpty(outProductId)) {
            UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
            updateProductStateDTO.setState(2);
            updateProductStateDTO.setProductIds(outProductId);
            productService.updateProductState(updateProductStateDTO);
        }


    }


//    /**
//     * 计划详情生成订单
//     *
//     * @param dtos
//     * @param shopId
//     * @param account
//     * @param map
//     */
//    @Override
//    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
//    public void createMaterialOrder(List<ProductBuyInfoDTO> dtos, String shopId, BigDecimal account, HashMap<Object, Object> map) {
//        // 实际总金额
//        BigDecimal actualAmount = new BigDecimal(0);
//        // 总金额
//        BigDecimal totalAmount = new BigDecimal(0);
//        // 总成本价
//        BigDecimal costPriceTotal = new BigDecimal(0);
//
//        Shop shopOne = shopService.lambdaQuery()
//                .eq(Shop::getShopId, shopId)
//                .select(Shop::getShopName, Shop::getShopClass, Shop::getEnterpriseId).one();
//        if (shopOne == null) {
//            throw new BusinessException("店铺不存在！");
//        }
//        String enterpriseId = shopOne.getEnterpriseId();
//        HashSet<Object> supplierIdSet = new HashSet<>();
//        // 判断是不是多供方订单
//        boolean flagOrderClass = false;
//        String untitled = "";
//        for (ProductBuyInfoDTO dto : dtos) {
//            String productId = dto.getProductId();
//            // 查看购物车的商品是否还上架未删除
//            Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
//            if (product == null) {
//                Product byId = productService.getProductExcludeRemarkById(productId);
//                if (byId != null) {
//                    throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
//                } else {
//                    throw new BusinessException(OrderEnum.RESULT_CODE_500201.getRemark());
//                }
//            } else {
//                String supperBy = product.getSupperBy();
//                // 装入set集合
//                supplierIdSet.add(supperBy);
//                ProductSku productSku = productSkuService.getProductSkuByProductId(productId, null).get(0);
////                // 如果计划库存低于现有库存
//                int i = dto.getCartNum().compareTo(productSku.getStock());
//                if (i == 1) {
//                    // 大于
//                    throw new BusinessException("商品名为：【" + product.getProductName() + "】的商品库存不足！");
//                }
//                //获取每个商品名称，以,分割，拼接成字符串
//                untitled = untitled + product.getProductName() + ",";
//                actualAmount = actualAmount.add(productSku.getSellPrice().multiply(dto.getCartNum()));
//                totalAmount = totalAmount.add(productSku.getOriginalPrice().multiply(dto.getCartNum()));
//                costPriceTotal = costPriceTotal.add(productSku.getCostPrice().multiply(dto.getCartNum()));
//            }
//        }
//        // 验价
//        if (Math.abs(account.subtract(actualAmount).doubleValue()) >= 0.01) {
//            throw new BusinessException("【" + untitled.substring(0, untitled.length() - 1) + "】订单总价发生变化！");
//        }
//
//
//        // 供应商的id不只是1个 或者 不包含本身机构 （废弃，只要支持多供方店铺，所有的商品都是多供方）
////        if (supplierIdSet.size() != 1 || !supplierIdSet.contains(enterpriseId)) {
////            flagOrderClass = true;
////        }
//
//
//        Orders order = new Orders();
//        order.setEnterpriseId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
//        ProductBuyInfoDTO dtox = dtos.get(0);
//        order.setReceiverName(dtox.getReceiverName());
//        order.setPayWay(dtox.getPayWay());
//        order.setReceiverMobile(dtox.getReceiverMobile());
//        order.setReceiverAddress(dtox.getReceiverAddress());
//        order.setOrderRemark(dtox.getOrderRemark());
//
//        // 生成订单号
//        order.setOrderSn(OrderUtils.getOrder());
//        // 用户ID
//        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
//        order.setUserId(userId);
//        order.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
//        // 商品名称拼接
//        order.setUntitled(untitled.substring(0, untitled.length() - 1));
//
//        // 店铺名称
//        order.setShopId(shopId);
//
//        // 多供方订单 （废弃，只要支持多供方店铺，所有的商品都是多供方）
////        if (flagOrderClass && shopOne.getShopClass() == 2) {
////            order.setOrderClass(2);
////        }
//        if (shopOne.getShopClass() == 2) {
//            order.setOrderClass(2);
//        } else {
//            // 不是多供方，可以保存供方信息
//            Product product = productService.getProductById(dtos.get(0).getProductId(), null);
//            if (product != null) {
//                order.setSupplierId(product.getSupperBy());
//                order.setSupplierName(product.getSupplierName());
//            }
//        }
//
//        order.setShopName(shopOne.getShopName());
//        // 金额
//        order.setTotalAmount(totalAmount);
//        order.setActualAmount(actualAmount);
//        order.setCostPriceTotal(costPriceTotal);
//        order.setProfitPriceTotal(actualAmount.subtract(costPriceTotal));
//        order.setFlishTime(new Date());
//        order.setState(OrderEnum.STATE_FINISH.getCode());
//        order.setOrderFreight(new BigDecimal("0"));
//        //  低值易耗品
//        order.setProductType(10);
//        // 发票状态
//        order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());
//        boolean save = this.save(order);
//        if (!save) {
//            throw new BusinessException("订单保存失败！");
//        }
//        ArrayList<ProductSku> productSkus = new ArrayList<>();
//        ArrayList<OrderSelectPlan> orderSelectPlans = new ArrayList<>();
//        ArrayList<Map<String, Object>> retPlanList = new ArrayList<>();
//        // 要下架的商品id
//        List<String> outProductId = new ArrayList<>();
//        // 处理拆单
//        ArrayList<OrderItem> orderItems = new ArrayList<>();
//        // 拆单
//        Integer shopClass = shopOne.getShopClass();
//        for (ProductBuyInfoDTO dto : dtos) {
//            Product product = productService.getProductById(dto.getProductId(), null);
//            ProductSku productSku = productSkuService.getProductSkuByProductId(dto.getProductId(), null).get(0);
//            // 订单项
//            OrderItem orderItem = new OrderItem();
//            orderItem.setUnit(productSku.getUnit());
//            orderItem.setOrderId(order.getOrderId());
//            orderItem.setOrderSn(order.getOrderSn());
//            orderItem.setProductId(product.getProductId());
//            orderItem.setProductSn(product.getSerialNum());
//            orderItem.setProductName(product.getProductName());
//            orderItem.setProductImg(productSku.getSkuImg());
//            orderItem.setSkuId(productSku.getSkuId());
//            orderItem.setSkuName(productSku.getSkuName().trim());
//            orderItem.setProductPrice(productSku.getSellPrice());
//            orderItem.setBuyCounts(dto.getCartNum());
//            orderItem.setProductType(10);
//            orderItem.setTotalAmount(productSku.getSellPrice().multiply(dto.getCartNum()));
//            orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
//            orderItem.setCostPrice(productSku.getCostPrice());
//            orderItem.setOriginalPrice(productSku.getOriginalPrice());
//            orderItem.setCostAmount(productSku.getCostPrice().multiply(dto.getCartNum()));
//            orderItem.setSupplierId(product.getSupperBy());
//            if (productSku.getCostPrice() != null) {
//                orderItem.setProfitPrice(productSku.getSellPrice().subtract(productSku.getCostPrice()));
//            }
//            List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(product.getClassId());
//            String classPath = "";
//            if (CollectionUtils.isEmpty(categoryParentPath)) {
//                throw new BusinessException("分类不存在！" );
//            }else {
//                if(categoryParentPath.size() == 1) {
//                    orderItem.setClassPathName(categoryParentPath.get(0).getClassName());
//                }else {
//                    for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
//                        if (!org.springframework.util.StringUtils.isEmpty(categoryClassIdAndClassNameVO.getClassName())) {
//                            classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
//                        }
//                    }
//                    orderItem.setClassPathName(classPath.substring(0,classPath.length()-1));
//                }
//            }
//            orderItemService.save(orderItem);
//
//            // 拆单所需数据
//            if (shopClass == 2) {
////                orderItem.setOriginalPriceSum(productSku.getOriginalPrice().multiply(dto.getCartNum()));
//                orderItem.setCostPriceSum(productSku.getCostPrice().multiply(dto.getCartNum()));
//                orderItems.add(orderItem);
//            }
//
//            // 扣减库存
//            String skuId = productSku.getSkuId();
//            BigDecimal newStock = productSku.getStock().subtract(dto.getCartNum());
//            ProductSku productSku2 = new ProductSku();
//            productSku2.setSkuId(skuId);
//            productSku2.setStock(newStock);
//            productSkus.add(productSku2);
//
//            // 判断库存是否等于0如果等于0则下架商品
//            if (newStock.compareTo(BigDecimal.ZERO) == 0) {
//                outProductId.add(dto.getProductId());
//            }
//
//            // 保存计划订单
//            OrderSelectPlan orderSelectPlan = new OrderSelectPlan();
//            orderSelectPlan.setOrderSn(order.getOrderSn());
//            orderSelectPlan.setOrderItemId(orderItem.getOrderItemId());
//            orderSelectPlan.setDtlId(dto.getDtlId());
//            orderSelectPlan.setEquipmentName(product.getProductName());
//            orderSelectPlan.setCount(dto.getCartNum());
//            orderSelectPlan.setBillId(dto.getBillId());
//            orderSelectPlan.setBillNo(dto.getBillNo());
//            orderSelectPlan.setProductType(10);
//            orderSelectPlan.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
//            orderSelectPlan.setPrice(dto.getPrice());
//            orderSelectPlan.setAccount(dto.getPrice().multiply(dto.getCartNum()));
//            orderSelectPlan.setStorageId(dto.getStorageId());
//            orderSelectPlan.setStorageName(dto.getStorageName());
//            orderSelectPlan.setShortCode(dto.getShortCode());
//            orderSelectPlan.setCreditCode(dto.getCreditCode());
//            orderSelectPlan.setStorageOrgId(dto.getStorageOrgId());
//            orderSelectPlan.setPlanType(1);
//            // 如果信用代码为空表示是内部供应商
//            if (StringUtils.isEmpty(dto.getCreditCode())) {
//                orderSelectPlan.setSupplierType(2);
//            } else {
//                orderSelectPlan.setSupplierType(1);
//            }
//            orderSelectPlans.add(orderSelectPlan);
//
//            // 组装反写计划
//            HashMap<String, Object> onePlanMap = new HashMap<>();
//            onePlanMap.put("DtlId", dto.getDtlId());
//            onePlanMap.put("BillId", dto.getDtlId());
//            BigDecimal multiply = productSku.getSellPrice().multiply(dto.getCartNum());
//            BigDecimal resultAmount = multiply.setScale(2, RoundingMode.HALF_UP);
//            onePlanMap.put("Amount", resultAmount);
//            onePlanMap.put("Number", dto.getCartNum());
//            retPlanList.add(onePlanMap);
//        }
//        // 处理拆单
//        if (shopClass == 2) {
//            // 如果商品没有供应不拆单，，（废弃）都有供应商
////            List<OrderItem> collect = orderItems.stream().filter(orderItem -> orderItem.getSupplierId() != null).collect(Collectors.toList());
//
//            //（废弃） 新增商品我会保存id
////            for (OrderItem orderItem : orderItems) {
////                if (orderItem.getSupplierId() == null) {
////                    // 如果商品供方没有数据，则表示自己为供方
////                    orderItem.setSupplierId(shopOne.getEnterpriseId());
////                }
////            }
//
//            // 生成订单号
//            Map<String, List<OrderItem>> collectOrderItems = orderItems.stream()
//                    .collect(Collectors.groupingBy(OrderItem::getSupplierId));
//            collectOrderItems.forEach((supplierId, newOrderItems) -> {
//                // 保存订单、计算金额
//                Orders orders = new Orders();
//                BeanUtils.copyProperties(order, orders);
//                orders.setOrderId(null);
//                orders.setUntitled(null);
//                orders.setOrderSn(null);
//                orders.setEnterpriseId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
//
//                // 子订单没有这些金额
//                // 实际总金额
////                BigDecimal actualAmount2 = new BigDecimal(0);
//                // 总金额
////                BigDecimal totalAmount2 = new BigDecimal(0);
//                // 总成本价
//                BigDecimal costPriceTotal2 = new BigDecimal(0);
//                String untitled2 = "";
//                for (OrderItem newOrderItem : newOrderItems) {
////                    actualAmount2 = actualAmount2.add(newOrderItem.getTotalAmount());
////                    totalAmount2 = totalAmount2.add(newOrderItem.getOriginalPriceSum());
//                    costPriceTotal2 = costPriceTotal2.add(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()));
//                    untitled2 = untitled2 + newOrderItem.getProductName() + ",";
//                }
//
//                orders.setUntitled(untitled2.substring(0, untitled2.length() - 1));
//                // 理论上，在主订单包含商品多供方的情况下，如果将主订单拆分成多个子订单，那么每个子订单的销售价应
//                // 该等于对应供应商提供商品的成本价，这与主订单的成本价是相同的。
//                orders.setActualAmount(costPriceTotal2);
////                orders.setActualAmount(actualAmount2);
////                orders.setTotalAmount(totalAmount2);
//                orders.setTotalAmount(BigDecimal.ZERO);
//                orders.setCostPriceTotal(BigDecimal.ZERO);
//                orders.setOrderClass(3);
//                orders.setParentOrderId(order.getOrderId());
//                orders.setOrderSourceType(1);
//                orders.setOrderSourceId(orders.getParentOrderId());
//                EnterpriseInfo enterpriseInfoOne = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, supplierId)
//                        .select(EnterpriseInfo::getEnterpriseName).one();
//                if (enterpriseInfoOne != null) {
//                    orders.setSupplierName(enterpriseInfoOne.getEnterpriseName());
//                } else {
//                    throw new BusinessException("供应商不存在！");
//                }
//                orders.setSupplierId(supplierId);
//                orders.setOrderSn(OrderUtils.getOrder());
//                save(orders);
//                // 保存好了订单项，
//                for (OrderItem newOrderItem : newOrderItems) {
//                    newOrderItem.setOrderId(orders.getOrderId());
//                    newOrderItem.setOrderSn(orders.getOrderSn());
//                    newOrderItem.setProductPrice(newOrderItem.getCostPrice());
//                    newOrderItem.setTotalAmount(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()));
//                    newOrderItem.setCostPrice(BigDecimal.ZERO);
//                    newOrderItem.setCostAmount(BigDecimal.ZERO);
//                    newOrderItem.setOriginalPrice(BigDecimal.ZERO);
//                    // 保存父明细id
//                    newOrderItem.setParentOrderItemId(newOrderItem.getOrderItemId());
//                    newOrderItem.setOrderItemId(null);
//                }
//                orderItemService.saveBatch(newOrderItems);
//            });
//        }
//
//        // 调用反写接口
//        HashMap<Object, Object> subPlanDDTO = new HashMap<>();
//        subPlanDDTO.put("jsonrpc", "2.0");
//        subPlanDDTO.put("method", "Material.SporadicPlan.UpdateShopDtl");
//        subPlanDDTO.put("id", 1);
//        HashMap<String, String> tags = new HashMap<>();
//        UserLogin user = ThreadLocalUtil.getCurrentUser();
//        tags.put("userid", user.getFarUserId());
//        tags.put("username", user.getOriginalUserName());
//        tags.put("orgid", user.getOrgId());
//        tags.put("orgname", user.getEnterpriseName());
//        tags.put("companycode", "1000");
//        tags.put("auth_client_id", "test");
//        tags.put("auth_token", "test");
//        tags.put("platformid", "1");
//        subPlanDDTO.put("tags", tags);
//        HashMap<Object, Object> params = new HashMap<>();
//        params.put("list", retPlanList);
//        subPlanDDTO.put("params", params);
//        String content = JSON.toJSONString(subPlanDDTO);
//        log.warn("反写计划接口请求参数：" + content);
//        // 发送请求
//        HttpHeaders headers = new HttpHeaders();
//        HttpEntity<String> request = new HttpEntity<>(content, headers);
//        String url = mallConfig.pcwp1PlanUrl + "/json.rpc";
//        Map rMap = null;
//        try {
//            // 中文乱码，主要是 StringHttpMessageConverter的默认编码为ISO导致的
//            List<HttpMessageConverter<?>> templateMessageConverters = restTemplate.getMessageConverters();
//            for (HttpMessageConverter converter : templateMessageConverters) {
//                if (converter instanceof StringHttpMessageConverter) {
//                    ((StringHttpMessageConverter) converter).setDefaultCharset(Charset.forName("UTF-8"));
//                    break;
//                }
//            }
//            rMap = restTemplate.postForObject(url, request, Map.class);
//        } catch (Exception e) {
//            log.error(e.getMessage());
//            throw new BusinessException("远程反写计划服务异常！");
//        }
//        Map error = (Map) rMap.get("error");
//        if (error != null) {
//            log.error("推送计划报错：" + error);
//            throw new BusinessException("远程反写计划接口错误！");
//        }
//        String result = (String) rMap.get("result");
//        if (result != null && result.equals("操作成功")) {
//
//        } else {
//            log.error("反写计划接口错误！返回：" + rMap);
//            throw new BusinessException("远程反写计划服务异常");
//        }
//
//
//        // 保存关联计划信息
//        orderSelectPlanService.saveBatch(orderSelectPlans);
//
//        // 修改库存
//        productSkuService.updateBatchById(productSkus);
//
//        // 下架商品
//        if (!CollectionUtils.isEmpty(outProductId)) {
//            UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
//            updateProductStateDTO.setState(2);
//            updateProductStateDTO.setProductIds(outProductId);
//            productService.updateProductState(updateProductStateDTO);
//        }
//
//
//    }


    /**
     * 提交计划
     *
     * @param dtos
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Map> submitPlanAndOrder(List<ProductBuyInfoDTO> dtos) {
        ArrayList<Map> maps = new ArrayList<>();
        OrdersService ordersService = SpringBeanUtil.getBean(OrdersService.class);
        Map<String, List<ProductBuyInfoDTO>> shopIdGroup = dtos.stream()
                .collect(Collectors.groupingBy(ProductBuyInfoDTO::getShopId));
        shopIdGroup.forEach((shopId, shopIdGroupList) -> {
            HashMap<Object, Object> map = new HashMap<>();
            BigDecimal totalAccount = new BigDecimal(0);
            for (ProductBuyInfoDTO t : shopIdGroupList) {
                totalAccount = totalAccount.add(t.getSellPrice().multiply(t.getCartNum()).setScale(2, RoundingMode.HALF_UP));
            }
            try {
                ordersService.createMaterialOrder(shopIdGroupList, shopId, totalAccount, map);
            } catch (Exception e) {
                e.printStackTrace();
                map.put("shopName", shopIdGroupList.get(0).getShopName());
                map.put("cause", e.getMessage());
                maps.add(map);
                log.error("提交计划数据：" + shopIdGroupList + "----店铺--" + shopId + totalAccount);
                log.error("提交计划错误：" + e.getMessage());
            }
        });
        return maps;
    }


    /**
     * 生成零星采购订单
     *
     * @param dtos
     * @param shopId
     * @param account
     * @param map
     * @param idStr
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void PCWP2createMaterialOrder(List<ProductBuyInfoDTO> dtos, String shopId, BigDecimal account, HashMap<Object, Object> map, String idStr, String stringBuilder) {
        // 实际总金额  //订单采用计划金额，不再使用商品的金额 2025年2月10号修改
        BigDecimal actualAmount = new BigDecimal(0);
        //不含税总金额
        BigDecimal noRateActualAmount = new BigDecimal(0);

        // 总金额
        BigDecimal totalAmount = new BigDecimal(0);
        // 总成本价
        BigDecimal costPriceTotal = new BigDecimal(0);

        Shop shopOne = shopService.lambdaQuery()
                .eq(Shop::getShopId, shopId)
                .select(Shop::getShopName, Shop::getShopClass, Shop::getEnterpriseId).one();
        if (shopOne == null) {
            throw new BusinessException("店铺不存在！");
        }
        // 设置利率，计算金额
        String enterpriseId = shopOne.getEnterpriseId();
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate).one();
        String enterpriseName = enterpriseInfo.getEnterpriseName();
        BigDecimal taxRate = BigDecimal.ZERO;

        HashSet<Object> supplierIdSet = new HashSet<>();
        // 判断是不是多供方订单
        boolean flagOrderClass = false;
        String untitled = "";
        for (ProductBuyInfoDTO dto : dtos) {
            String productId = dto.getProductId();
            // 查看购物车的商品是否还上架未删除
            Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
            taxRate = dto.getTaxRate();

            if (product == null) {
                Product byId = productService.getProductExcludeRemarkById(productId);
                if (byId != null) {
                    throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
                } else {
                    throw new BusinessException(OrderEnum.RESULT_CODE_500201.getRemark());
                }
            } else {
                String supperBy = product.getSupperBy();
                // 装入set集合
                supplierIdSet.add(supperBy);
                ProductSku productSku = productSkuService.getProductSkuByProductId(productId, null).get(0);
//                // 如果计划库存低于现有库存
                int i = dto.getCartNum().compareTo(productSku.getStock());
                if (i == 1) {
                    // 大于
                    throw new BusinessException("商品名为：【" + product.getProductName() + "】的商品库存不足！");
                }
                //获取每个商品名称，以,分割，拼接成字符串
                untitled = untitled + product.getProductName() + ",";
                //推送计划下单采用商品的价格 2025年2月10号修改
                actualAmount = actualAmount.add(dto.getSellPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));
                //含税反推不含税
                BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(dto.getSellPrice(), taxRate);
//
//                //计算不含税的金额
                BigDecimal notRateAmount = TaxCalculator.noTarRateItemAmount(productSku.getSellPrice().multiply(dto.getCartNum()),
                        noRatePrice, dto.getCartNum(), taxRate);
                noRateActualAmount = noRateActualAmount.add(notRateAmount);
//
//                totalAmount = totalAmount.add(productSku.getOriginalPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));
                costPriceTotal = costPriceTotal.add(productSku.getCostPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));
            }
        }
        // 验价
//        if (Math.abs(account.subtract(actualAmount).doubleValue()) >= 0.01) {
//            throw new BusinessException("【" + untitled.substring(0, untitled.length() - 1) + "】订单总价发生变化！");
//        }

        Orders order = new Orders();
        order.setEnterpriseId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        order.setEnterpriseName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
        ProductBuyInfoDTO dtox = dtos.get(0);
        order.setReceiverName(dtox.getReceiverName());
        order.setPayWay(dtox.getPayWay());
        order.setReceiverMobile(dtox.getReceiverMobile());
        order.setReceiverAddress(dtox.getReceiverAddress());
        order.setOrderRemark(dtox.getOrderRemark());


        // 生成订单号
        order.setOrderSn(OrderUtils.getOrder());
        // 用户ID
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        order.setUserId(userId);
        order.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
        // 商品名称拼接
        order.setUntitled(untitled.substring(0, untitled.length() - 1));

        // 店铺名称
        order.setShopId(shopId);

        if (shopOne.getShopClass() == 2) {
            order.setOrderClass(2);
        } else {
            // 不是多供方，普通订单，保存商品的供应商
            Product product = productService.getProductById(dtos.get(0).getProductId(), null);
            if (product != null) {
                order.setOrderClass(1);
                order.setSupplierId(product.getSupperBy());
                order.setSupplierName(product.getSupplierName());
            }
        }

        order.setShopName(shopOne.getShopName());
        // 金额
        order.setTotalAmount(totalAmount);
        order.setActualAmount(actualAmount);
        if (mallConfig.isNotRateAmount == 1) {
            order.setNoRateAmount(noRateActualAmount);
        } else {
//            order.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(actualAmount, taxRate));
            order.setNoRateAmount(noRateActualAmount);
        }
        order.setSupplierId(enterpriseId);
        order.setSupplierName(enterpriseName);
        order.setTaxRate(taxRate);
        order.setCostPriceTotal(costPriceTotal);
        order.setProfitPriceTotal(account.subtract(costPriceTotal));
        order.setFlishTime(new Date());
        order.setState(OrderEnum.STATE_FINISH.getCode());
        order.setOrderFreight(new BigDecimal("0"));
        //  低值易耗品
        order.setProductType(10);
        // 发票状态
        order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());
        boolean save = this.save(order);
        if (!save) {
            throw new BusinessException("订单保存失败！");
        }
        ArrayList<ProductSku> productSkus = new ArrayList<>();
        ArrayList<OrderSelectPlan> orderSelectPlans = new ArrayList<>();
        ArrayList<Map<String, Object>> retPlanList = new ArrayList<>();
        // 要下架的商品id
        List<String> outProductId = new ArrayList<>();
        // 处理拆单
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        // 拆单
        Integer shopClass = shopOne.getShopClass();
        for (ProductBuyInfoDTO dto : dtos) {
            Product product = productService.getProductById(dto.getProductId(), null);
            ProductSku productSku = productSkuService.getProductSkuByProductId(dto.getProductId(), null).get(0);
            // 订单项
            OrderItem orderItem = new OrderItem();
            orderItem.setUnit(productSku.getUnit());
            orderItem.setOrderId(order.getOrderId());
            orderItem.setOrderSn(order.getOrderSn());
            orderItem.setProductId(product.getProductId());
            orderItem.setProductSn(product.getSerialNum());
            orderItem.setProductName(product.getProductName().trim());
            orderItem.setProductImg(productSku.getSkuImg());
            orderItem.setSkuId(productSku.getSkuId());
            orderItem.setSkuName(productSku.getSkuName() == null ? null : productSku.getSkuName().trim());
            ////订单采用计划金额，不再使用商品的金额 2025年2月10号修改
//            orderItem.setProductPrice(productSku.getSellPrice());
            orderItem.setProductPrice(dto.getSellPrice());
            orderItem.setRelevanceName(product.getRelevanceName().trim());
            orderItem.setRelevanceNo(product.getRelevanceNo());
            orderItem.setRelevanceId(product.getRelevanceId());
            //速率计算
            orderItem.setTaxRate(taxRate);
            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(dto.getSellPrice(), taxRate);
            orderItem.setNoRatePrice(noRatePrice);
            orderItem.setTotalAmount(dto.getSellPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));


            BigDecimal noRateAmount = TaxCalculator.noTarRateItemAmount(orderItem.getTotalAmount(), noRatePrice, dto.getCartNum(), taxRate);
            orderItem.setNoRateAmount(noRateAmount);

            orderItem.setBuyCounts(dto.getCartNum());
            orderItem.setProductType(10);
            // 如果是多供方
            if (order.getOrderClass() == 2) {
                orderItem.setState(1);
            }
            orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
            orderItem.setCostPrice(productSku.getCostPrice());
            orderItem.setOriginalPrice(productSku.getOriginalPrice());
            orderItem.setCostAmount(productSku.getCostPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));
            orderItem.setSupplierId(product.getSupperBy());
            orderItem.setClassId(product.getClassId());
            orderItem.setBrandId(product.getBrandId());
            orderItem.setBrandName(product.getBrandName());
            if (productSku.getCostPrice() != null) {
                orderItem.setProfitPrice(dto.getSellPrice().subtract(productSku.getCostPrice()));
            }
            List<CategoryClassIdAndClassNameVO> categoryParentPath = productCategoryService.getCategoryParentPath(product.getClassId());
            String classPath = "";
            String classPathId = "";
            if (CollectionUtils.isEmpty(categoryParentPath)) {
                throw new BusinessException("分类不存在！");
            } else {
                if (categoryParentPath.size() == 1) {
                    orderItem.setClassPathName(categoryParentPath.get(0).getClassName());
                    orderItem.setClassPathId(categoryParentPath.get(0).getClassId());
                } else {
                    for (CategoryClassIdAndClassNameVO categoryClassIdAndClassNameVO : categoryParentPath) {
                        if (!org.springframework.util.StringUtils.isEmpty(categoryClassIdAndClassNameVO.getClassName())) {
                            classPath += categoryClassIdAndClassNameVO.getClassName() + "/";
                            classPathId += categoryClassIdAndClassNameVO.getClassId() + "/";
                        }
                    }
                    orderItem.setClassPathName(classPath.substring(0, classPath.length() - 1));
                    orderItem.setClassPathId(classPathId.substring(0, classPathId.length() - 1));
                }
            }
            orderItemService.save(orderItem);

            // 拆单所需数据
            if (shopClass == 2) {
//                orderItem.setOriginalPriceSum(productSku.getOriginalPrice().multiply(dto.getCartNum()));
                orderItem.setCostPriceSum(productSku.getCostPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));
                orderItems.add(orderItem);
            }

            // 扣减库存
            String skuId = productSku.getSkuId();
            BigDecimal newStock = productSku.getStock().subtract(dto.getCartNum());
            ProductSku productSku2 = new ProductSku();
            productSku2.setSkuId(skuId);
            productSku2.setStock(newStock);
            productSkus.add(productSku2);

            // 判断库存是否等于0如果等于0则下架商品
            if (newStock.compareTo(BigDecimal.ZERO) == 0) {
                outProductId.add(dto.getProductId());
            }

            // 保存计划订单
            OrderSelectPlan orderSelectPlan = new OrderSelectPlan();

            orderSelectPlan.setOrderId(order.getOrderId());
            orderSelectPlan.setOrderSn(order.getOrderSn());
            orderSelectPlan.setOrderItemId(orderItem.getOrderItemId());
            orderSelectPlan.setDtlId(dto.getDtlId());
            orderSelectPlan.setEquipmentName(product.getProductName());
            orderSelectPlan.setCount(dto.getCartNum());
            orderSelectPlan.setBillId(dto.getBillId());
            orderSelectPlan.setBillNo(dto.getBillNo());
            orderSelectPlan.setProductType(10);
            orderSelectPlan.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
            orderSelectPlan.setOrgName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
            orderSelectPlan.setPrice(dto.getPrice());
            orderSelectPlan.setAccount(dto.getPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP));
            orderSelectPlan.setStorageId(dto.getStorageId());
            orderSelectPlan.setStorageName(dto.getStorageName());
            orderSelectPlan.setShortCode(dto.getShortCode());
            orderSelectPlan.setCreditCode(dto.getCreditCode());
            orderSelectPlan.setStorageOrgId(dto.getStorageOrgId());
            orderSelectPlan.setPlanType(1);
            orderSelectPlan.setTaxRate(order.getTaxRate());
            // 如果信用代码为空表示是内部供应商
            if (StringUtils.isEmpty(dto.getCreditCode())) {
                orderSelectPlan.setSupplierType(2);
            } else {
                orderSelectPlan.setSupplierType(1);
            }
            orderSelectPlans.add(orderSelectPlan);

            // 组装反写计划
            HashMap<String, Object> onePlanMap = new HashMap<>();
            onePlanMap.put("dtlId", dto.getDtlId());
            onePlanMap.put("billId", dto.getBillId());
            BigDecimal multiply = dto.getSellPrice().multiply(dto.getCartNum()).setScale(2, RoundingMode.HALF_UP);
            //20250102 pcwp要求amount 是不含税金额
            BigDecimal noRatePrice2 = TaxCalculator.calculateNotTarRateAmount(multiply, taxRate);
            onePlanMap.put("amount", noRatePrice2);
            onePlanMap.put("number", dto.getCartNum());
            retPlanList.add(onePlanMap);
        }
        // 处理拆单
        if (shopClass == 2) {
            // 生成订单号
            Map<String, List<OrderItem>> collectOrderItems = orderItems.stream()
                    .collect(Collectors.groupingBy(OrderItem::getSupplierId));
            collectOrderItems.forEach((supplierId, newOrderItems) -> {
                // 保存订单、计算金额
                Orders orders = new Orders();
                BeanUtils.copyProperties(order, orders);
                orders.setOrderId(null);
                orders.setUntitled(null);
                orders.setOrderSn(null);
                orders.setEnterpriseId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
                orders.setEnterpriseName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
                // 总成本价
                BigDecimal costPriceTotal2 = new BigDecimal(0);

                EnterpriseInfo enterpriseInfoOne = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, supplierId)
                        .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate).one();
                if (enterpriseInfoOne != null) {
                    orders.setSupplierId(supplierId);
                    orders.setSupplierName(enterpriseInfoOne.getEnterpriseName());
                } else {
                    throw new BusinessException("供应商不存在！");
                }
                BigDecimal taxRate2 = enterpriseInfoOne.getTaxRate();
                if (taxRate2 == null || taxRate2.compareTo(BigDecimal.ZERO) == 0) {
                    throw new BusinessException("【" + enterpriseInfoOne.getEnterpriseName() + "】供应商未设置利率！");
                }
                BigDecimal noRateActualAmount2 = new BigDecimal(0);
                String untitled2 = "";
                for (OrderItem newOrderItem : newOrderItems) {
                    costPriceTotal2 = costPriceTotal2.add(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
                    BigDecimal noRatePrice2 = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate2);
                    BigDecimal noRateAmount = TaxCalculator.noTarRateItemAmount(
                            newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()), noRatePrice2, newOrderItem.getBuyCounts(), taxRate2);
                    noRateActualAmount2 = noRateActualAmount2.add(noRateAmount);
                    untitled2 = untitled2 + newOrderItem.getProductName() + ",";
                }

                orders.setUntitled(untitled2.substring(0, untitled2.length() - 1));
                // 理论上，在主订单包含商品多供方的情况下，如果将主订单拆分成多个子订单，那么每个子订单的销售价应
                // 该等于对应供应商提供商品的成本价，这与主订单的成本价是相同的。
                orders.setActualAmount(costPriceTotal2);

                orders.setTotalAmount(BigDecimal.ZERO);
                orders.setCostPriceTotal(BigDecimal.ZERO);
                orders.setOrderClass(3);
                orders.setParentOrderId(order.getOrderId());
                orders.setOrderSourceType(1);
                orders.setOrderSourceId(orders.getParentOrderId());


                orders.setTaxRate(taxRate2);
                if (mallConfig.isNotRateAmount == 1) {
                    orders.setNoRateAmount(noRateActualAmount2);
                } else {
                    orders.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(costPriceTotal2, taxRate2));
                }
                orders.setOrderSn(OrderUtils.getOrder());
                orders.setOutKeyId(idStr);
                save(orders);
                // 保存好了订单项，
                for (OrderItem newOrderItem : newOrderItems) {

                    newOrderItem.setOrderId(orders.getOrderId());
                    newOrderItem.setOrderSn(orders.getOrderSn());
                    newOrderItem.setProductPrice(newOrderItem.getCostPrice());
                    newOrderItem.setTotalAmount(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));

                    newOrderItem.setTaxRate(taxRate2);
                    newOrderItem.setNoRatePrice(TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate2).setScale(2, RoundingMode.HALF_UP));
                    BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate2).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal noRateAmount = TaxCalculator.noTarRateItemAmount(newOrderItem.getTotalAmount(), noRatePrice, newOrderItem.getBuyCounts(), taxRate2);
                    newOrderItem.setNoRateAmount(noRateAmount);

                    newOrderItem.setCostPrice(BigDecimal.ZERO);
                    newOrderItem.setCostAmount(BigDecimal.ZERO);
                    newOrderItem.setOriginalPrice(BigDecimal.ZERO);
                    // 保存父明细id
                    newOrderItem.setParentOrderItemId(newOrderItem.getOrderItemId());
                    newOrderItem.setOrderItemId(null);
                }
                orderItemService.saveBatch(newOrderItems);
            });
        }

        // 调用反写接口
        HashMap<Object, Object> subPlanDDTO = new HashMap<>();
        subPlanDDTO.put("data", retPlanList);
        subPlanDDTO.put("keyId", idStr);
        subPlanDDTO.put("orgId", ThreadLocalUtil.getCurrentUser().getOrgId());
        String content = JSON.toJSONString(subPlanDDTO);
        stringBuilder = content;
        log.warn("反写计划接口请求参数：" + content);
        // 发送请求
//        HttpHeaders headers = new HttpHeaders();
        String url = mallConfig.prodPcwp2Url02 + "/thirdapi/sporadicPurchasePlan/updateShopDtl";
        LogUtil.writeInfoLog(idStr, "PCWP2createMaterialOrder", dtos, subPlanDDTO, null, OrdersServiceImpl.class);
        R rMap = null;
        try {
            rMap = restTemplateUtils.postPCWP2(url, subPlanDDTO);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "PCWP2createMaterialOrder", dtos, subPlanDDTO, null, e.getMessage(), OrdersServiceImpl.class);
            log.error(e.getMessage());
            throw new BusinessException("远程异常！" + e.getMessage());
        }
        if (rMap.getCode() == null || rMap.getCode() != 200) {
            LogUtil.writeErrorLog(idStr, "PCWP2createMaterialOrder", dtos, subPlanDDTO, rMap, rMap.getMessage(), OrdersServiceImpl.class);
            log.error("反写计划接口错误！返回：" + rMap.getMessage());
            throw new BusinessException(500, "远程异常！" + rMap.getMessage());
        }

        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(OrdersServiceImpl.class.getName());
        iLog.setMethodName("PCWP2createMaterialOrder");
        iLog.setLocalArguments(JSON.toJSONString(dtos));
        iLog.setFarArguments(content);
        iLog.setIsSuccess(1);
        iLog.setLogType(1);
        iLog.setErrorInfo(null);
        interfaceLogsService.create(iLog);
        // 保存关联计划信息
        orderSelectPlanService.saveBatch(orderSelectPlans);

        // 修改库存
        productSkuService.updateBatchById(productSkus);

        // 下架商品
        if (!CollectionUtils.isEmpty(outProductId)) {
            UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
            updateProductStateDTO.setState(2);
            updateProductStateDTO.setProductIds(outProductId);
            productService.updateProductState(updateProductStateDTO);
        }
    }


    /**
     * 回调反写零星采购计划商城订单数量
     *
     * @param idStr
     */


    private void deleteKey(String idStr) {
        R r = null;
        try {
            r = restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + "/thirdapi/sporadicPurchasePlan/rollBackUpdateShopDtl?keyId=" + idStr);
        } catch (Exception e) {
            throw new BusinessException("远程异常：远程反写零星采购计划商城订单数量方法接口出错");
        }
        if (r == null || r.getCode() != 200) {
            throw new BusinessException("远程异常：回滚反写零星采购计划商城订单数量方法调用失败" + r);
        }


    }


    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 生成零星采购订单
     *
     * @param dtos
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Map> PCWP2submitPlanAndOrder(List<ProductBuyInfoDTO> dtos) {
        ArrayList<Map> maps = new ArrayList<>();
        OrdersService ordersService = SpringBeanUtil.getBean(OrdersService.class);
        //根据店铺id进行分组
        Map<String, List<ProductBuyInfoDTO>> shopIdGroup = dtos.stream()
                .collect(Collectors.groupingBy(ProductBuyInfoDTO::getShopId));
        shopIdGroup.forEach((shopId, shopIdGroupList) -> {
            String idStr = IdWorker.getIdStr();
            HashMap<Object, Object> map = new HashMap<>();
            BigDecimal totalAccount = new BigDecimal(0);
            for (ProductBuyInfoDTO t : shopIdGroupList) {
                totalAccount = totalAccount.add(t.getSellPrice().multiply(t.getCartNum()).setScale(2, RoundingMode.HALF_UP));
            }
            String stringBuilder = "";
            try {
                String clientId = UUID.randomUUID().toString();
                Boolean b = stringRedisTemplate.opsForValue().setIfAbsent(MaterialLockUtils.PUSH_LX_ORDER_LOCK + ":" + dtos.get(0).getBillNo(), clientId, 2, TimeUnit.MINUTES);
                if (b) {
                    ordersService.PCWP2createMaterialOrder(shopIdGroupList, shopId, totalAccount, map, idStr, stringBuilder);
                } else {
                    throw new BusinessException("请勿频繁推送订单数据");
                }
            } catch (Exception e) {
                LogUtil.writeErrorLog(idStr, "PCWP2submitPlanAndOrder", dtos, null, null, e.getMessage(), OrdersServiceImpl.class);
                InterfaceLogs iLog = new InterfaceLogs();
                iLog.setSecretKey(idStr);
                iLog.setClassPackage(UserCenterOrderController.class.getName());
                iLog.setMethodName("PCWP2submitPlanAndOrder");
                iLog.setLocalArguments(JSON.toJSONString(dtos));
                iLog.setFarArguments(stringBuilder.toString());
                iLog.setErrorInfo(e.getMessage());
                interfaceLogsService.createFailLog(iLog);
                e.printStackTrace();
                map.put("shopName", shopIdGroupList.get(0).getShopName());
                map.put("cause", e.getMessage());
                maps.add(map);
                log.error("提交计划数据：" + shopIdGroupList + "----店铺--" + shopId + totalAccount);
                log.error("提交计划错误：" + e.getMessage());
                try {
                    deleteKey(idStr);
                    InterfaceLogs rollBack = new InterfaceLogs();
                    rollBack.setSecretKey(idStr);
                    rollBack.setClassPackage(UserCenterOrderController.class.getName());
                    rollBack.setMethodName("PCWP2submitPlanAndOrder(rollBack)");
                    rollBack.setLocalArguments(JSON.toJSONString(dtos));
                    rollBack.setIsSuccess(1);
                    rollBack.setLogType(2);
                    rollBack.setErrorInfo(e.getMessage());
                    interfaceLogsService.create(rollBack);
                } catch (Exception ex) {
                    InterfaceLogs rollBack1 = new InterfaceLogs();
                    rollBack1.setSecretKey(idStr);
                    rollBack1.setClassPackage(UserCenterOrderController.class.getName());
                    rollBack1.setMethodName("PCWP2submitPlanAndOrder(rollBack)");
                    rollBack1.setLocalArguments(JSON.toJSONString(dtos));
                    rollBack1.setIsSuccess(0);
                    rollBack1.setLogType(1);
                    rollBack1.setErrorInfo(e.getMessage());
                    interfaceLogsService.create(rollBack1);
                }
                throw new BusinessException(e.getMessage());
            }
        });
        return maps;
    }

    @Override
    public PageUtils purchaseList(JSONObject jsonObject, LambdaQueryWrapper<Orders> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        q.eq(Orders::getProductType, 10);
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        q.eq(Orders::getOrgId, user.getOrgId());
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), Orders::getGmtCreate, startDate, endDate);
        q.orderByDesc(Orders::getGmtCreate);
        IPage<Orders> page = this.page(
                new Query<Orders>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    /**
     * 清除供应商
     *
     * @param orderId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeOrderSupplier(String orderId) {
        // TODO 修改没意义
        lambdaUpdate().eq(Orders::getOrderId, orderId)
                .set(Orders::getSupplierId, null)
                .set(Orders::getSupplierName, null)
                .set(Orders::getMasterAffirmState, 0)
                .set(Orders::getAffirmState, 3)
                .update();
        orderItemService.lambdaUpdate().eq(OrderItem::getOrderId, orderId)
                .set(OrderItem::getSupplierId, null).update();

        // 清除供应商后，让主订单明细id，状态更改为待分配
        List<OrderItem> list = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, orderId)
                .select(OrderItem::getParentOrderItemId).list();
        List<String> items = list.stream().map(t -> t.getParentOrderItemId()).collect(Collectors.toList());
        orderItemService.lambdaUpdate().in(OrderItem::getOrderItemId, items)
                .set(OrderItem::getState, 2)
                .set(OrderItem::getGmtModified, new Date()).update();
        // 删除源单
        delete(orderId);
        orderItemService.lambdaUpdate().eq(OrderItem::getOrderId, orderId).remove();
    }

    /**
     * 查询拆的订单
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils getTwoOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        Integer state = (Integer) innerMap.get("state");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        String startDate = (String) innerMap.get("startDate");
        String parentOrderId = (String) innerMap.get("parentOrderId");
        String endDate = (String) innerMap.get("endDate");
        String okStartDate = (String) innerMap.get("okStartDate");
        String okEndDate = (String) innerMap.get("okEndDate");
        String deliverGoodsStartDate = (String) innerMap.get("deliverGoodsStartDate");
        String deliverGoodsEndDate = (String) innerMap.get("deliverGoodsEndDate");
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        Integer productType = (Integer) innerMap.get("productType");
        String shopName = (String) innerMap.get("shopName");
        String untitled = (String) innerMap.get("untitled");
        String orderSn = (String) innerMap.get("orderSn");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        Integer affirmState = (Integer) innerMap.get("affirmState");
        List<Integer> affirmStates = (List<Integer>) innerMap.get("affirmStates");

        // 只查询本机构相关联的二级订单
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        q.eq(Orders::getSupplierId, enterpriseId);
        // 只能是已经主订单方确认的订单
        q.eq(Orders::getMasterAffirmState, 1);
        // 查询是拆单的订单
        q.eq(Orders::getOrderClass, 3);
        q.eq(affirmState != null, Orders::getAffirmState, affirmState);
        q.in(!CollectionUtils.isEmpty(affirmStates), Orders::getAffirmState, affirmStates);
        q.like(StringUtils.isNotEmpty(shopName), Orders::getShopName, shopName);
        q.like(StringUtils.isNotEmpty(untitled), Orders::getUntitled, untitled);
        q.like(StringUtils.isNotEmpty(orderSn), Orders::getOrderSn, orderSn);
        q.eq(StringUtils.isNotEmpty(parentOrderId), Orders::getParentOrderId, parentOrderId);
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(Orders::getOrderSn, keywords)
                        .or()
                        .like(Orders::getUntitled, keywords)
                        .or()
                        .like(Orders::getShopName, keywords)
                        .or()
                        .like(Orders::getReceiverMobile, keywords);
            });
        }
        q.ge(StringUtils.isNotBlank(abovePrice), Orders::getActualAmount, abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), Orders::getActualAmount, belowPrice);
        q.eq(orderClass != null, Orders::getOrderClass, orderClass);
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), Orders::getGmtCreate, startDate, endDate);
        q.between(StringUtils.isNotEmpty(okStartDate) && StringUtils.isNotEmpty(okEndDate), Orders::getFlishTime, okStartDate, okEndDate);
        q.between(StringUtils.isNotEmpty(deliverGoodsStartDate) && StringUtils.isNotEmpty(deliverGoodsEndDate), Orders::getDeliveryTime, deliverGoodsStartDate, deliverGoodsEndDate);
        q.eq(state != null, Orders::getState, state);
        q.eq(productType != null, Orders::getProductType, productType);
        q.eq(Orders::getMallType, mallConfig.mallType);
        q.eq(Orders::getIsDelete, PublicEnum.IS_DELETE_NO.getCode());
        if (orderBy == null) {
            orderBy = 1;
        }
        if (orderBy == 1) {
            q.orderByDesc(Orders::getGmtCreate);
        }
        if (orderBy == 2) {
            q.orderByDesc(Orders::getFlishTime);
        }
        if (orderBy == 3) {
            q.orderByDesc(Orders::getDeliveryTime);
        }
        IPage<Orders> page = this.page(
                new Query<Orders>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }

    /**
     * 店铺订单增加供应商
     *
     * @param orders
     */
    @Override
    public void addOrderSupplier(Orders orders) {
        Orders byId = getById(orders.getOrderId());
        if (byId != null) {
            byId.setSupplierId(orders.getSupplierId());
            byId.setSupplierName(orders.getSupplierName());
            byId.setAffirmState(orders.getAffirmState());
            updateById(byId);

            // 废弃、
//            List<OrderItem> list = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, orders.getOrderId())
//                    .select(OrderItem::getParentOrderItemId).list();
//            List<String> items = list.stream().map(t -> t.getParentOrderItemId()).collect(Collectors.toList());
//            orderItemService.lambdaUpdate().in(OrderItem::getOrderItemId,items)
//                    .set(OrderItem::getState,1)
//                    .set(OrderItem::getGmtModified,new Date()).update();
        } else {
            throw new BusinessException("订单不存在！");
        }
    }

    /**
     * 确认订单
     *
     * @param orderId
     * @param isAffirm
     */
    @Override
    public void affirmTwoOrder(String orderId, Boolean isAffirm) {
        if (StringUtils.isEmpty(orderId) || isAffirm == null) {
            return;
        }
        Orders byId = getById(orderId);
        if (byId != null) {
            if (isAffirm) {
                byId.setAffirmState(1);
                byId.setFlishTime(new Date());
            } else {
                byId.setAffirmState(2);
            }
            updateById(byId);
        } else {
            throw new BusinessException("修改失败");
        }

    }

    /**
     * 主订单方确认订单
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void masterAffirmTwoOrder(MasterAffirmTwoOrderDTO dto) {
        List<String> orderIds = dto.getOrderIds();
        Boolean isAffirm = dto.getIsAffirm();
        for (String orderId : orderIds) {
            Orders byId = getById(orderId);
            if (byId != null) {
                if (isAffirm) {
                    byId.setMasterAffirmState(1);
                } else {
                    byId.setMasterAffirmState(2);
                }
                updateById(byId);
            } else {
                throw new BusinessException("修改失败");
            }
        }
    }

    /**
     * 批量修改二级订单供方价格
     *
     * @param dtos
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateTwoOrderPrice(List<UpdateTwoOrderPriceDTO> dtos) {
        String twoOrderId = null;
        String oneOrderId = null;
        for (UpdateTwoOrderPriceDTO dto : dtos) {
            BigDecimal productPrice = dto.getProductPrice();
            String orderItemId = dto.getOrderItemId();
            // 改了二级订单的供方价格此时应该修改主订单成本价
            OrderItem orderItemTwo = orderItemService.getById(orderItemId);
            if (twoOrderId == null) {
                twoOrderId = orderItemTwo.getOrderId();
            }
            orderItemTwo.setProductPrice(productPrice);
            BigDecimal totalCostPrice = orderItemTwo.getBuyCounts().multiply(productPrice).setScale(2, RoundingMode.HALF_UP);
            orderItemTwo.setTotalAmount(totalCostPrice);

            // 主订单
            Orders orderOne = getById(oneOrderId);
            // 税率
            orderItemTwo.setNoRatePrice(TaxCalculator.calculateNotTarRateAmount(productPrice, orderItemTwo.getTaxRate()));
            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(productPrice, orderItemTwo.getTaxRate());
//            orderItemTwo.setNoRateAmount(noRatePrice.multiply(orderItemTwo.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
            orderItemTwo.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(totalCostPrice, orderOne.getTaxRate()));


            orderItemService.update(orderItemTwo);

            // 修改父级明细价格
            String parentOrderItemId = orderItemTwo.getParentOrderItemId();
            OrderItem orderItemOne = orderItemService.getById(parentOrderItemId);
            orderItemOne.setCostPrice(productPrice);
            orderItemOne.setCostAmount(totalCostPrice);
            if (oneOrderId == null) {
                oneOrderId = orderItemOne.getOrderId();
            }
            orderItemService.update(orderItemOne);
        }
        // 明细都修改完成后，主订单重新统计金额，主订单计算成本价，二级订单计算销售价

        // 主订单
        Orders orderOne = getById(oneOrderId);
        // 所有明细
        List<OrderItem> orderItemsOne = orderItemService.lambdaQuery()
                .eq(OrderItem::getOrderId, orderOne.getOrderId()).list();
        BigDecimal costAmount = new BigDecimal(0);
        for (OrderItem orderItem : orderItemsOne) {
            // 计算总成本价
            BigDecimal c = orderItem.getCostAmount();
            costAmount = costAmount.add(c);
        }
        orderOne.setCostPriceTotal(costAmount);
        update(orderOne);

        // 二级订单
        Orders orderTwo = getById(twoOrderId);
        // 所有明细
        List<OrderItem> orderItemsTwo = orderItemService.lambdaQuery()
                .eq(OrderItem::getOrderId, orderTwo.getOrderId()).list();
        BigDecimal actualAmount = new BigDecimal(0);
        BigDecimal noRateactualAmount = new BigDecimal(0);
        for (OrderItem orderItem : orderItemsTwo) {
            // 计算总成本价
            actualAmount = actualAmount.add(orderItem.getTotalAmount());
            noRateactualAmount = noRateactualAmount.add(orderItem.getNoRateAmount());
        }
        orderTwo.setActualAmount(actualAmount);
        // 税率
        if (mallConfig.isNotRateAmount == 1) {
            orderTwo.setNoRateAmount(noRateactualAmount);
        } else {
            orderTwo.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(actualAmount, orderTwo.getTaxRate()));
        }
        update(orderTwo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeOrder(String orderId) {
        boolean flag = true;
        Orders orders = getById(orderId);
        //子订单集合
        List<Orders> ordersList = null;

        //查找所有子订单
        if (StringUtils.isNotBlank(orders.getParentOrderId())) {
            Orders parentOrder = getById(orders.getParentOrderId());
            orders = parentOrder;
            orderId = parentOrder.getOrderId();
            ordersList = getDataByParentId(parentOrder.getOrderId());
        } else {
            ordersList = getDataByParentId(orderId);
        }
        if (ordersList != null && ordersList.size() > 0) {
            for (Orders sonOrders : ordersList) {
                boolean sonFlag = true;
                List<OrderItem> sonList = orderItemService.findAllByOrderId(sonOrders.getOrderId());
                for (OrderItem orderItem : sonList) {
                    BigDecimal returnCounts = orderReturnItemService.getDataByOrderItmId(orderItem.getParentOrderItemId(), 0);
                    if (orderItem.getBuyCounts().compareTo(orderItem.getShipCounts().add(returnCounts)) > 0) {
                        flag = false;
                        sonFlag = false;
                    }
                    //如果发货数量+退货数量==购买数量，查看所有发货单是否已确认
                    else if (orderItem.getBuyCounts().compareTo(orderItem.getShipCounts().add(returnCounts)) < 0) {
                        throw new BusinessException("发货单有误");
                    } else {
                        List<OrderShip> orderShips = orderShipService.getListByOrderId(sonOrders.getOrderId());
                        if (orderShips != null && orderShips.size() > 0) {
                            for (OrderShip orderShip : orderShips) {
                                if (orderShip.getType() != 2) {
                                    flag = false;
                                    sonFlag = false;

                                }
                            }
                        } else {
                            flag = false;
                            sonFlag = false;

                        }

                    }
                }
                if (sonFlag) {
                    sonOrders.setState(10);
                    sonOrders.setSuccessDate(new Date());
                    update(sonOrders);
                }

            }
        } else {
            //，主订单
            List<OrderItem> orderItems = orderItemService.findAllByOrderId(orderId);


            for (OrderItem orderItem : orderItems) {
                //查看订单项购买数量=发货数量+商城退货数量
                BigDecimal returnCounts = orderReturnItemService.getDataByOrderItmId(orderItem.getParentOrderItemId(), 0);
                if (orderItem.getBuyCounts().compareTo(orderItem.getShipCounts().add(returnCounts)) > 0) {
                    flag = false;
                    break;
                }
                //如果发货数量+退货数量==购买数量，
                else if (orderItem.getBuyCounts().compareTo(returnCounts) < 0) {
                    throw new BusinessException("发货单有误");
                } else {
                    // 查看所有发货单是否存在
                    List<OrderShip> orderShips = orderShipService.getListByOrderId(orderId);
                    if (orderShips != null && orderShips.size() > 0) {
                        for (OrderShip orderShip : orderShips) {
                            //  查看所有发货单是否已确认
                            if (orderShip.getType() != 2) {
                                flag = false;
                                break;
                            }
                        }
                    } else {
                        flag = false;
                        break;
                    }

                }
            }
        }
        if (flag) {
            orders.setState(10);
            orders.setSuccessDate(new Date());
            update(orders);
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeOrder2(String orderId) {
        boolean flag = true;
        Orders orders = getById(orderId);
        // 订单数量-商城退货>发货数量-pcwp退货数量时，为未全部发货。flag = false
        flag = closeOrderItems(orderId, flag);
        if (flag) {
            // 已发货完成
            orders.setSuccessDate(new Date());
            orders.setState(10);
            update(orders);
            //如果订单是多供方订单，查询所有二级订单，把没有完结的子订单设为完成
            if (orders.getOrderClass() == 2) {
                List<Orders> sonOrders = getDataByParentId(orderId);
                ArrayList<Orders> sonList = new ArrayList<>();
                if (sonOrders != null && sonOrders.size() > 0) {
                    for (Orders son : sonOrders) {
                        if (son.getState() != 10) {
                            son.setSuccessDate(new Date());
                            son.setState(10);
                            sonList.add(son);
                        }
                    }
                    if (sonList.size() > 0) {
                        updateBatch(sonList);
                    }
                }
            }
            //如果订单是子订单，查询其他子订单，所有子订单完成，主订单就完成
            else if (orders.getOrderClass() == 3) {
                List<Orders> dataByParentId = getDataByParentId(orders.getParentOrderId());
                boolean parentFlag = true;
                for (Orders sonOrder : dataByParentId) {
                    if (sonOrder.getState() != 10) {
                        parentFlag = false;
                        break;
                    }
                }
                // parentFlag ture订单完成  false 主订单未完成         所有子订单完成，主订单就完成
                if (parentFlag) {
                    Orders byId = getById(orders.getParentOrderId());
                    byId.setSuccessDate(new Date());
                    byId.setState(10);
                    update(byId);
                }
            }
        } else {
            // 主 订单未完成
            if (orders.getOrderClass() == 2) {
                //查询所有子订单，对已经完成的子订单进行关闭
                List<Orders> sonOrders = getDataByParentId(orderId);
                if (sonOrders != null && sonOrders.size() > 0) {
                    for (Orders sonOrder : sonOrders) {
                        if (sonOrder.getState() != 10) {
                            boolean sonFlag = true;
                            boolean b = closeOrderItems(sonOrder.getOrderId(), sonFlag);
                            if (b) {
                                sonOrder.setSuccessDate(new Date());
                                sonOrder.setState(10);
                                update(sonOrder);
                            }
                        }
                    }
                }
            }   // 子 订单未完成
            else if (orders.getOrderClass() == 3) {
                List<Orders> dataByParentId = getDataByParentId(orders.getParentOrderId());
                if (dataByParentId != null && dataByParentId.size() > 0) {
                    for (Orders sonOrder : dataByParentId) {
                        if (sonOrder.getState() != 10) {
                            boolean sonFlag = true;
                            boolean b = closeOrderItems(sonOrder.getOrderId(), sonFlag);
                            if (b) {
                                sonOrder.setSuccessDate(new Date());
                                sonOrder.setState(10);
                                update(sonOrder);
                            }
                        }
                    }
                }
            }
        }

    }

    private boolean closeOrderItems(String orderId, boolean flag) {
        List<OrderItem> list = orderItemService.findByOrderIdList(orderId);
        Orders byId = getById(orderId);
        Integer orderClass = byId.getOrderClass();

        for (OrderItem orderItem : list) {
            //购买数量-商城退货数量《=发货数量-pcwp退货数量 为待收货状态 ，其他为代发货状态
            BigDecimal mReturnCounts = orderItem.getReturnCounts();
            BigDecimal pcwpRetuen = orderItem.getPcwpReturn();
            BigDecimal buyCounts = orderItem.getBuyCounts();
            BigDecimal confirmCounts = orderItem.getConfirmCounts();

//           当订单收货数量-pwcp退货数量>=订单数量-商城退货数量,并且所有发货单都已收货，则表示订单已完成。
            int i = confirmCounts.subtract(pcwpRetuen).compareTo(buyCounts.subtract(mReturnCounts));
            List<OrderShipDtl> dtls = orderShipDtlService.lambdaQuery()
                    .eq(OrderShipDtl::getOrderItemId, orderItem.getOrderItemId())
                    .select(OrderShipDtl::getBillId, OrderShipDtl::getShipCounts, OrderShipDtl::getShipNum).list();
            boolean f1 = false;
            if (CollectionUtils.isEmpty(dtls)) {
                OrderItem orderItemTwo = orderItemService.lambdaQuery().eq(OrderItem::getParentOrderItemId, orderItem.getOrderItemId())
                        .select(OrderItem::getOrderId, OrderItem::getOrderItemId, OrderItem::getBuyCounts).one();
                if (orderItemTwo != null) {
                    dtls = orderShipDtlService.lambdaQuery()
                            .eq(OrderShipDtl::getOrderItemId, orderItemTwo.getOrderItemId())
                            .select(OrderShipDtl::getBillId, OrderShipDtl::getShipCounts, OrderShipDtl::getShipNum).list();
                }
            }
            for (OrderShipDtl dtl : dtls) {
                OrderShip orderShip = orderShipService.lambdaQuery().eq(OrderShip::getBillId, dtl.getBillId())
                        .select(OrderShip::getBillId, OrderShip::getType).one();
                // 存在未收货数据
                if (orderShip.getType() == 0 || orderShip.getType() == 1) {
                    f1 = true;
                }
            }
            if (i == -1 || f1) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    /**
     * 退货修改金额
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void returnProductUpdateOrder(ReturnProductUpdateOrderDTO dto) {
//
//        // TODO 废弃，如果使用需要计算利率后金额
//        String orderItemId = dto.getOrderItemId();
//        String orderId = dto.getOrderId();
//        BigDecimal number = dto.getNumber();
//        int numberBool = number.compareTo(BigDecimal.ZERO);
//        if (numberBool == 0 || numberBool == -1) throw new BusinessException("退货数量不能小于或等于0");
//        OrderItem orderItem = orderItemService.getById(orderItemId);
//        Orders orders = getById(orderId);
//        if (orders == null || orderItem == null) {
//            throw new BusinessException("订单不存在！");
//        }
//        Integer count = orderItemService.lambdaQuery()
//                .eq(OrderItem::getOrderId, orderId)
//                .count();
//        Integer orderClass = orders.getOrderClass();
//
//
//        // 拿到订单关联计划信息
//        OrderSelectPlan orderSelectPlan = orderSelectPlanService.lambdaQuery()
//                .eq(OrderSelectPlan::getOrderItemId, orderItemId).one();
//        if (orderSelectPlan == null) throw new BusinessException("订单关联计划数据不存在！");
//
//        // 相等： 单明细全退
//        List<Map<String, Object>> retPlanList = new ArrayList<>();
//        // 组装反写计划
//        HashMap<String, Object> onePlanMap = new HashMap<>();
//        onePlanMap.put("DtlId", orderSelectPlan.getDtlId());
//        onePlanMap.put("BillId", orderSelectPlan.getBillId());
//        // 传入负数
//        onePlanMap.put("Amount", orderItem.getProductPrice().multiply(number)
//                .negate().setScale(2, RoundingMode.HALF_UP));
//        onePlanMap.put("Number", number.negate().setScale(3, RoundingMode.HALF_UP));
//        retPlanList.add(onePlanMap);
//
//        // 获取订单项数量
//        BigDecimal buyCounts = orderItem.getBuyCounts();
//
//
//        // 不管什么类型的订单都要还原库存
//        ProductSku productSku = productSkuService.getById(orderItem.getSkuId());
//        BigDecimal newStock = productSku.getStock().add(number);
//        ProductSku productSkuUpdate = new ProductSku();
//        productSkuUpdate.setSkuId(orderItem.getSkuId());
//        productSkuUpdate.setStock(newStock);
//        productSkuService.update(productSkuUpdate);
//
//        // TODO 是否因为库存下架的商品重新上架
//
//        // 普通订单退货
//        if (orderClass == 1) {
//            /**
//             * 1、单明细全退
//             * 2、单明细部分退
//             * 3、多明细全退
//             * 4、多明细部分退
//             */
//            if (count == 1) {
//                if (buyCounts.compareTo(number) == 0) {
//                    // 单明细全退
//
//                    // 全部退修改状态即可
//                    orders.setState(7);
//                    orders.setCloseType(5);
//                    update(orders);
//                    // TODO 先删除，后续不删除再讨论
//                    delete(orderId);
//                    orderItemService.delete(orderItemId);
//                    // 删除关联的计划
//                    orderSelectPlanService.delete(orderSelectPlan.getOrderSelectPlanId());
//
//
//                } else if (buyCounts.compareTo(number) == 1) {
//                    // 单明细部分退
//
//                    // 重新计算金额
//                    BigDecimal subNumber = buyCounts.subtract(number);
//                    orderItem.setBuyCounts(subNumber);
//                    orderItem.setTotalAmount(subNumber.multiply(orderItem.getProductPrice()));
//                    orderItem.setCostAmount(subNumber.multiply(orderItem.getCostPrice()));
//                    orderItemService.update(orderItem);
//
//                    // 主订单金额修改
//                    orders.setActualAmount(orderItem.getTotalAmount());
//                    orders.setCostPriceTotal(orderItem.getCostPrice());
//                    orders.setTotalAmount(subNumber.multiply(orderItem.getOriginalPrice()));
//                    update(orders);
//
//                    // 关联计划数量更改
//                    orderSelectPlan.setCount(subNumber);
//                    orderSelectPlanService.update(orderSelectPlan);
//
//                } else {
//                    // 退货大于购买数据
//                    throw new BusinessException("退货数量大于购买数量");
//                }
//            } else if (count > 1) {
//                /**
//                 * 多明细全退
//                 * 多明细部分退
//                 */
//                if (buyCounts.compareTo(number) == 0) {
//                    // 多明细全退
//
//                    // 直接删除订单项
//                    BigDecimal subNumber = buyCounts.subtract(number);
//                    orderItemService.delete(orderItemId);
//
//                    // 主订单金额修改，统计剩余订单项金额
//                    List<OrderItem> orderItems = orderItemService.lambdaQuery()
//                            .eq(OrderItem::getOrderId, orderId)
//                            .list();
//                    // 实际总金额
//                    BigDecimal actualAmount = new BigDecimal(0);
//                    // 总金额
//                    BigDecimal totalAmount = new BigDecimal(0);
//                    // 总成本价
//                    BigDecimal costPriceTotal = new BigDecimal(0);
//                    for (OrderItem item : orderItems) {
//                        actualAmount = actualAmount.add(item.getTotalAmount());
//                        totalAmount = totalAmount.add(subNumber.multiply(item.getOriginalPrice()));
//                        costPriceTotal = costPriceTotal.add(item.getCostAmount());
//                    }
//                    orders.setActualAmount(actualAmount);
//                    orders.setCostPriceTotal(costPriceTotal);
//                    orders.setTotalAmount(totalAmount);
//                    update(orders);
//
//                    // 关联计划，因为全退直接删除
//                    orderSelectPlanService.delete(orderSelectPlan.getOrderSelectPlanId());
//
//                } else if (buyCounts.compareTo(number) == 1) {
//                    // 多明细部分退
//
//                    // 重新计算金额
//                    BigDecimal subNumber = buyCounts.subtract(number);
//                    orderItem.setBuyCounts(subNumber);
//                    orderItem.setTotalAmount(subNumber.multiply(orderItem.getProductPrice()));
//                    orderItem.setCostAmount(subNumber.multiply(orderItem.getCostPrice()));
//                    orderItemService.update(orderItem);
//
//                    // 主订单金额修改，查询所欲订单项目
//                    List<OrderItem> orderItems = orderItemService.lambdaQuery()
//                            .eq(OrderItem::getOrderId, orderId)
//                            .list();
//                    // 实际总金额
//                    BigDecimal actualAmount = new BigDecimal(0);
//                    // 总金额
//                    BigDecimal totalAmount = new BigDecimal(0);
//                    // 总成本价
//                    BigDecimal costPriceTotal = new BigDecimal(0);
//                    for (OrderItem item : orderItems) {
//                        actualAmount = actualAmount.add(item.getTotalAmount());
//                        totalAmount = totalAmount.add(subNumber.multiply(item.getOriginalPrice()));
//                        costPriceTotal = costPriceTotal.add(item.getCostAmount());
//                    }
//                    orders.setActualAmount(actualAmount);
//                    orders.setCostPriceTotal(costPriceTotal);
//                    orders.setTotalAmount(totalAmount);
//                    update(orders);
//
//                    // 关联计划数量更改
//                    orderSelectPlan.setCount(subNumber);
//                    orderSelectPlanService.update(orderSelectPlan);
//
//                } else {
//                    // 退货大于购买数据
//                    throw new BusinessException("退货数量大于购买数量");
//                }
//            } else {
//                throw new BusinessException("订单明细数据不存在！");
//            }
//        } else if (orderClass == 2) {
//            // 多供方订单
//
//            // 查询拆分的订单
//            Orders twoOrder = lambdaQuery().eq(Orders::getParentOrderId, orderId).one();
//
//            // 查询拆分的二级订单
//            OrderItem twoOrderItem = orderItemService.lambdaQuery().eq(OrderItem::getParentOrderItemId, orderItemId).one();
//
//            if (twoOrder == null || twoOrderItem == null) {
//                throw new BusinessException("未查询到拆分的订单信息！");
//            }
//
//            /**
//             * 1、单明细全退
//             * 2、单明细部分退
//             * 3、多明细全退
//             * 4、多明细部分退
//             */
//            if (count == 1) {
//                if (buyCounts.compareTo(number) == 0) {
//                    // 单明细全退
//
//                    // 全部退修改状态即可
//                    orders.setState(7);
//                    orders.setCloseType(5);
//                    update(orders);
//
//                    // TODO 先删除，后续不删除再讨论
//                    delete(orderId);
//                    orderItemService.delete(orderItemId);
//                    // 修改拆分订单
//                    twoOrder.setState(7);
//                    twoOrder.setCloseType(5);
//                    update(twoOrder);
//
//                    // TODO 先删除，后续不删除再讨论
//                    delete(twoOrder.getOrderId());
//                    orderItemService.delete(twoOrderItem.getParentOrderItemId());
//                    // 删除关联的计划
//                    orderSelectPlanService.delete(orderSelectPlan.getOrderSelectPlanId());
//
//                } else if (buyCounts.compareTo(number) == 1) {
//                    // 单明细部分退
//
//                    // 重新计算金额
//                    BigDecimal subNumber = buyCounts.subtract(number);
//                    orderItem.setBuyCounts(subNumber);
//                    orderItem.setTotalAmount(subNumber.multiply(orderItem.getProductPrice()));
//                    orderItem.setCostAmount(subNumber.multiply(orderItem.getCostPrice()));
//                    orderItemService.update(orderItem);
//
//                    // 重新计算拆分订单
//                    twoOrderItem.setBuyCounts(subNumber);
//                    twoOrderItem.setTotalAmount(subNumber.multiply(twoOrderItem.getProductPrice()));
//                    orderItemService.update(twoOrderItem);
//
//                    // 主订单金额修改
//                    orders.setActualAmount(orderItem.getTotalAmount());
//                    orders.setCostPriceTotal(orderItem.getCostPrice());
//                    orders.setTotalAmount(subNumber.multiply(orderItem.getOriginalPrice()));
//                    update(orders);
//
//                    // 拆分主订单修改
//                    twoOrder.setActualAmount(twoOrderItem.getTotalAmount());
//                    update(twoOrder);
//
//                    // 关联计划数量更改
//                    orderSelectPlan.setCount(subNumber);
//                    orderSelectPlanService.update(orderSelectPlan);
//
//                } else {
//                    // 退货大于购买数据
//                    throw new BusinessException("退货数量大于购买数量");
//                }
//            } else if (count > 1) {
//                /**
//                 * 多明细全退
//                 * 多明细部分退
//                 */
//                if (buyCounts.compareTo(number) == 0) {
//                    // 多明细全退，注意，判断二级订单是否多级
//
//                    // 直接删除订单项
//                    BigDecimal subNumber = buyCounts.subtract(number);
//                    orderItemService.delete(orderItemId);
//                    orderItemService.delete(twoOrderItem.getOrderItemId());
//
//
//                    // 主订单金额修改，统计剩余订单项金额
//                    List<OrderItem> orderItems = orderItemService.lambdaQuery()
//                            .eq(OrderItem::getOrderId, orderId)
//                            .list();
//                    // 实际总金额
//                    BigDecimal actualAmount = new BigDecimal(0);
//                    // 总金额
//                    BigDecimal totalAmount = new BigDecimal(0);
//                    // 总成本价
//                    BigDecimal costPriceTotal = new BigDecimal(0);
//                    for (OrderItem item : orderItems) {
//                        actualAmount = actualAmount.add(item.getTotalAmount());
//                        totalAmount = totalAmount.add(subNumber.multiply(item.getOriginalPrice()));
//                        costPriceTotal = costPriceTotal.add(item.getCostAmount());
//                    }
//                    orders.setActualAmount(actualAmount);
//                    orders.setCostPriceTotal(costPriceTotal);
//                    orders.setTotalAmount(totalAmount);
//                    update(orders);
//
//
//                    List<OrderItem> twoOrderItems = orderItemService.lambdaQuery()
//                            .eq(OrderItem::getOrderId, twoOrder.getOrderId())
//                            .list();
//                    if (CollectionUtils.isEmpty(twoOrderItems)) {
//                        // 如果二级订单没有数据，直接删除主订单
//                        delete(twoOrder.getOrderId());
//                    } else {
//                        BigDecimal actualAmount2 = new BigDecimal(0);
//                        for (OrderItem item : twoOrderItems) {
//                            actualAmount2 = actualAmount2.add(item.getTotalAmount());
//                        }
//                        twoOrder.setActualAmount(actualAmount2);
//                        update(twoOrder);
//                    }
//                    // 关联计划，因为全退直接删除
//                    orderSelectPlanService.delete(orderSelectPlan.getOrderSelectPlanId());
//                } else if (buyCounts.compareTo(number) == 1) {
//                    // 多明细部分退
//
//                    BigDecimal subNumber = buyCounts.subtract(number);
//
//                    // 重新计算金额
//                    orderItem.setBuyCounts(subNumber);
//                    orderItem.setTotalAmount(subNumber.multiply(orderItem.getProductPrice()));
//                    orderItem.setCostAmount(subNumber.multiply(orderItem.getCostPrice()));
//                    orderItemService.update(orderItem);
//
//                    // 重新计算拆分订单
//                    twoOrderItem.setBuyCounts(subNumber);
//                    twoOrderItem.setTotalAmount(subNumber.multiply(twoOrderItem.getProductPrice()));
//                    orderItemService.update(twoOrderItem);
//
//
//                    // 主订单金额修改，查询所欲订单项目
//                    List<OrderItem> orderItems = orderItemService.lambdaQuery()
//                            .eq(OrderItem::getOrderId, orderId)
//                            .list();
//                    // 实际总金额
//                    BigDecimal actualAmount = new BigDecimal(0);
//                    // 总金额
//                    BigDecimal totalAmount = new BigDecimal(0);
//                    // 总成本价
//                    BigDecimal costPriceTotal = new BigDecimal(0);
//                    for (OrderItem item : orderItems) {
//                        actualAmount = actualAmount.add(item.getTotalAmount());
//                        totalAmount = totalAmount.add(subNumber.multiply(item.getOriginalPrice()));
//                        costPriceTotal = costPriceTotal.add(item.getCostAmount());
//                    }
//                    orders.setActualAmount(actualAmount);
//                    orders.setCostPriceTotal(costPriceTotal);
//                    orders.setTotalAmount(totalAmount);
//                    update(orders);
//
//                    // 重新统计拆分订单数据
//                    List<OrderItem> twoOrderItems = orderItemService.lambdaQuery()
//                            .eq(OrderItem::getOrderId, twoOrder.getOrderId())
//                            .list();
//                    BigDecimal actualAmount2 = new BigDecimal(0);
//                    for (OrderItem item : twoOrderItems) {
//                        actualAmount2 = actualAmount2.add(item.getTotalAmount());
//                    }
//                    twoOrder.setActualAmount(actualAmount2);
//                    update(twoOrder);
//
//
//                    // 关联计划数量更改
//                    orderSelectPlan.setCount(subNumber);
//                    orderSelectPlanService.update(orderSelectPlan);
//
//                } else {
//                    // 退货大于购买数据
//                    throw new BusinessException("退货数量大于购买数量");
//                }
//            } else {
//                throw new BusinessException("订单明细数据不存在！");
//            }
//        } else {
//            throw new BusinessException("订单类型错误！");
//        }
//        // 回写数量
//        updatePlanNum(retPlanList);
    }

    private void updatePlanNum(List<Map<String, Object>> retPlanList) {

        // 调用反写接口
        HashMap<Object, Object> subPlanDDTO = new HashMap<>();
        subPlanDDTO.put("jsonrpc", "2.0");
        subPlanDDTO.put("method", "Material.SporadicPlan.UpdateShopDtl");
        subPlanDDTO.put("id", 1);
        HashMap<String, String> tags = new HashMap<>();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        tags.put("userid", user.getFarUserId());
        tags.put("username", user.getOriginalUserName());
        tags.put("orgid", user.getOrgId());
        tags.put("orgname", user.getEnterpriseName());
        tags.put("companycode", "1000");
        tags.put("auth_client_id", "test");
        tags.put("auth_token", "test");
        tags.put("platformid", "1");
        subPlanDDTO.put("tags", tags);
        HashMap<Object, Object> params = new HashMap<>();
        params.put("list", retPlanList);
        subPlanDDTO.put("params", params);
        String content = JSON.toJSONString(subPlanDDTO);
        log.warn("反写计划接口请求参数：" + content);
        // 发送请求
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<String> request = new HttpEntity<>(content, headers);
        String url = mallConfig.pcwp1PlanUrl + "/json.rpc";
        Map rMap = null;
        try {
            // 中文乱码，主要是 StringHttpMessageConverter的默认编码为ISO导致的
            List<HttpMessageConverter<?>> templateMessageConverters = restTemplate.getMessageConverters();
            for (HttpMessageConverter converter : templateMessageConverters) {
                if (converter instanceof StringHttpMessageConverter) {
                    ((StringHttpMessageConverter) converter).setDefaultCharset(Charset.forName("UTF-8"));
                    break;
                }
            }
            rMap = restTemplate.postForObject(url, request, Map.class);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException("远程反写计划服务异常！");
        }
        Map error = (Map) rMap.get("error");
        if (error != null) {
            log.error("推送计划报错：" + error);
            throw new BusinessException("远程反写计划接口错误！");
        }
        String result = (String) rMap.get("result");
        if (result != null && result.equals("操作成功")) {

        } else {
            log.error("反写计划接口错误！返回：" + rMap);
            throw new BusinessException("远程反写计划服务异常");
        }
    }

    private void PCWP2updatePlanNum(List<Map<String, Object>> retPlanList) {

        // 调用反写接口
        HashMap<Object, Object> subPlanDDTO = new HashMap<>();
        ArrayList<HashMap<String, Object>> hashMaps = new ArrayList<>();
        subPlanDDTO.put("method", "Material.SporadicPlan.UpdateShopDtl");
        subPlanDDTO.put("id", 1);
        HashMap<String, String> tags = new HashMap<>();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        tags.put("userid", user.getFarUserId());
        tags.put("username", user.getOriginalUserName());
        tags.put("orgid", user.getOrgId());
        tags.put("orgname", user.getEnterpriseName());
        tags.put("companycode", "1000");
        tags.put("auth_client_id", "test");
        tags.put("auth_token", "test");
        tags.put("platformid", "1");
        subPlanDDTO.put("tags", tags);
        HashMap<Object, Object> params = new HashMap<>();
        params.put("list", retPlanList);
        subPlanDDTO.put("params", params);
        String content = JSON.toJSONString(subPlanDDTO);
        log.warn("反写计划接口请求参数：" + content);
        // 发送请求
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<String> request = new HttpEntity<>(content, headers);
        String url = mallConfig.pcwp1PlanUrl + "/json.rpc";
        Map rMap = null;
        try {
            // 中文乱码，主要是 StringHttpMessageConverter的默认编码为ISO导致的
            List<HttpMessageConverter<?>> templateMessageConverters = restTemplate.getMessageConverters();
            for (HttpMessageConverter converter : templateMessageConverters) {
                if (converter instanceof StringHttpMessageConverter) {
                    ((StringHttpMessageConverter) converter).setDefaultCharset(Charset.forName("UTF-8"));
                    break;
                }
            }
            rMap = restTemplate.postForObject(url, request, Map.class);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException("远程反写计划服务异常！");
        }
        Map error = (Map) rMap.get("error");
        if (error != null) {
            log.error("推送计划报错：" + error);
            throw new BusinessException("远程反写计划接口错误！");
        }
        String result = (String) rMap.get("result");
        if (result != null && result.equals("操作成功")) {

        } else {
            log.error("反写计划接口错误！返回：" + rMap);
            throw new BusinessException("远程反写计划服务异常");
        }

    }

    private void closeTwoOrder(String orderId, boolean flag) {
        Orders byId = getById(orderId);
        List<Orders> twoOrders = findALLTwoOrderList(orderId);
        for (Orders twoOrder : twoOrders) {
            if (twoOrder.getState() != 10) {
                flag = false;
                break;
            }
        }
        if (flag) {
            byId.setState(10);
            byId.setSuccessDate(new Date());
            update(byId);
        }
    }

    private List<Orders> findALLTwoOrderList(String orderId) {
        LambdaQueryWrapper<Orders> q = new LambdaQueryWrapper<>();
        q.eq(Orders::getParentOrderId, orderId);
        List<Orders> list = list(q);
        return list;
    }


    /**
     * 生成大宗订单
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitMonthPlanOrder(SubmitMonthPlanOrderDTO dto) {
        Integer isInterior = ThreadLocalUtil.getCurrentUser().getIsInterior();
        if (isInterior == 1) {
            Integer isSubmitOrder = ThreadLocalUtil.getCurrentUser().getIsSubmitOrder();
            if (isSubmitOrder == null || isSubmitOrder == 0) {
                throw new BusinessException("没有下单权限！");
            }
        }
        List<SubmitMonthPlanOrderItemDTO> dtos = dto.getDtos();
        // 判断是不是多供方订单
        String untitled = "";
        for (SubmitMonthPlanOrderItemDTO sdto : dtos) {
            // 校验下单数量不能超过本期数量
            BigDecimal selectQty = sdto.getSelectQty();
            MaterialMonthSupplyPlanDtl planDtlInfo = materialMonthSupplyPlanDtlService.getById(sdto.getPlanDtlId());
            if (mallConfig.isCountPlanOrderNum == 1) {
                // TODO 计划最新统计
                BigDecimal qty = getOrderUseCountBySelectPlanDtlIds(sdto.getPlanDtlId());
                BigDecimal subNum = planDtlInfo.getThisPlanQty().subtract(qty);
                // 如果生成订单的数量大于剩余数量直接保错
                if (selectQty.compareTo(subNum) == 1) {
                    throw new BusinessException("物资名称：【" + sdto.getMaterialName() + "】所生成订单的数量超过剩余可生成数量！");
                }
            } else {
                if (planDtlInfo == null) {
                    throw new BusinessException("计划明细不存在！");
                }
                BigDecimal subNum = planDtlInfo.getThisPlanQty().subtract(planDtlInfo.getOrderQty());
                // 如果生成订单的数量大于剩余数量直接保错
                if (selectQty.compareTo(subNum) == 1) {
                    throw new BusinessException("物资名称：【" + sdto.getMaterialName() + "】所生成订单的数量超过剩余可生成数量！");
                }
            }

            untitled = untitled + sdto.getMaterialName() + ",";
        }
        SubmitMonthPlanOrderItemDTO dtox = dtos.get(0);
        MaterialMonthSupplyPlan planInfo = materialMonthSupplyPlanService.getById(dtox.getPlanId());
        if (planInfo == null) {
            throw new BusinessException("计划不存在！");
        } else {
            Integer state = planInfo.getState();
            if (state != 2) {
                throw new BusinessException("该计划不是已审核状态不能生成订单！");
            }
        }


        Orders order = new Orders();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        order.setEnterpriseId(user.getEnterpriseId());
        order.setEnterpriseName(user.getEnterpriseName());
        order.setReceiverName(dtox.getReceiverName());
        order.setPayWay(dtox.getPayWay());
        order.setReceiverMobile(dtox.getReceiverMobile());
        order.setReceiverAddress(dtox.getReceiverAddress());
        order.setOrderRemark(dtox.getOrderRemark());

        // 生成订单号
        order.setOrderSn(OrderUtils.getOrder());
        String userId = user.getUserId();
        order.setUserId(userId);
        order.setOrgId(user.getOrgId());
        order.setUntitled(untitled.substring(0, untitled.length() - 1));
        // 金额
        order.setFlishTime(new Date());
        order.setState(OrderEnum.STATE_FINISH.getCode());
        order.setOrderFreight(new BigDecimal("0"));
        order.setProductType(12);
        // 发票状态
        order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());

        // 设置利率，计算金额
        EnterpriseInfo en = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseName, planInfo.getSupplierName())
                .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate, EnterpriseInfo::getEnterpriseId).one();
        if (en == null) {
            throw new BusinessException("供应商不存在！");
        }
        int shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, en.getEnterpriseId()).eq(Shop::getAuditStatus, 1).count();
        if (shop == 0) {
            throw new BusinessException("供应商未开店或供应商店铺未审核！");
        }
        BigDecimal taxRate = en.getTaxRate();
        if (taxRate == null) {
            throw new BusinessException("【" + planInfo.getSupplierName() + "】供应商未设置利率！");
        }
        order.setTaxRate(taxRate);
        order.setOrderClass(1);
        order.setOrderSourceType(3);
        if (mallConfig.isShowCode == 1) {
            // 如果是物资分公司生成多供方订单
            if (planInfo.getSupplierName().equals(mallConfig.selfOrgName) || planInfo.getSupplierName().equals("四川路桥建设集团物资有限责任公司")) {
                order.setOrderClass(2);
            }
        }
        order.setOrderSourceId(dtos.get(0).getContractId());
        order.setSupplierId(planInfo.getLocalSupplierId());
        order.setSupplierName(planInfo.getSupplierName());
        boolean save = this.save(order);
        if (!save) {
            throw new BusinessException("订单保存失败！");
        }
        // 处理拆单
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        for (SubmitMonthPlanOrderItemDTO iDTO : dtos) {
            MaterialMonthSupplyPlanDtl planDtlInfo = materialMonthSupplyPlanDtlService.getById(iDTO.getPlanDtlId());
            // 订单项
            OrderItem orderItem = new OrderItem();
            orderItem.setClassPathId(iDTO.getClassPathId());
            orderItem.setClassPathName(iDTO.getClassPathName());
            orderItem.setProductId(iDTO.getMaterialId());
            orderItem.setProductName(iDTO.getMaterialName().trim());
            if (iDTO.getSpec() != null) {
                orderItem.setSkuName(iDTO.getSpec().trim());
            }
            orderItem.setUnit(iDTO.getUnit());
            // 如果是多供方
            if (order.getOrderClass() == 2) {
                if (StringUtils.isBlank(planDtlInfo.getTwoSupplierId())) {
                    orderItem.setState(2);
                } else {
                    orderItem.setSupplierId(planDtlInfo.getTwoSupplierId());
                    orderItem.setState(1);
                }
            } else {
                orderItem.setSupplierId(order.getSupplierId());
            }
            orderItem.setOrderId(order.getOrderId());
            orderItem.setOrderSn(order.getOrderSn());
            orderItem.setBuyCounts(iDTO.getSelectQty());
            orderItem.setProductType(12);
            orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
            orderItem.setTexture(iDTO.getTexture() == null ? null : iDTO.getTexture().trim());
            orderItem.setRelevanceName(iDTO.getMaterialName().trim());
            orderItem.setRelevanceId(iDTO.getMaterialId());
            if (StringUtils.isNotEmpty(iDTO.getClassPathId())) {
                orderItem.setClassId(iDTO.getClassPathId().split("/")[0]);
            }
            orderItemService.save(orderItem);
            // 拆单所需数据
            if (orderItem.getState() != null && orderItem.getState() == 1) {
                orderItems.add(orderItem);
            }

            // 保存计划订单
            OrderSelectPlan orderSelectPlan = new OrderSelectPlan();
            orderSelectPlan.setOrderId(order.getOrderId());
            orderSelectPlan.setOrderSn(order.getOrderSn());
            orderSelectPlan.setOrderItemId(orderItem.getOrderItemId());
            orderSelectPlan.setDtlId(planDtlInfo.getPlanDtlId());
            orderSelectPlan.setSourcePlanQty(planDtlInfo.getThisPlanQty());
            orderSelectPlan.setSourceContractQty(planDtlInfo.getSourceQty());
            orderSelectPlan.setEquipmentName(planDtlInfo.getMaterialName());
            orderSelectPlan.setCount(iDTO.getSelectQty());
            orderSelectPlan.setBillId(planInfo.getPlanId());
            orderSelectPlan.setBillNo(planInfo.getPlanNo());
            orderSelectPlan.setProductType(12);
            orderSelectPlan.setOrgId(ThreadLocalUtil.getCurrentUser().getOrgId());
            orderSelectPlan.setOrgName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
            orderSelectPlan.setStorageId(planInfo.getSupplierId());
            orderSelectPlan.setStorageName(planInfo.getSupplierName());
            orderSelectPlan.setContractId(iDTO.getContractId());
            orderSelectPlan.setContractNo(iDTO.getContractNo());
            EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                    .eq(EnterpriseInfo::getEnterpriseName, planInfo.getSupplierName())
                    .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getSocialCreditCode, EnterpriseInfo::getShortCode, EnterpriseInfo::getInteriorId)
                    .one();
            if (enterpriseInfo != null) {
                if (StringUtils.isEmpty(enterpriseInfo.getSocialCreditCode())) {
                    orderSelectPlan.setSupplierType(2);
                    orderSelectPlan.setShortCode(enterpriseInfo.getShortCode());
                    orderSelectPlan.setStorageOrgId(enterpriseInfo.getInteriorId());
                } else {
                    orderSelectPlan.setCreditCode(enterpriseInfo.getSocialCreditCode());
                    orderSelectPlan.setSupplierType(1);
                }
            } else {
                throw new BusinessException("未找到供应商信息！");
            }

            orderSelectPlan.setPlanType(2);
            orderSelectPlan.setTaxRate(order.getTaxRate());
            orderSelectPlanService.save(orderSelectPlan);

            // 修改计划已下单数量
            BigDecimal orderQty = planDtlInfo.getOrderQty(); // 没啥用
            planDtlInfo.setOrderQty(orderQty.add(iDTO.getSelectQty()));
            materialMonthSupplyPlanDtlService.update(planDtlInfo);
        }


        if (order.getOrderClass() == 2) {
            if (CollectionUtils.isEmpty(orderItems)) {
                return;
            }
            // 生成订单号
            Map<String, List<OrderItem>> collectOrderItems = orderItems.stream()
                    .collect(Collectors.groupingBy(OrderItem::getSupplierId));
            collectOrderItems.forEach((supplierId, newOrderItems) -> {
                // 保存订单、计算金额
                Orders orders = new Orders();
                BeanUtils.copyProperties(order, orders);
                orders.setOrderId(null);
                orders.setUntitled(null);
                orders.setOrderSn(null);

                EnterpriseInfo enterpriseInfoOne = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, supplierId)
                        .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate).one();
                if (enterpriseInfoOne != null) {
                    orders.setSupplierId(supplierId);
                    orders.setSupplierName(enterpriseInfoOne.getEnterpriseName());
                } else {
                    throw new BusinessException("供应商不存在！");
                }
                BigDecimal taxRate2 = enterpriseInfoOne.getTaxRate();
                if (taxRate2 == null || taxRate2.compareTo(BigDecimal.ZERO) == 0) {
                    throw new BusinessException("【" + enterpriseInfoOne.getEnterpriseName() + "】供应商未设置利率！");
                }
                String untitled2 = "";
                for (OrderItem newOrderItem : newOrderItems) {
                    untitled2 = untitled2 + newOrderItem.getProductName() + ",";
                }
                orders.setUntitled(untitled2.substring(0, untitled2.length() - 1));
                orders.setActualAmount(BigDecimal.ZERO);
                orders.setTotalAmount(BigDecimal.ZERO);
                orders.setCostPriceTotal(BigDecimal.ZERO);
                orders.setOrderClass(3);
                orders.setParentOrderId(order.getOrderId());
                orders.setOrderSourceType(6);
                orders.setOrderSourceId(orders.getParentOrderId());
                orders.setTaxRate(taxRate2);
                orders.setNoRateAmount(BigDecimal.ZERO);
                orders.setOrderSn(OrderUtils.getOrder());
                save(orders);
                // 保存好了订单项，
                for (OrderItem newOrderItem : newOrderItems) {

                    newOrderItem.setOrderId(orders.getOrderId());
                    newOrderItem.setOrderSn(orders.getOrderSn());
                    newOrderItem.setProductPrice(BigDecimal.ZERO);
                    newOrderItem.setTotalAmount(BigDecimal.ZERO);
                    newOrderItem.setTaxRate(taxRate2);
                    newOrderItem.setNoRatePrice(BigDecimal.ZERO);
                    newOrderItem.setNoRateAmount(BigDecimal.ZERO);
                    newOrderItem.setCostPrice(BigDecimal.ZERO);
                    newOrderItem.setCostAmount(BigDecimal.ZERO);
                    newOrderItem.setOriginalPrice(BigDecimal.ZERO);
                    // 保存父明细id
                    newOrderItem.setParentOrderItemId(newOrderItem.getOrderItemId());
                    newOrderItem.setOrderItemId(null);
                }
                orderItemService.saveBatch(newOrderItems);
            });
        }


    }


    @Override
    public List<Orders> getTwoOrderByOrderId(String parentOrderId) {
        LambdaQueryWrapper<Orders> eq = new LambdaQueryWrapper<Orders>().eq(Orders::getParentOrderId, parentOrderId);
        List<Orders> list = list(eq);
        return list;
    }

    /**
     * 根据主订单项id生成子订单
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreateTwoOrder(BatchCreateTwoOrderDTO dto) {
        List<String> orderItemId = dto.getOrderItemId();
        String supplierId = dto.getSupplierId();
        String supplierName = dto.getSupplierName();
        List<OrderItem> orderItems = orderItemService.lambdaQuery().in(OrderItem::getOrderItemId, orderItemId).list();
        if (CollectionUtils.isEmpty(orderItems)) {
            throw new BusinessException("订单不存在！");
        }
        for (OrderItem orderItem : orderItems) {
            if (orderItem.getBuyCounts().compareTo(orderItem.getReturnCounts()) == 0) {
                throw new BusinessException("物资【" + orderItem.getProductName() + "】已全部完成退货不能生成二级订单！");
            }
        }
        String orderId = orderItems.get(0).getOrderId();
        Orders orders = getById(orderId);
        // 保存订单、计算金额
        Orders newOrders = new Orders();
        BeanUtils.copyProperties(orders, newOrders);
        newOrders.setOrderId(null);
        newOrders.setUntitled(null);
        newOrders.setOrderSn(null);
        // 总成本价
        BigDecimal costPriceTotal2 = new BigDecimal(0);
        EnterpriseInfo en = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, supplierId)
                .select(EnterpriseInfo::getTaxRate, EnterpriseInfo::getEnterpriseId).one();
        if (en == null) {
            throw new BusinessException("供应商不存在！");
        }
        BigDecimal taxRate = en.getTaxRate();
        if (taxRate == null) {
            throw new BusinessException("供应商未设置税率！");
        }
        BigDecimal noRatecostPriceTotal2 = new BigDecimal(0);
        String untitled2 = "";
        for (OrderItem newOrderItem : orderItems) {
            costPriceTotal2 = costPriceTotal2.add(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
//            BigDecimal noTatePrice = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate);
//            noRatecostPriceTotal2 = noRatecostPriceTotal2.add(noTatePrice.multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));

            BigDecimal noRateAmount = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP), taxRate);
            noRatecostPriceTotal2 = noRatecostPriceTotal2.add(noRateAmount);
            untitled2 = untitled2 + newOrderItem.getProductName() + ",";
        }
        newOrders.setUntitled(untitled2.substring(0, untitled2.length() - 1));
        // 理论上，在主订单包含商品多供方的情况下，如果将主订单拆分成多个子订单，那么每个子订单的销售价应
        // 该等于对应供应商提供商品的成本价，这与主订单的成本价是相同的。

        newOrders.setActualAmount(costPriceTotal2);
        if (mallConfig.isNotRateAmount == 1) {
            newOrders.setNoRateAmount(noRatecostPriceTotal2);
        } else {
            newOrders.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(costPriceTotal2, taxRate));
        }
        newOrders.setTotalAmount(BigDecimal.ZERO);
        newOrders.setCostPriceTotal(BigDecimal.ZERO);
        newOrders.setOrderClass(3);
        newOrders.setParentOrderId(orders.getOrderId());
        newOrders.setOrderSourceType(1);
        newOrders.setOrderSourceId(orders.getParentOrderId());
        newOrders.setSupplierId(supplierId);
        newOrders.setSupplierName(supplierName);
        newOrders.setOrderSn(OrderUtils.getOrder());
        save(newOrders);
        // 保存好了订单项，
        for (OrderItem newOrderItem : orderItems) {
            newOrderItem.setOrderId(newOrders.getOrderId());
            newOrderItem.setOrderSn(newOrders.getOrderSn());
            newOrderItem.setProductPrice(newOrderItem.getCostPrice());
            newOrderItem.setNoRatePrice(TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate));
            newOrderItem.setTaxRate(taxRate);
            BigDecimal amount = newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP);
            newOrderItem.setTotalAmount(amount);

            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate);
//            newOrderItem.setNoRateAmount(noRatePrice.multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
            newOrderItem.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(amount, taxRate));

            newOrderItem.setCostPrice(BigDecimal.ZERO);
            newOrderItem.setCostAmount(BigDecimal.ZERO);
            newOrderItem.setOriginalPrice(BigDecimal.ZERO);
            // 保存父明细id
            newOrderItem.setParentOrderItemId(newOrderItem.getOrderItemId());
            newOrderItem.setOrderItemId(null);
        }
        orderItemService.saveBatch(orderItems);

        // 修改主订单明细状态
        orderItemService.lambdaUpdate().in(OrderItem::getOrderItemId, orderItemId)
                .set(OrderItem::getState, 1)
                .set(OrderItem::getGmtModified, new Date()).update();


    }

    /**
     * 根据订单明细id修改为待竞价状态
     *
     * @param orderItemIds
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateToBidingState(List<String> orderItemIds) {
        // 检验是自营店
        String shopId = ThreadLocalUtil.getCurrentUser().getShopId();
        Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, shopId)
                .select(Shop::getIsBusiness).one();
        if (shop == null) {
            throw new BusinessException("未找到店铺！");
        }
        if (shop.getIsBusiness() != 1) {
            throw new BusinessException("店铺不是自营店不能设置为待竞价！");
        }
        // 校验是待发货
        List<OrderItem> list = orderItemService.lambdaQuery().in(OrderItem::getOrderItemId, orderItemIds).list();
        if (!CollectionUtils.isEmpty(list)) {
            String orderId = list.get(0).getOrderId();
            Orders orders = lambdaQuery().eq(Orders::getOrderId, orderId).select(Orders::getState).one();
            if (orders.getState() != OrderEnum.STATE_FINISH.getCode()) {
                // 不是待发货
                throw new BusinessException("订单只能是发货状态才能修改！");
            }
            for (OrderItem orderItem : list) {
                Integer state = orderItem.getState();
                if (state == 2 || state == 3) {
                    orderItem.setState(3);
                    orderItemService.update(orderItem);
                }
            }
        }
    }

    /**
     * 根据订单生成竞价
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void
    createBidingByOrder(CreateBidingByOrderDTO dto) {
        String biddingExplain = dto.getBiddingExplain();
        Integer type = dto.getType();
        if (biddingExplain != null) {
            if (biddingExplain.length() > 2500) {
                throw new BusinessException("竞价说明内容过大！");
            }
        }
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        // 检验是自营店
        String shopId = user.getShopId();
        Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, shopId)
                .select(Shop::getIsBusiness).one();
        if (shop == null) {
            throw new BusinessException("未找到店铺！");
        }
        if (shop.getIsBusiness() != 1) {
            throw new BusinessException("店铺不是自营店不能生成竞价！");
        }
        BiddingPurchase bid = new BiddingPurchase();
        BeanUtils.copyProperties(dto, bid);
        List<OrderItem> orderItems = dto.getOrderItems();
        String biddingPurchaseNo = "";
        if (orderItems != null && orderItems.size() > 0) {
            OrderItem orderItem = orderItems.get(0);
            String sn = CodeGenerator.generateBidCode(orderItem.getProductType());
            //Integer count = biddingPurchaseService.lambdaQuery().like(BiddingPurchase::getBiddingSn, sn).count();
            // TODO 找到最大编号
            //String sn= CodeGenerator.generateBidCode(13);
            //Integer count = biddingPurchaseService.lambdaQuery().like(BiddingPurchase::getBiddingSn, sn).count();
            // TODO 找到最大编号
            List<BiddingPurchase> bids = biddingPurchaseService.lambdaQuery().like(BiddingPurchase::getBiddingSn, sn).list();
            int count = 0;
            //List<String> bidSnList = bids.stream().map(BiddingPurchase::getBiddingSn).collect(Collectors.toList());
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(bids)) {
                Collections.sort(bids);
                BiddingPurchase biddingPurchase = bids.get(0);
                count = biddingPurchase.currentCount();
            }
            biddingPurchaseNo = CodeGenerator.generateBidCodeNum(sn, count);

        } else {
            throw new BusinessException("订单商品不能为空！");
        }
        bid.setBiddingSn(biddingPurchaseNo);
        bid.setBiddingSourceType(1);

        bid.setType(type);
        bid.setState(0);
        bid.setShopId(user.getShopId());
        bid.setBiddingState(1);
        bid.setPublicityState(0);
        bid.setCreateOrgId(user.getEnterpriseId());
        bid.setCreateOrgName(user.getEnterpriseName());
        biddingPurchaseService.save(bid);
        List<OrderItem> list = dto.getOrderItems();
        if (!CollectionUtils.isEmpty(list)) {
            String orderId = list.get(0).getOrderId();
            Orders orders = lambdaQuery().eq(Orders::getOrderId, orderId).select(Orders::getState).one();
            for (OrderItem orderItem : list) {
                BiddingProduct bidP = new BiddingProduct();
                BeanUtils.copyProperties(orderItem, bidP);
                bidP.setBiddingId(bid.getBiddingId());
                bidP.setBiddingSn(bid.getBiddingSn());
                bidP.setBrand(orderItem.getBrandName());
                bidP.setClassId(orderItem.getClassId());
                bidP.setSpec(orderItem.getSkuName());
                bidP.setNum(orderItem.getBuyCounts());
                bidP.setCreateOrgId(user.getEnterpriseId());
                bidP.setCreateOrgName(user.getEnterpriseName());
                bidP.setProductTexture(orderItem.getTexture());
                bidP.setReferencePrice(orderItem.getMaxPrice());
                bidP.setState(null);
                biddingProductService.save(bidP);
            }
            List<String> collect = list.stream().map(t -> t.getOrderItemId()).collect(Collectors.toList());
            // 修改主订单明细状态
            orderItemService.lambdaUpdate().in(OrderItem::getOrderItemId, collect)
                    .set(OrderItem::getState, 4)
                    .set(OrderItem::getGmtModified, new Date()).update();
        }
        if (type == 2) {
            //  邀请竞价
            List<BiddingInvitationRelevance> suppliers = dto.getSuppliers();
            for (BiddingInvitationRelevance supplier : suppliers) {
                supplier.setBiddingSn(biddingPurchaseNo);
            }
            boolean b = biddingInvitationRelevanceService.saveBatch(suppliers);
            if (!b) {
                throw new BusinessException("邀请供应商异常");
            }

        }
    }

    /**
     * 根据订单明细id修改为未分配状态
     *
     * @param orderItemIds
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateNotUseState(List<String> orderItemIds) {
        // 检验是自营店
        String shopId = ThreadLocalUtil.getCurrentUser().getShopId();
        Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, shopId)
                .select(Shop::getIsBusiness).one();
        if (shop == null) {
            throw new BusinessException("未找到店铺！");
        }
        if (shop.getIsBusiness() != 1) {
            throw new BusinessException("店铺不是自营店不能取消待竞价！");
        }
        // 校验是待发货
        List<OrderItem> list = orderItemService.lambdaQuery().in(OrderItem::getOrderItemId, orderItemIds).list();
        if (!CollectionUtils.isEmpty(list)) {
            String orderId = list.get(0).getOrderId();
            Orders orders = lambdaQuery().eq(Orders::getOrderId, orderId).select(Orders::getState).one();
            if (orders.getState() != OrderEnum.STATE_FINISH.getCode()) {
                // 不是待发货
                throw new BusinessException("订单只能是发货状态才能修改！");
            }
            for (OrderItem orderItem : list) {
                Integer state = orderItem.getState();
                if (state == 3) {
                    orderItem.setState(2);
                    orderItemService.update(orderItem);
                }
            }
        }
    }


    @Override
    public Orders getDataByOrderSn(String orderSn) {
        Orders one = lambdaQuery().eq(Orders::getOrderSn, orderSn).one();
        EnterpriseInfo byId = enterpriseInfoService.getById(one.getEnterpriseId());
        one.setEnterpriseName(byId.getEnterpriseName());
        if (!StringUtils.isNotBlank(one.getSupplierName())) {
            String name = shopService.getEnterpriseInfoNameByShopId(one.getShopId());
            one.setSupplierName(name);
        }

        String enterpriseName = enterpriseInfoService.lambdaQuery().select(EnterpriseInfo::getEnterpriseName).eq(EnterpriseInfo::getEnterpriseId, one.getEnterpriseId()).one().getEnterpriseName();
        one.setEnterpriseName(enterpriseName);
        String orderId = one.getOrderId();
        if (StringUtils.isNotBlank(one.getParentOrderId())) {
            orderId = one.getParentOrderId();
        }
        // 同一订单下的商品账期一致
        List<OrderItem> orderItems = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, orderId).list();
        if (!orderItems.isEmpty()) {
            one.setPaymentPeriod(orderItems.get(0).getPaymentPeriod());
        }


        List<OrderSelectPlan> list = orderSelectPlanService.lambdaQuery().eq(OrderSelectPlan::getOrderId, orderId).list();
        if (!CollectionUtils.isEmpty(list)) {
            OrderSelectPlan orderSelectPlan = list.get(0);
            if (orderSelectPlan.getProductType() == 10) {
                one.setBillNo(orderSelectPlan.getBillNo());
            }
            if (orderSelectPlan.getProductType() == 12) {
                one.setBillNo(orderSelectPlan.getContractNo());
            }
            if (orderSelectPlan.getProductType() == 13) {
                one.setBillNo(orderSelectPlan.getBillNo());
            }
        }
        return one;
    }


    @Override
    public PageUtils selectOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> lambdaQuery) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        LambdaQueryWrapper<Orders> eq = null;
        if (mallConfig.profilesActive.equals("dev")) {
            // 履约平台控制数据权限
            // 机构数据查看权限（1本机及子级2只看本级3指定）
            Integer dataSelect = (Integer) jsonObject.get("dataSelect");
            List dataScopes = (List) jsonObject.get("dataScope");
            String roleName = RoleEnum.ROLE_5.getName();
            List<MallRole> mallRoles = user.getMallRoles();
            if (CollectionUtils.isEmpty(mallRoles) || mallRoles.stream().noneMatch(t -> t.getName().equals(roleName))) {
                throw new BusinessException(500, "当前用户无物资采购平台履约系统权限！");
            }
            MallRole role = mallRoles.stream().filter(t -> t.getName().equals(roleName)).findFirst().get();
            if (dataSelect != null && dataSelect == 1) {
                // 本机及子级
                List<OrgAndSon> orgAndSon = user.getOrgAndSon();
                List<String> collect = orgAndSon.stream().map(t -> t.getOrgId()).collect(Collectors.toList());
                lambdaQuery.in(!CollectionUtils.isEmpty(collect), Orders::getOrgId, collect);
            } else if (dataSelect != null && dataSelect == 2) {
                // 只看本级
                lambdaQuery.eq(Orders::getEnterpriseId, user.getEnterpriseId());
            } else if (dataSelect != null && dataSelect == 3 && role.getOrgSearch() == 1) {
                // 指定
                if (dataScopes != null && dataScopes.size() > 0) {
                    lambdaQuery.in(Orders::getOrgId, dataScopes);
                }
                if (CollectionUtils.isEmpty(dataScopes)) {
                    // 查询除自己意外的机构
                    List<OrgAndSon> orgAndSon = user.getOrgAndSon();
                    List<String> orgIds = orgAndSon.stream().map(OrgAndSon::getOrgId).filter(t -> !t.equals(user.getOrgId())).collect(Collectors.toList());
                    lambdaQuery.in(!CollectionUtils.isEmpty(orgIds), Orders::getOrgId, orgIds);
                    lambdaQuery.in(CollectionUtils.isEmpty(orgIds), Orders::getEnterpriseId, user.getEnterpriseId());
                }
            }
        } else {
            eq = lambdaQuery.eq(Orders::getEnterpriseId, user.getEnterpriseId());
        }
        PageUtils pageUtils = listPlatformOrdersByParameters(jsonObject, lambdaQuery);
        return pageUtils;
    }


    @Override
    public List<Orders> getDataByParentId(String orderId) {
        LambdaQueryWrapper<Orders> q = new LambdaQueryWrapper<>();
        q.eq(Orders::getParentOrderId, orderId);
        List<Orders> one = list(q);
        return one;
    }


    @Override
    public void rollBackPlanAndOrder(String idStr) {
        interfaceLogsService.getById(idStr);
    }

    /**
     * 据参数分页查询订单信息（店铺导出）
     *
     * @param jsonObject
     * @param qx
     * @param response
     */
    @Override
    public void shopManageListByParametersExport(JSONObject jsonObject, LambdaQueryWrapper<Orders> qx, HttpServletResponse response) {
        QueryWrapper<Orders> q = new QueryWrapper<>();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        Integer state = (Integer) innerMap.get("state");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        String startDate = (String) innerMap.get("startDate");
        String parentOrderId = (String) innerMap.get("parentOrderId");
        String endDate = (String) innerMap.get("endDate");
        String okStartDate = (String) innerMap.get("okStartDate");
        String okEndDate = (String) innerMap.get("okEndDate");
        String deliverGoodsStartDate = (String) innerMap.get("deliverGoodsStartDate");
        String deliverGoodsEndDate = (String) innerMap.get("deliverGoodsEndDate");
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        Integer productType = (Integer) innerMap.get("productType");
        String shopName = (String) innerMap.get("shopName");
        String untitled = (String) innerMap.get("untitled");
        String orderSn = (String) innerMap.get("orderSn");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        Boolean isQueryTwoOrder = (Boolean) innerMap.get("isQueryTwoOrder");
        List<String> ids = (ArrayList<String>) jsonObject.get("ids");
        // 只查询一级订单
        if (isQueryTwoOrder == null || !isQueryTwoOrder) {
            q.isNull("o.parent_order_id");
        }
        if (productType != null && productType == 12) {
            q.eq("o.supplier_id", ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        } else {
            q.eq("o.shop_id", ThreadLocalUtil.getCurrentUser().getShopId());
        }
        q.in(io.seata.common.util.CollectionUtils.isNotEmpty(ids), "o.order_id", ids);
        q.like(StringUtils.isNotEmpty(shopName), "o.shop_name", shopName);
        q.like(StringUtils.isNotEmpty(untitled), "o.untitled", untitled);
        q.like(StringUtils.isNotEmpty(orderSn), "o.order_sn", orderSn);
        q.eq(StringUtils.isNotEmpty(parentOrderId), "o.parent_order_id", parentOrderId);
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like("o.order_sn", keywords)
                        .or()
                        .like("o.untitled", keywords)
                        .or()
                        .like("o.shop_name", keywords)
                        .or()
                        .like("o.receiver_mobile", keywords);
            });
        }
        q.ge(StringUtils.isNotBlank(abovePrice), "o.actual_amount", abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), "o.actual_amount", belowPrice);
        q.eq(orderClass != null, "o.order_class", orderClass);
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), "o.gmt_create", startDate, endDate);
        q.between(StringUtils.isNotEmpty(okStartDate) && StringUtils.isNotEmpty(okEndDate), "o.flish_time", okStartDate, okEndDate);
        q.between(StringUtils.isNotEmpty(deliverGoodsStartDate) && StringUtils.isNotEmpty(deliverGoodsEndDate), "o.delivery_time", deliverGoodsStartDate, deliverGoodsEndDate);
        q.eq(state != null, "o.state", state);
        q.eq(productType != null, "o.product_type", productType);
        if (orderBy == null) {
            orderBy = 1;
        }
        if (orderBy == 1) {
            q.orderByDesc("o.gmt_create");
        }
        if (orderBy == 2) {
            q.orderByDesc("o.flish_time");
        }
        if (orderBy == 3) {
            q.orderByDesc("o.delivery_time");
        }
        if (orderBy == 4) {
            q.orderByDesc("o.success_date");
        }
        q.eq("o.is_delete", 0);
        q.eq("oi.is_delete", 0);
        q.last("LIMIT 5000");
        List<GetShopManageOrderOutZIPDataVO> vos = baseMapper.getShopManageOrderOutZIPData(q);
        if (CollectionUtils.isEmpty(vos)) {
            throw new BusinessException("数据为空！");
        }
        FileInputStream fin = null;
        Workbook workBook = null;
        ByteArrayOutputStream excelOut = null;
        InputStream excelIS = null;
        ByteArrayOutputStream pdfOut = null;
        HashMap<String, InputStream> zipMapList = new HashMap<>();
        for (GetShopManageOrderOutZIPDataVO vo : vos) {
            for (GetShopManageOrderOutZIPDataItemVO getShopManageOrderOutZIPDataItemVO : vo.getDataList()) {
                try {
                    try {
                        // 解析数据为excel
                        String templateFormUrl = mallConfig.templateFormUrl;
                        String url = null;
                        if (productType == 13) {
                            url = templateFormUrl + File.separator + "大宗零购合并订单导出模板.xlsx";
                        } else {
                            url = templateFormUrl + File.separator + "零星采购合并订单导出模板.xlsx";
                        }
                        fin = new FileInputStream(url);
                        Map<String, Object> map = new HashMap<>();
                        map.put("dataList", vo.getDataList());

                        BigDecimal subtotalTotalAmount = new BigDecimal(0);
                        BigDecimal subtotalMoRateAmount = new BigDecimal(0);

                        // 计算金额
                        for (GetShopManageOrderOutZIPDataItemVO d : vo.getDataList()) {
                            subtotalTotalAmount = subtotalTotalAmount.add(d.getTotalAmount());
                            subtotalMoRateAmount = subtotalMoRateAmount.add(d.getNoRateAmount());
                        }

                        map.put("subtotalMoRateAmountStr", subtotalMoRateAmount.toPlainString());
                        map.put("subtotalTotalAmountStr", subtotalTotalAmount.toPlainString());
                        map.put("supplierName", vo.getSupplierName());
                        map.put("enterpriseName", vo.getEnterpriseName());

//                XLSTransformer transformer = new XLSTransformer();
//                workBook = transformer.transformXLS(fin, map);
//                Sheet sheetAt = workBook.getSheetAt(0);
//                workBook.write(excelOut);
                        // 最后几个合并
//                sheetAt.addMergedRegion(new CellRangeAddress(sheetAt.getLastRowNum(), sheetAt.getLastRowNum(), 1, 10));
//                sheetAt.addMergedRegion(new CellRangeAddress(sheetAt.getLastRowNum() - 1, sheetAt.getLastRowNum() - 1, 1, 10));

                        // 获取输入流
                        excelOut = new ByteArrayOutputStream();
                        PoiExporter.export2Destination(fin, map, excelOut);
                        excelIS = new ByteArrayInputStream(excelOut.toByteArray());

                        // excel转成pdf
                        List<ExcelObject> excelSList = new ArrayList<>();
                        excelSList.add(new ExcelObject("", excelIS));
                        pdfOut = new ByteArrayOutputStream();
                        Excel2Pdf pdf = new Excel2Pdf(excelSList, pdfOut);
                        pdf.convert();

                        InputStream pdfIS = new ByteArrayInputStream(pdfOut.toByteArray());
                        zipMapList.put(vo.getEnterpriseName() + "订单数据.pdf", pdfIS);
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        try {
                            if (pdfOut != null) pdfOut.close();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        try {
                            if (excelIS != null) excelIS.close();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        try {
                            if (excelOut != null) excelOut.close();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        try {
                            if (workBook != null) workBook.close();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        try {
                            if (fin != null) fin.close();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }


                } catch (Exception e) {
                    e.getMessage();
                }
            }

        }
        try {
//            response.setContentType("application/zip");
//            String fileName = URLEncoder.encode("零星采购合并订单.zip", StandardCharsets.UTF_8.toString());
//            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            ZipUtil.zipFileInput(zipMapList, new ZipOutputStream(response.getOutputStream()));
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    /**
     * 根据订单id导出订单pdf（店铺导出pdf）
     *
     * @param orderId
     * @param response
     */
    @Override
    public void getOrderInfoOutPutPdf(String orderId, HttpServletResponse response) {
        Orders order = lambdaQuery().eq(Orders::getOrderId, orderId)
                .select(Orders::getOrderId,
                        Orders::getOrderSn,
                        Orders::getGmtCreate,
                        Orders::getNoRateAmount,
                        Orders::getActualAmount,
                        Orders::getSupplierName,
                        Orders::getOrderRemark,
                        Orders::getEnterpriseName
                ).one();
        if (order == null) {
            throw new BusinessException("订单不存在！");
        }
        GetShopManageOrderOutZIPDataVO vo = new GetShopManageOrderOutZIPDataVO();
        ArrayList<GetShopManageOrderOutZIPDataItemVO> dtls = new ArrayList<>();
        List<OrderItem> list = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, orderId).list();
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("订单明细不存在！");
        }

        Date gmtModified = order.getGmtCreate();
        LocalDateTime localDateTime = gmtModified.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("YYYY-MM-dd HH:mm:ss");
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("createDateStr", localDateTime.format(formatter));
        dataMap.put("supplierName", order.getSupplierName());
        dataMap.put("enterpriseName", order.getEnterpriseName());
        dataMap.put("orderSn", order.getOrderSn());
        dataMap.put("subtotalMoRateAmountStr", order.getNoRateAmount().toPlainString());
        dataMap.put("subtotalTotalAmountStr", order.getActualAmount().toPlainString());

        // 处理明细
        for (OrderItem orderItem : list) {
            GetShopManageOrderOutZIPDataItemVO dtl = new GetShopManageOrderOutZIPDataItemVO();
            BeanUtils.copyProperties(orderItem, dtl);
            dtl.setOrderRemark(order.getOrderRemark());
            dtls.add(dtl);
        }

        vo.setDataList(dtls);
        // 导出
        dataMap.put("dataList", vo.getDataList());
        FileInputStream fin = null;
        Workbook workBook = null;
        ByteArrayOutputStream excelOut = null;
        InputStream excelIS = null;
        try {
            // 解析数据为excel
            String templateFormUrl = mallConfig.templateFormUrl;
            String url = templateFormUrl + File.separator + "零星采购订单导出模板.xlsx";
            fin = new FileInputStream(url);

            // 获取输入流
            excelOut = new ByteArrayOutputStream();

            PoiExporter.export2Destination(fin, dataMap, excelOut);

            excelIS = new ByteArrayInputStream(excelOut.toByteArray());

            // excel转成pdf
//            String fileName = URLEncoder.encode("零星采购订单.pdf", StandardCharsets.UTF_8.toString());
//            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            List<ExcelObject> excelSList = new ArrayList<>();
            excelSList.add(new ExcelObject("", excelIS));
            Excel2Pdf pdf = new Excel2Pdf(excelSList, response.getOutputStream());
            pdf.convert();

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (excelIS != null) excelIS.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (excelOut != null) excelOut.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (workBook != null) workBook.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (fin != null) fin.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 据主订单项id生成大宗子订单（选择供方生成二级订单）
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreateContractTwoOrder(BatchCreateTwoOrderDTO dto) {
        List<String> orderItemId = dto.getOrderItemId();
        String supplierId = dto.getSupplierId();
        String supplierName = dto.getSupplierName();
        List<OrderItem> orderItems = orderItemService.lambdaQuery().in(OrderItem::getOrderItemId, orderItemId).list();
        if (CollectionUtils.isEmpty(orderItems)) {
            throw new BusinessException("订单不存在！");
        }
        for (OrderItem orderItem : orderItems) {
            if (orderItem.getBuyCounts().compareTo(orderItem.getReturnCounts()) == 0) {
                throw new BusinessException("物资【" + orderItem.getProductName() + "】已全部完成退货不能生成二级订单！");
            }
        }
        String orderId = orderItems.get(0).getOrderId();
        Orders orders = getById(orderId);

        // 保存订单、计算金额
        Orders newOrders = new Orders();
        BeanUtils.copyProperties(orders, newOrders);
        newOrders.setOrderId(null);
        newOrders.setUntitled(null);
        newOrders.setOrderSn(null);
        EnterpriseInfo en = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, supplierId)
                .select(EnterpriseInfo::getTaxRate, EnterpriseInfo::getEnterpriseId).one();
        if (en == null) {
            throw new BusinessException("供应商不存在！");
        }
        BigDecimal taxRate = en.getTaxRate();
        if (taxRate == null) {
            throw new BusinessException("供应商未设置税率！");
        }
        String untitled2 = "";
        for (OrderItem newOrderItem : orderItems) {
            untitled2 = untitled2 + newOrderItem.getProductName() + ",";
        }
        newOrders.setUntitled(untitled2.substring(0, untitled2.length() - 1));
        newOrders.setActualAmount(BigDecimal.ZERO);
        newOrders.setNoRateAmount(BigDecimal.ZERO);
        newOrders.setTotalAmount(BigDecimal.ZERO);
        newOrders.setCostPriceTotal(BigDecimal.ZERO);
        newOrders.setOrderClass(3);
        newOrders.setParentOrderId(orders.getOrderId());
        newOrders.setOrderSourceType(3);
        newOrders.setOrderSourceId(orders.getParentOrderId());
        newOrders.setSupplierId(supplierId);
        newOrders.setSupplierName(supplierName);
        newOrders.setOrderSn(OrderUtils.getOrder());
        save(newOrders);
        // 保存好了订单项，
        for (OrderItem newOrderItem : orderItems) {
            newOrderItem.setOrderId(newOrders.getOrderId());
            newOrderItem.setOrderSn(newOrders.getOrderSn());
            newOrderItem.setTaxRate(taxRate);
            newOrderItem.setProductPrice(BigDecimal.ZERO);
            newOrderItem.setNoRatePrice(BigDecimal.ZERO);
            newOrderItem.setTotalAmount(BigDecimal.ZERO);
            newOrderItem.setNoRateAmount(BigDecimal.ZERO);
            newOrderItem.setCostPrice(BigDecimal.ZERO);
            newOrderItem.setCostAmount(BigDecimal.ZERO);
            newOrderItem.setOriginalPrice(BigDecimal.ZERO);
            // 保存父明细id
            newOrderItem.setParentOrderItemId(newOrderItem.getOrderItemId());
            newOrderItem.setOrderItemId(null);
        }
        orderItemService.saveBatch(orderItems);
        // 修改主订单明细状态
        orderItemService.lambdaUpdate().in(OrderItem::getOrderItemId, orderItemId)
                .set(OrderItem::getState, 1)
                .set(OrderItem::getGmtModified, new Date()).update();
    }

    /**
     * 根据分页参数查询大宗合同二级订单
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listContractTwoOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String parentOrderId = (String) innerMap.get("parentOrderId");
        Integer productType = (Integer) innerMap.get("productType");
        Boolean isQueryTwoOrder = (Boolean) innerMap.get("isQueryTwoOrder");
        // 只查询一级订单
        if (isQueryTwoOrder == null || !isQueryTwoOrder) {
            q.isNull(Orders::getParentOrderId);
        }
        q.eq(StringUtils.isNotEmpty(parentOrderId), Orders::getParentOrderId, parentOrderId);
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(Orders::getOrderSn, keywords)
                        .or()
                        .like(Orders::getUntitled, keywords)
                        .or()
                        .like(Orders::getShopName, keywords)
                        .or()
                        .like(Orders::getReceiverMobile, keywords);
            });
        }
        q.eq(productType != null, Orders::getProductType, productType);
        IPage<Orders> page = this.page(
                new Query<Orders>().getPage(jsonObject),
                q
        );
        List<Orders> records = page.getRecords();
        if (records != null && records.size() > 0) {
            for (Orders record : records) {
                //获取二级供应商项目部的名称（物资公司）
                Orders one = lambdaQuery().eq(Orders::getOrderId, record.getParentOrderId()).select(Orders::getSupplierName).one();
                record.setTwoEnterpriseName(one.getSupplierName());
            }
        }

        return new PageUtils(page);
    }

    /**
     * 生成大宗临购订单
     *
     * @param idStr
     * @param farArg
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitSynthesizeTemporaryOrder(GetSynthesizeTemporaryPlanDetailVO rDto, String idStr, StringBuilder farArg) {
        // 实际总金额
        BigDecimal actualAmount = new BigDecimal(0);
        BigDecimal noRateActualAmount = new BigDecimal(0);
        BigDecimal costPriceTotal = new BigDecimal(0);

        String synthesizeTemporaryId = rDto.getSynthesizeTemporaryId();
        SynthesizeTemporary synthesizeTemporary = synthesizeTemporaryService.getById(synthesizeTemporaryId);
        if (synthesizeTemporary == null) {
            throw new BusinessException("大宗临购清单不存在！");
        }
        String supplierOrgId = synthesizeTemporary.getSupplierOrgId();
        Shop shopOne = shopService.lambdaQuery()
                .eq(Shop::getEnterpriseId, supplierOrgId)
                .select(Shop::getShopName, Shop::getShopClass, Shop::getEnterpriseId, Shop::getShopId)
                .one();
        if (shopOne == null) {
            throw new BusinessException("店铺不存在！");
        }
        // 设置利率，计算金额
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, supplierOrgId)
                .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate).one();
        String enterpriseName = enterpriseInfo.getEnterpriseName();
        BigDecimal taxRate = rDto.getTaxRate();
        if (taxRate == null) {
            throw new BusinessException("供应商未设置税率！");
        }
        String untitled = "";
        List<GetSynthesizeTemporaryPlanDetailItemVO> details = rDto.getDetails();

        // 校验商品
        for (GetSynthesizeTemporaryPlanDetailItemVO dto : details) {
            // 校验可下单数量是不是超过了
            String productId = dto.getProductId();
            Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
            if (product == null) {
                Product byId = productService.getProductExcludeRemarkById(productId);
                if (byId != null) {
                    throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
                } else {
                    throw new BusinessException(OrderEnum.RESULT_CODE_500201.getRemark());
                }
            } else {
                if (mallConfig.isCountPlanOrderNum == 1) {
                    // TODO 计划最新统计
                    BigDecimal qty = getOrderUseCountBySelectPlanDtlIds(dto.getDtlId());
                    dto.setReceivedQuantity(qty);
                }


                BigDecimal selectQty = dto.getSelectQty();
                BigDecimal receivedQuantity = dto.getReceivedQuantity();
                if (receivedQuantity == null) {
                    receivedQuantity = new BigDecimal(0);
                }
                if (receivedQuantity.compareTo(dto.getQty()) == 0) {
                    throw new BusinessException("商品名为：【" + product.getProductName() + "】已完成下单，不可再次下单！");
                }
                BigDecimal subQty = dto.getQty().subtract(receivedQuantity);
                // 如果选择的数量大于了
                if (selectQty.compareTo(subQty) == 1) {
                    throw new BusinessException("商品名为：【" + product.getProductName() + "】已选数量超过可下单数量！");
                }


                ProductSku productSku = productSkuService.getProductSkuByProductId(productId, null).get(0);
                int i = dto.getSelectQty().compareTo(productSku.getStock());
                if (i == 1) {
                    throw new BusinessException("商品名为：【" + product.getProductName() + "】的商品库存不足！");
                }
                untitled = untitled + dto.getProductName() + ",";
                actualAmount = actualAmount.add(dto.getSynthesizePrice().multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
//                BigDecimal noRatePrice = null;
//                if (synthesizeTemporary.getBillType() == 1) {
//                    BigDecimal pr1 = TaxCalculator.calculateNotTarRateAmount(dto.getNetPrice(), taxRate);
//                    BigDecimal pr2 = TaxCalculator.calculateNotTarRateAmount(dto.getFixationPrice(), taxRate);
//                    noRatePrice = pr1.add(pr2);
//                }
//                if (synthesizeTemporary.getBillType() == 2) {
//                    BigDecimal pr1 = TaxCalculator.calculateNotTarRateAmount(dto.getOutFactoryPrice(), taxRate);
//                    BigDecimal pr2 = TaxCalculator.calculateNotTarRateAmount(dto.getTransportPrice(), taxRate);
//                    noRatePrice = pr1.add(pr2);
//                }
//                noRateActualAmount = noRateActualAmount.add(noRatePrice.multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
                BigDecimal noRateAmount = TaxCalculator.calculateNotTarRateAmount(dto.getSynthesizePrice().multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP), taxRate);
                noRateActualAmount = noRateActualAmount.add(noRateAmount);
                costPriceTotal = costPriceTotal.add(productSku.getCostPrice().multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
            }
        }


        Orders order = new Orders();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String userId = user.getUserId();
        order.setOrderSn(OrderUtils.getOrder());
        order.setShopName(shopOne.getShopName());
        order.setShopId(shopOne.getShopId());
        order.setUserId(userId);
        order.setUntitled(untitled.substring(0, untitled.length() - 1));
        order.setReceiverName(rDto.getReceiverName());
        order.setReceiverMobile(rDto.getReceiverMobile());
        order.setReceiverAddress(rDto.getSubOrderAddress());
        order.setTotalAmount(BigDecimal.ZERO);
        order.setActualAmount(actualAmount);
        if (mallConfig.isNotRateAmount == 1) {
            order.setNoRateAmount(noRateActualAmount);
        } else {
            order.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(actualAmount, taxRate));
        }
        order.setPayWay(2);
        order.setOrderRemark(rDto.getOrderRemark());
        order.setState(OrderEnum.STATE_FINISH.getCode());
        order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());
        order.setDeliveryType(0);
        order.setDeliveryFlowId(null);
        order.setLogisticsCompany(null);
        order.setOrderFreight(BigDecimal.ZERO);
        order.setProfitPriceTotal(actualAmount.subtract(costPriceTotal));
        order.setCostPriceTotal(costPriceTotal);
        order.setFlishTime(new Date());
        order.setProductType(13);
        order.setSupplierId(supplierOrgId);
        order.setSupplierName(enterpriseName);
        order.setOrgId(user.getOrgId());
        order.setEnterpriseId(user.getEnterpriseId());
        order.setEnterpriseName(user.getEnterpriseName());
        if (shopOne.getShopClass() == 2) {
            order.setOrderClass(2);
        } else {
            order.setOrderClass(1);
        }
        order.setOrderSourceType(4);
        order.setOrderSourceId(synthesizeTemporary.getSynthesizeTemporaryId());
        order.setTaxRate(taxRate);
        order.setBillType(synthesizeTemporary.getBillType());
        order.setPaymentWeek(synthesizeTemporary.getPaymentWeek());
        order.setOutPhaseInterest(synthesizeTemporary.getOutPhaseInterest());
        order.setOrderFreight(new BigDecimal("0"));
        order.setOutKeyId(idStr);
        boolean save = this.save(order);
        if (!save) {
            throw new BusinessException("订单保存失败！");
        }
        ArrayList<ProductSku> productSkus = new ArrayList<>();
        ArrayList<OrderSelectPlan> orderSelectPlans = new ArrayList<>();
        ArrayList<Map<String, Object>> retPlanList = new ArrayList<>();
        // 要下架的商品id
        List<String> outProductId = new ArrayList<>();
        // 处理拆单
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        // 拆单
        Integer shopClass = shopOne.getShopClass();
        for (GetSynthesizeTemporaryPlanDetailItemVO dto : details) {
            ProductSku productSku = productSkuService.getProductSkuByProductId(dto.getProductId(), null).get(0);
            OrderItem orderItem = new OrderItem();
            orderItem.setOrderId(order.getOrderId());
            orderItem.setOrderSn(order.getOrderSn());
            orderItem.setUnit(dto.getUnit());
            orderItem.setProductId(dto.getProductId());
            orderItem.setProductSn(dto.getProductSn());
            orderItem.setProductName(dto.getProductName().trim());
            orderItem.setRelevanceName(dto.getMaterialName().trim());
            orderItem.setRelevanceNo(dto.getMaterialSn());
            orderItem.setRelevanceId(dto.getMaterialId());

            Product byId = productService.getById(dto.getProductId());
            orderItem.setProductImg(byId.getProductMinImg());
            if (productSku != null) {
                orderItem.setSkuId(productSku.getSkuId());
            }
            if (dto.getSpec() != null) {
                orderItem.setSkuName(dto.getSpec().trim());

            }
            orderItem.setCostPrice(dto.getCostPrice());
            orderItem.setCostAmount(dto.getCostPrice().multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
            orderItem.setOriginalPrice(BigDecimal.ZERO);
            orderItem.setProductPrice(dto.getSynthesizePrice());
            orderItem.setTaxRate(enterpriseInfo.getTaxRate());
            BigDecimal noRatePrice = null;
            if (synthesizeTemporary.getBillType() == 1) {
                BigDecimal pr1 = TaxCalculator.calculateNotTarRateAmount(dto.getNetPrice(), taxRate);
                BigDecimal pr2 = TaxCalculator.calculateNotTarRateAmount(dto.getFixationPrice(), taxRate);
                noRatePrice = pr1.add(pr2);
            }
            if (synthesizeTemporary.getBillType() == 2) {
                BigDecimal pr1 = TaxCalculator.calculateNotTarRateAmount(dto.getOutFactoryPrice(), taxRate);
                BigDecimal pr2 = TaxCalculator.calculateNotTarRateAmount(dto.getTransportPrice(), taxRate);
                noRatePrice = pr1.add(pr2);
            }
            orderItem.setNoRatePrice(noRatePrice);
            orderItem.setTotalAmount(dto.getSelectQty().multiply(dto.getSynthesizePrice()).setScale(2, RoundingMode.HALF_UP));
//            orderItem.setNoRateAmount(noRatePrice.multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
            orderItem.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(orderItem.getTotalAmount(), taxRate));

            orderItem.setBuyCounts(dto.getSelectQty());
            orderItem.setProductType(13);
            if (shopOne.getShopClass() == 2) {
                orderItem.setState(1);
            } else {
                orderItem.setState(0);
            }
            orderItem.setTotalAmount(dto.getSynthesizePrice().multiply(dto.getSelectQty()));
            orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
            orderItem.setBuyTime(new Date());
            orderItem.setProductType(13);
            orderItem.setSupplierId(dto.getSupplierOrgId());
            orderItem.setSupplierName(dto.getSupplierName());
            orderItem.setCostPrice(dto.getCostPrice());
            orderItem.setCostAmount(dto.getCostPrice().multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
            orderItem.setClassPathName(dto.getClassNamePath());
            orderItem.setClassPathId(dto.getClassIdPath());
            orderItem.setClassId(dto.getClassId());
            orderItem.setBrandId(dto.getBrandId());
            orderItem.setTexture(dto.getTexture() == null ? null : dto.getTexture().trim());
            orderItem.setBrandName(dto.getBrandName());
            orderItem.setNetPrice(dto.getNetPrice());
            orderItem.setFixationPrice(dto.getFixationPrice());
            orderItem.setOutFactoryPrice(dto.getOutFactoryPrice());
            orderItem.setTransportPrice(dto.getTransportPrice());
            orderItem.setProfitPrice(dto.getSynthesizePrice().subtract(dto.getCostPrice()));

            // 副单位
            orderItem.setIsTwoUnit(dto.getIsTwoUnit());
            orderItem.setTwoUnit(dto.getTwoUnit());
            orderItem.setTwoUnitNum(dto.getTwoUnitNum());
            orderItem.setSecondUnitNum(dto.getSecondUnitNum());

            orderItemService.save(orderItem);
            // 拆单所需数据
            if (shopClass == 2) {
                orderItems.add(orderItem);
            }

            // 扣减库存
            String skuId = productSku.getSkuId();
            BigDecimal newStock = productSku.getStock().subtract(dto.getSelectQty());
            ProductSku productSku2 = new ProductSku();
            productSku2.setSkuId(skuId);
            productSku2.setStock(newStock);
            productSkus.add(productSku2);
            // 判断库存是否等于0如果等于0则下架商品
            if (newStock.compareTo(BigDecimal.ZERO) == 0) {
                outProductId.add(dto.getProductId());
            }

            // 保存计划订单
            OrderSelectPlan orderSelectPlan = new OrderSelectPlan();
            orderSelectPlan.setOrderId(order.getOrderId());
            orderSelectPlan.setOrderSn(order.getOrderSn());
            orderSelectPlan.setOrderItemId(orderItem.getOrderItemId());
            orderSelectPlan.setDtlId(dto.getDtlId());
            orderSelectPlan.setEquipmentName(dto.getProductName());
            orderSelectPlan.setCount(dto.getSelectQty());
            orderSelectPlan.setSourcePlanQty(dto.getQty()); // 没意义
            orderSelectPlan.setBillId(rDto.getBillId());
            orderSelectPlan.setBillNo(rDto.getBillNo());
            orderSelectPlan.setProductType(13);
            orderSelectPlan.setOrgId(user.getOrgId());
            orderSelectPlan.setBillType(synthesizeTemporary.getBillType());
            orderSelectPlan.setOrgName(user.getEnterpriseName());
            orderSelectPlan.setPrice(dto.getSynthesizePrice());
            orderSelectPlan.setAccount(dto.getSynthesizePrice().multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
            orderSelectPlan.setSynthesizeTemporaryDtlId(dto.getSynthesizeTemporaryDtlId());
            // TODO 都赋值为供应商内部机构id
            orderSelectPlan.setStorageId(rDto.getSupplierOrgFarId());
            orderSelectPlan.setStorageOrgId(rDto.getSupplierOrgFarId());


            orderSelectPlan.setStorageName(rDto.getSupplierOrgName());
            orderSelectPlan.setShortCode(rDto.getOrgShort());
            orderSelectPlan.setCreditCode(rDto.getCreditCode());
            orderSelectPlan.setPlanType(3);
            orderSelectPlan.setTaxRate(order.getTaxRate());
            // 如果信用代码为空表示是内部供应商
            if (StringUtils.isEmpty(rDto.getCreditCode())) {
                orderSelectPlan.setSupplierType(2);
            } else {
                orderSelectPlan.setSupplierType(1);
            }
            orderSelectPlans.add(orderSelectPlan);

            // 组装反写计划
            HashMap<String, Object> onePlanMap = new HashMap<>();
            onePlanMap.put("dtlId", dto.getDtlId());
            onePlanMap.put("billId", dto.getBillId());
            onePlanMap.put("amount", orderItem.getNoRateAmount());
            onePlanMap.put("number", dto.getSelectQty());
            retPlanList.add(onePlanMap);
        }

        // 处理拆单
        if (shopClass == 2) {
            // 生成订单号
            Map<String, List<OrderItem>> collectOrderItems = orderItems.stream()
                    .collect(Collectors.groupingBy(OrderItem::getSupplierId));
            collectOrderItems.forEach((supplierId, newOrderItems) -> {
                // 保存订单、计算金额
                Orders orders = new Orders();
                BeanUtils.copyProperties(order, orders);
                orders.setOrderId(null);
                orders.setUntitled(null);
                orders.setOrderSn(null);
                orders.setEnterpriseId(user.getEnterpriseId());
                orders.setEnterpriseName(user.getEnterpriseName());
                // 总成本价
                BigDecimal costPriceTotal2 = new BigDecimal(0);
                EnterpriseInfo enterpriseInfoOne = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, supplierId)
                        .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate).one();
                if (enterpriseInfoOne != null) {
                    orders.setSupplierId(supplierId);
                    orders.setSupplierName(enterpriseInfoOne.getEnterpriseName());
                } else {
                    throw new BusinessException("供应商不存在！");
                }
                BigDecimal taxRate2 = enterpriseInfoOne.getTaxRate();
                if (taxRate2 == null || taxRate2.compareTo(BigDecimal.ZERO) == 0) {
                    throw new BusinessException("【" + enterpriseInfoOne.getEnterpriseName() + "】供应商未设置利率！");
                }
                BigDecimal noRateActualAmount2 = new BigDecimal(0);
                String untitled2 = "";
                for (OrderItem newOrderItem : newOrderItems) {
                    costPriceTotal2 = costPriceTotal2.add(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
//                    BigDecimal noRatePrice2 = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate2);
//                    noRateActualAmount2 = noRateActualAmount2.add(noRatePrice2.multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
                    BigDecimal noRateAmount = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP), taxRate2);
                    noRateActualAmount2 = noRateActualAmount2.add(noRateAmount);
                    untitled2 = untitled2 + newOrderItem.getProductName() + ",";
                }

                orders.setUntitled(untitled2.substring(0, untitled2.length() - 1));
                orders.setActualAmount(costPriceTotal2);
                if (mallConfig.isNotRateAmount == 1) {
                    orders.setNoRateAmount(noRateActualAmount2);
                } else {
                    orders.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(costPriceTotal2, taxRate2));
                }
                orders.setTotalAmount(BigDecimal.ZERO);
                orders.setCostPriceTotal(BigDecimal.ZERO);
                orders.setOrderClass(3);
                orders.setParentOrderId(order.getOrderId());
                orders.setOrderSourceType(5);
                orders.setOrderSourceId(orders.getParentOrderId());
                orders.setTaxRate(taxRate2);
                orders.setOrderSn(OrderUtils.getOrder());
                save(orders);
                // 保存好了订单项，
                for (OrderItem newOrderItem : newOrderItems) {
                    newOrderItem.setOrderId(orders.getOrderId());
                    newOrderItem.setOrderSn(orders.getOrderSn());
                    newOrderItem.setProductPrice(newOrderItem.getCostPrice());
                    newOrderItem.setTotalAmount(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
                    newOrderItem.setTaxRate(taxRate2);
                    newOrderItem.setNoRatePrice(TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate2));
                    BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate2);
//                    newOrderItem.setNoRateAmount(noRatePrice.multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
                    newOrderItem.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(newOrderItem.getTotalAmount(), taxRate2));
                    newOrderItem.setCostPrice(BigDecimal.ZERO);
                    newOrderItem.setCostAmount(BigDecimal.ZERO);
                    newOrderItem.setOriginalPrice(BigDecimal.ZERO);
                    newOrderItem.setParentOrderItemId(newOrderItem.getOrderItemId());


                    newOrderItem.setTransportPrice(BigDecimal.ZERO);
                    newOrderItem.setOutFactoryPrice(BigDecimal.ZERO);
                    newOrderItem.setFixationPrice(BigDecimal.ZERO);
                    newOrderItem.setNetPrice(BigDecimal.ZERO);

                    newOrderItem.setOrderItemId(null);
                }
                orderItemService.saveBatch(newOrderItems);
            });
        }
        // 调用反写接口
        HashMap<Object, Object> subPlanDDTO = new HashMap<>();
        subPlanDDTO.put("data", retPlanList);
        subPlanDDTO.put("keyId", idStr);
        subPlanDDTO.put("orgId", user.getOrgId());

        String content = JSON.toJSONString(subPlanDDTO);
        farArg.append(content);
        log.warn("反写大宗临购计划接口请求参数：" + content);
        if (mallConfig.isCountPlanOrderNum == 0) {
            // 发送请求
            String url = mallConfig.prodPcwp2Url02 + "/thirdapi/bulkRetailPlan/updateBulkRetailPlanDtl";
            LogUtil.writeInfoLog(idStr, "submitSynthesizeTemporaryOrder", rDto, subPlanDDTO, null, OrdersServiceImpl.class);
            R rMap = null;
            try {
                rMap = restTemplateUtils.postPCWP2(url, subPlanDDTO);
            } catch (Exception e) {
                LogUtil.writeErrorLog(idStr, "submitSynthesizeTemporaryOrder", rDto, subPlanDDTO, null, e.getMessage(), OrdersServiceImpl.class);
                log.error(e.getMessage());
                throw new BusinessException("远程反写大宗临购计划服务异常：" + e.getMessage());
            }
            if (rMap.getCode() == null || rMap.getCode() != 200) {
                LogUtil.writeErrorLog(idStr, "submitSynthesizeTemporaryOrder", rDto, subPlanDDTO, rMap, rMap.getMessage(), OrdersServiceImpl.class);
                log.error("反写大宗临购计划接口错误！返回：" + rMap);
                throw new BusinessException("远程反写大宗临购计划服务异常：" + rMap);
            }
        }

        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(OrdersServiceImpl.class.getName());
        iLog.setMethodName("submitSynthesizeTemporaryOrder");
        iLog.setLocalArguments(JSON.toJSONString(rDto));
        iLog.setFarArguments(content);
        iLog.setIsSuccess(1);
        iLog.setLogType(1);
        iLog.setErrorInfo(null);
        interfaceLogsService.create(iLog);
        // 保存关联计划信息
        orderSelectPlanService.saveBatch(orderSelectPlans);
        // 修改库存
        productSkuService.updateBatchById(productSkus);
        // 下架商品
        if (!CollectionUtils.isEmpty(outProductId)) {
            UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
            updateProductStateDTO.setState(2);
            updateProductStateDTO.setProductIds(outProductId);
            productService.updateProductState(updateProductStateDTO);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitInventoryOrder(GetSynthesizeTemporaryPlanDetailVO rDto, String idStr, StringBuilder farArg) {
        String synthesizeTemporaryId = rDto.getSynthesizeTemporaryId();
        SynthesizeTemporary synthesizeTemporary = synthesizeTemporaryService.getById(synthesizeTemporaryId);
        if (synthesizeTemporary == null) {
            throw new BusinessException("大宗临购清单不存在！");
        }
        // 查询是否有已经中标的竞价信息
        String synthesizeTemporarySn = synthesizeTemporary.getSynthesizeTemporarySn();
        List<HitBidVo> hitBidVos = biddingPurchaseService.getBiddingPurchaseBySynthesizeTemporarySn(synthesizeTemporarySn);
        if (CollectionUtils.isEmpty(hitBidVos)) {
            // 没有竞价信息，走原来的生成订单流程
            SpringBeanUtil.getBean(OrdersServiceImpl.class).submitSynthesizeTemporaryOrder(rDto, idStr, farArg);
            return;
        }
        // 剩下的部分比对明细分为
        // 已经竞价的部分和没竞价的部分
        List<GetSynthesizeTemporaryPlanDetailItemVO> submitDetails = rDto.getDetails();
        List<GetSynthesizeTemporaryPlanDetailItemVO> details = rDto.getDetails();
        // 校验商品
        for (GetSynthesizeTemporaryPlanDetailItemVO dto : details) {
            // 校验可下单数量是不是超过了
            String productId = dto.getProductId();
            Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
            if (product == null) {
                Product byId = productService.getProductExcludeRemarkById(productId);
                if (byId != null) {
                    throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
                } else {
                    throw new BusinessException(OrderEnum.RESULT_CODE_500201.getRemark());
                }
            } else {
                if (mallConfig.isCountPlanOrderNum == 1) {
                    // TODO 计划最新统计
                    BigDecimal qty = getOrderUseCountBySelectPlanDtlIds(dto.getDtlId());
                    dto.setReceivedQuantity(qty);
                }
                BigDecimal selectQty = dto.getSelectQty();
                BigDecimal receivedQuantity = dto.getReceivedQuantity();
                if (receivedQuantity == null) {
                    receivedQuantity = new BigDecimal(0);
                }
                if (receivedQuantity.compareTo(dto.getQty()) == 0) {
                    throw new BusinessException("商品名为：【" + product.getProductName() + "】已完成下单，不可再次下单！");
                }
                BigDecimal subQty = dto.getQty().subtract(receivedQuantity);
                // 如果选择的数量大于了
                if (selectQty.compareTo(subQty) == 1) {
                    throw new BusinessException("商品名为：【" + product.getProductName() + "】已选数量超过可下单数量！");
                }


                ProductSku productSku = productSkuService.getProductSkuByProductId(productId, null).get(0);
                int i = dto.getSelectQty().compareTo(productSku.getStock());
                if (i == 1) {
                    throw new BusinessException("商品名为：【" + product.getProductName() + "】的商品库存不足！");
                }
            }
        }
        // 使用竞价的数据
        List<GetSynthesizeTemporaryPlanDetailItemVO> hitBidDetails = new ArrayList<>();
        // 使用清单的数据
        List<GetSynthesizeTemporaryPlanDetailItemVO> noHitBidDetails = new ArrayList<>();
        // 分离数据
        for (GetSynthesizeTemporaryPlanDetailItemVO detailItemVO : submitDetails) {
            boolean found = false;
            for (HitBidVo hitBidVo : hitBidVos) {
                if (hitBidVo.getProductSn().equals(detailItemVO.getProductSn())) {
                    // 使用竞价的数据生成订单
                    hitBidDetails.add(detailItemVO);
                    found = true;
                    break;
                }
            }
            if (!found) {
                // 使用原来的数据生成订单
                noHitBidDetails.add(detailItemVO);
            }
        }

        String supplierOrgId = synthesizeTemporary.getSupplierOrgId();
        Shop shopOne = shopService.lambdaQuery()
                .eq(Shop::getEnterpriseId, supplierOrgId)
                .select(Shop::getShopName, Shop::getShopClass, Shop::getEnterpriseId, Shop::getShopId)
                .one();
        if (shopOne == null) {
            throw new BusinessException("店铺不存在！");
        }
        // 设置利率，计算金额
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, supplierOrgId)
                .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate).one();
        String enterpriseName = enterpriseInfo.getEnterpriseName();
        BigDecimal taxRate = rDto.getTaxRate();
        if (taxRate == null) {
            throw new BusinessException("供应商未设置税率！");
        }
        String untitled = "";
        // 物资公司订单
        // 实际总金额
        BigDecimal actualAmount = new BigDecimal(0);
        // 不含税订单总金额
        BigDecimal noRateActualAmount = new BigDecimal(0);
        // 总成本价
        BigDecimal costPriceTotal = new BigDecimal(0);
        // 先处理未参加竞价的部分
        for (GetSynthesizeTemporaryPlanDetailItemVO noHitBidDetail : noHitBidDetails) {
            untitled = untitled + noHitBidDetail.getProductName() + ",";
            actualAmount = actualAmount.add(noHitBidDetail.getSynthesizePrice().multiply(noHitBidDetail.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
            BigDecimal noRateAmount = TaxCalculator.calculateNotTarRateAmount(noHitBidDetail.getSynthesizePrice().multiply(noHitBidDetail.getSelectQty()).setScale(2, RoundingMode.HALF_UP), taxRate);
            noRateActualAmount = noRateActualAmount.add(noRateAmount);
            String productId = noHitBidDetail.getProductId();
            ProductSku productSku = productSkuService.getProductSkuByProductId(productId, null).get(0);
            costPriceTotal = costPriceTotal.add(productSku.getCostPrice().multiply(noHitBidDetail.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
        }
        // 处理竞价的部分
        for (GetSynthesizeTemporaryPlanDetailItemVO hitBidDetail : hitBidDetails) {
            for (HitBidVo hitBidVo : hitBidVos) {
                if (hitBidVo.getProductSn().equals(hitBidDetail.getProductSn())) {
                    untitled = untitled + hitBidDetail.getProductName() + ",";
                    actualAmount = actualAmount.add(hitBidDetail.getSynthesizePrice().multiply(hitBidDetail.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
                    // 订单税率计算使用竞价的税率
                    BigDecimal noRateAmount = TaxCalculator.calculateNotTarRateAmount(hitBidDetail.getSynthesizePrice().multiply(hitBidDetail.getSelectQty()).setScale(2, RoundingMode.HALF_UP), hitBidVo.getTaxRate());
                    noRateActualAmount = noRateActualAmount.add(noRateAmount);
                    // 订单成本价就是竞价的含税总单价
                    BigDecimal costPrice = hitBidVo.getBidRatePrice();
                    costPriceTotal = costPriceTotal.add(costPrice.multiply(hitBidDetail.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
                    // 更改原单的含税单价 浮动 固定 出厂价 运费
                }
            }

        }
        // 组装物资公司的一级订单
        Orders order = new Orders();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String userId = user.getUserId();
        order.setOrderSn(OrderUtils.getOrder());
        order.setShopName(shopOne.getShopName());
        order.setShopId(shopOne.getShopId());
        order.setUserId(userId);
        order.setUntitled(untitled.substring(0, untitled.length() - 1));
        order.setReceiverName(rDto.getReceiverName());
        order.setReceiverMobile(rDto.getReceiverMobile());
        order.setReceiverAddress(rDto.getSubOrderAddress());
        order.setTotalAmount(BigDecimal.ZERO);
        order.setActualAmount(actualAmount);
        if (mallConfig.isNotRateAmount == 1) {
            order.setNoRateAmount(noRateActualAmount);
        } else {
            order.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(actualAmount, taxRate));
        }
        order.setPayWay(2);
        order.setOrderRemark(rDto.getOrderRemark());
        order.setState(OrderEnum.STATE_FINISH.getCode());
        order.setOrderBillState(OrderEnum.ORDER_BILL_STATE_INIT.getCode());
        order.setDeliveryType(0);
        order.setDeliveryFlowId(null);
        order.setLogisticsCompany(null);
        order.setOrderFreight(BigDecimal.ZERO);
        order.setProfitPriceTotal(actualAmount.subtract(costPriceTotal));
        order.setCostPriceTotal(costPriceTotal);
        order.setFlishTime(new Date());
        order.setProductType(13);
        order.setSupplierId(supplierOrgId);
        order.setSupplierName(enterpriseName);
        order.setOrgId(user.getOrgId());
        order.setEnterpriseId(user.getEnterpriseId());
        order.setEnterpriseName(user.getEnterpriseName());
        if (shopOne.getShopClass() == 2) {
            order.setOrderClass(2);
        } else {
            order.setOrderClass(1);
        }
        order.setOrderSourceType(4);
        order.setOrderSourceId(synthesizeTemporary.getSynthesizeTemporaryId());
        order.setTaxRate(taxRate);
        order.setBillType(synthesizeTemporary.getBillType());
        order.setPaymentWeek(synthesizeTemporary.getPaymentWeek());
        order.setOutPhaseInterest(synthesizeTemporary.getOutPhaseInterest());
        order.setOrderFreight(new BigDecimal("0"));
        boolean save = this.save(order);
        if (!save) {
            throw new BusinessException("订单保存失败！");
        }
        // 一级订单完成
        // 组装一级订单明细
        ArrayList<ProductSku> productSkus = new ArrayList<>();
        ArrayList<OrderSelectPlan> orderSelectPlans = new ArrayList<>();
        ArrayList<Map<String, Object>> retPlanList = new ArrayList<>();
        // 要下架的商品id
        List<String> outProductId = new ArrayList<>();
        // 处理拆单
        ArrayList<OrderItem> orderItems = new ArrayList<>();
        // 拆单
        Integer shopClass = shopOne.getShopClass();
        // 先处理未参与竞价的
        SpringBeanUtil.getBean(OrdersService.class).batchCreateNotBidingOrder(order, synthesizeTemporary, enterpriseInfo, shopOne, productSkus, orderSelectPlans, retPlanList, outProductId, orderItems, noHitBidDetails, rDto, taxRate);
        // 处理竞价的
        SpringBeanUtil.getBean(OrdersService.class).batchCreateBidingOrder(order, synthesizeTemporary, enterpriseInfo, shopOne, productSkus, orderSelectPlans, retPlanList, outProductId, orderItems, hitBidDetails, rDto, taxRate, hitBidVos);
        // 物资公司订单组装完成
        // 处理二级订单
        if (shopClass == 2) {
            // 生成订单号
            Map<String, List<OrderItem>> collectOrderItems = orderItems.stream()
                    .collect(Collectors.groupingBy(OrderItem::getSupplierId));
            collectOrderItems.forEach((supplierId, newOrderItems) -> {
                // 保存订单、计算金额
                Orders orders = new Orders();
                BeanUtils.copyProperties(order, orders);
                orders.setOrderId(null);
                orders.setUntitled(null);
                orders.setOrderSn(null);
                // 竞价的订单默认直接确认
                orders.setEnterpriseId(user.getEnterpriseId());
                orders.setEnterpriseName(user.getEnterpriseName());
                // 总成本价
                BigDecimal costPriceTotal2 = new BigDecimal(0);
                EnterpriseInfo enterpriseInfoOne = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, supplierId)
                        .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate).one();
                if (enterpriseInfoOne != null) {
                    orders.setSupplierId(supplierId);
                    orders.setSupplierName(enterpriseInfoOne.getEnterpriseName());
                } else {
                    throw new BusinessException("供应商不存在！");
                }
                BigDecimal taxRate2 = enterpriseInfoOne.getTaxRate();
                if (taxRate2 == null || taxRate2.compareTo(BigDecimal.ZERO) == 0) {
                    throw new BusinessException("【" + enterpriseInfoOne.getEnterpriseName() + "】供应商未设置利率！");
                }
                BigDecimal noRateActualAmount2 = new BigDecimal(0);
                String untitled2 = "";
                for (OrderItem newOrderItem : newOrderItems) {
                    // 如果是竞价的，需要使用竞价的税率
                    List<BigDecimal> tax = hitBidVos.stream().filter(item -> item.getProductSn().equals(newOrderItem.getProductSn())).map(HitBidVo::getTaxRate).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(tax)) {
                        taxRate2 = tax.get(0);
                        //竞价的不能直接确认 24-10-08
                        orders.setAffirmState(1);
                        orders.setMasterAffirmState(1);
                    }
                    costPriceTotal2 = costPriceTotal2.add(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
//                    BigDecimal noRatePrice2 = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate2);
//                    noRateActualAmount2 = noRateActualAmount2.add(noRatePrice2.multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
                    BigDecimal noRateAmount = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP), taxRate2);
                    noRateActualAmount2 = noRateActualAmount2.add(noRateAmount);
                    untitled2 = untitled2 + newOrderItem.getProductName() + ",";
                }

                orders.setUntitled(untitled2.substring(0, untitled2.length() - 1));
                orders.setActualAmount(costPriceTotal2);
                if (mallConfig.isNotRateAmount == 1) {
                    orders.setNoRateAmount(noRateActualAmount2);
                } else {
                    orders.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(costPriceTotal2, taxRate2));
                }
                orders.setTotalAmount(BigDecimal.ZERO);
                orders.setCostPriceTotal(BigDecimal.ZERO);
                orders.setOrderClass(3);
                orders.setParentOrderId(order.getOrderId());
                orders.setOrderSourceType(5);
                orders.setOrderSourceId(orders.getParentOrderId());
                orders.setTaxRate(taxRate2);
                orders.setOrderSn(OrderUtils.getOrder());
                save(orders);
                // 保存好了订单项，
                for (OrderItem newOrderItem : newOrderItems) {
                    newOrderItem.setOrderId(orders.getOrderId());
                    newOrderItem.setOrderSn(orders.getOrderSn());
                    newOrderItem.setProductPrice(newOrderItem.getCostPrice());
                    newOrderItem.setTotalAmount(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
                    newOrderItem.setTaxRate(taxRate2);
                    newOrderItem.setNoRatePrice(TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate2));
//                    newOrderItem.setNoRateAmount(noRatePrice.multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
                    newOrderItem.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(newOrderItem.getTotalAmount(), taxRate2));
                    newOrderItem.setCostPrice(BigDecimal.ZERO);
                    newOrderItem.setCostAmount(BigDecimal.ZERO);
                    newOrderItem.setOriginalPrice(BigDecimal.ZERO);
                    newOrderItem.setParentOrderItemId(newOrderItem.getOrderItemId());
                    newOrderItem.setTransportPrice(newOrderItem.getTwoTransportPrice());
                    newOrderItem.setOutFactoryPrice(newOrderItem.getTwoOutFactoryPrice());
                    newOrderItem.setFixationPrice(newOrderItem.getTwoFixationPrice());
                    newOrderItem.setNetPrice(newOrderItem.getTwoNetPrice());

                    newOrderItem.setOrderItemId(null);
                }
                orderItemService.saveBatch(newOrderItems);
            });
        }
        //二级订单处理完成
        // 调用反写接口
        HashMap<Object, Object> subPlanDDTO = new HashMap<>();
        subPlanDDTO.put("data", retPlanList);
        subPlanDDTO.put("keyId", idStr);
        subPlanDDTO.put("orgId", user.getOrgId());

        String content = JSON.toJSONString(subPlanDDTO);
        farArg.append(content);
        log.warn("反写大宗临购计划接口请求参数：" + content);
        if (mallConfig.isCountPlanOrderNum == 0) {
            // 发送请求
            String url = mallConfig.prodPcwp2Url02 + "/thirdapi/bulkRetailPlan/updateBulkRetailPlanDtl";
            LogUtil.writeInfoLog(idStr, "submitSynthesizeTemporaryOrder", rDto, subPlanDDTO, null, OrdersServiceImpl.class);
            R rMap = null;
            try {
                rMap = restTemplateUtils.postPCWP2(url, subPlanDDTO);
            } catch (Exception e) {
                LogUtil.writeErrorLog(idStr, "submitSynthesizeTemporaryOrder", rDto, subPlanDDTO, null, e.getMessage(), OrdersServiceImpl.class);
                log.error(e.getMessage());
                throw new BusinessException("远程反写大宗临购计划服务异常：" + e.getMessage());
            }
            if (rMap.getCode() == null || rMap.getCode() != 200) {
                LogUtil.writeErrorLog(idStr, "submitSynthesizeTemporaryOrder", rDto, subPlanDDTO, rMap, rMap.getMessage(), OrdersServiceImpl.class);
                log.error("反写大宗临购计划接口错误！返回：" + rMap);
                throw new BusinessException("远程反写大宗临购计划服务异常：" + rMap);
            }
        }

        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(OrdersServiceImpl.class.getName());
        iLog.setMethodName("submitSynthesizeTemporaryOrder");
        iLog.setLocalArguments(JSON.toJSONString(rDto));
        iLog.setFarArguments(content);
        iLog.setIsSuccess(1);
        iLog.setLogType(1);
        iLog.setErrorInfo(null);
        interfaceLogsService.create(iLog);
        // 保存关联计划信息
        orderSelectPlanService.saveBatch(orderSelectPlans);
        // 修改库存
        productSkuService.updateBatchById(productSkus);
        // 下架商品
        if (!CollectionUtils.isEmpty(outProductId)) {
            UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
            updateProductStateDTO.setState(2);
            updateProductStateDTO.setProductIds(outProductId);
            productService.updateProductState(updateProductStateDTO);
        }


    }

    //@Override
    //public void batchCreateBidingOrder(GetSynthesizeTemporaryPlanDetailVO map, List<HitBidVo> hitBidVos) {
    //
    //}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreateNotBidingOrder(Orders order,
                                          SynthesizeTemporary synthesizeTemporary,
                                          EnterpriseInfo enterpriseInfo,
                                          Shop shopOne,
                                          ArrayList<ProductSku> productSkus,
                                          ArrayList<OrderSelectPlan> orderSelectPlans,
                                          ArrayList<Map<String, Object>> retPlanList,
                                          List<String> outProductId,
                                          ArrayList<OrderItem> orderItems,
                                          List<GetSynthesizeTemporaryPlanDetailItemVO> details,
                                          GetSynthesizeTemporaryPlanDetailVO rDto,
                                          BigDecimal taxRate
    ) {
        Integer shopClass = shopOne.getShopClass();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        for (GetSynthesizeTemporaryPlanDetailItemVO dto : details) {
            ProductSku productSku = productSkuService.getProductSkuByProductId(dto.getProductId(), null).get(0);
            OrderItem orderItem = new OrderItem();
            orderItem.setOrderId(order.getOrderId());
            orderItem.setOrderSn(order.getOrderSn());
            orderItem.setUnit(dto.getUnit());
            orderItem.setProductId(dto.getProductId());
            orderItem.setProductSn(dto.getProductSn());
            orderItem.setProductName(dto.getProductName().trim());
            orderItem.setRelevanceName(dto.getMaterialName().trim());
            orderItem.setRelevanceNo(dto.getMaterialSn());
            orderItem.setRelevanceId(dto.getMaterialId());

            Product byId = productService.getById(dto.getProductId());
            orderItem.setProductImg(byId.getProductMinImg());
            if (productSku != null) {
                orderItem.setSkuId(productSku.getSkuId());
            }

            if (dto.getSpec() != null) {
                orderItem.setSkuName(dto.getSpec().trim());

            }

            orderItem.setCostPrice(dto.getCostPrice());
            orderItem.setCostAmount(dto.getCostPrice().multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
            orderItem.setOriginalPrice(BigDecimal.ZERO);
            orderItem.setProductPrice(dto.getSynthesizePrice());
            orderItem.setTaxRate(enterpriseInfo.getTaxRate());
            BigDecimal noRatePrice = null;
            if (synthesizeTemporary.getBillType() == 1) {
                BigDecimal pr1 = TaxCalculator.calculateNotTarRateAmount(dto.getNetPrice(), taxRate);
                BigDecimal pr2 = TaxCalculator.calculateNotTarRateAmount(dto.getFixationPrice(), taxRate);
                noRatePrice = pr1.add(pr2);
            }
            if (synthesizeTemporary.getBillType() == 2) {
                BigDecimal pr1 = TaxCalculator.calculateNotTarRateAmount(dto.getOutFactoryPrice(), taxRate);
                BigDecimal pr2 = TaxCalculator.calculateNotTarRateAmount(dto.getTransportPrice(), taxRate);
                noRatePrice = pr1.add(pr2);
            }
            orderItem.setNoRatePrice(noRatePrice);
            orderItem.setTotalAmount(dto.getSelectQty().multiply(dto.getSynthesizePrice()).setScale(2, RoundingMode.HALF_UP));
//            orderItem.setNoRateAmount(noRatePrice.multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
            orderItem.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(orderItem.getTotalAmount(), taxRate));

            orderItem.setBuyCounts(dto.getSelectQty());
            orderItem.setProductType(13);
            if (shopOne.getShopClass() == 2) {
                orderItem.setState(1);
            } else {
                orderItem.setState(0);
            }
            orderItem.setTotalAmount(dto.getSynthesizePrice().multiply(dto.getSelectQty()));
            orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
            orderItem.setBuyTime(new Date());
            orderItem.setProductType(13);
            orderItem.setSupplierId(dto.getSupplierOrgId());
            orderItem.setSupplierName(dto.getSupplierName());
            orderItem.setCostPrice(dto.getCostPrice());
            orderItem.setCostAmount(dto.getCostPrice().multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
            orderItem.setClassPathName(dto.getClassNamePath());
            orderItem.setClassPathId(dto.getClassIdPath());
            orderItem.setClassId(dto.getClassId());
            orderItem.setBrandId(dto.getBrandId());
            orderItem.setTexture(dto.getTexture() == null ? null : dto.getTexture().trim());
            orderItem.setBrandName(dto.getBrandName());
            orderItem.setNetPrice(dto.getNetPrice());
            // 价格
            orderItem.setFixationPrice(dto.getFixationPrice());
            orderItem.setOutFactoryPrice(dto.getOutFactoryPrice());
            orderItem.setTransportPrice(dto.getTransportPrice());
            //
            orderItem.setProfitPrice(dto.getSynthesizePrice().subtract(dto.getCostPrice()));

            // 副单位
            orderItem.setIsTwoUnit(dto.getIsTwoUnit());
            orderItem.setTwoUnit(dto.getTwoUnit());
            orderItem.setTwoUnitNum(dto.getTwoUnitNum());
            orderItem.setSecondUnitNum(dto.getSecondUnitNum());
            //竞价没有成功的，二级订单没有出厂价格，只有商品价格
            orderItem.setTwoFixationPrice(BigDecimal.ZERO);
            orderItem.setTwoNetPrice(BigDecimal.ZERO);
            orderItem.setTwoOutFactoryPrice(BigDecimal.ZERO);
            orderItem.setTwoTransportPrice(BigDecimal.ZERO);

            orderItemService.save(orderItem);
            // 拆单所需数据
            if (shopClass == 2) {
                orderItems.add(orderItem);
            }

            // 扣减库存
            String skuId = productSku.getSkuId();
            BigDecimal newStock = productSku.getStock().subtract(dto.getSelectQty());
            ProductSku productSku2 = new ProductSku();
            productSku2.setSkuId(skuId);
            productSku2.setStock(newStock);
            productSkus.add(productSku2);
            // 判断库存是否等于0如果等于0则下架商品
            if (newStock.compareTo(BigDecimal.ZERO) == 0) {
                outProductId.add(dto.getProductId());
            }

            // 保存计划订单
            OrderSelectPlan orderSelectPlan = new OrderSelectPlan();
            orderSelectPlan.setOrderId(order.getOrderId());
            orderSelectPlan.setOrderSn(order.getOrderSn());
            orderSelectPlan.setOrderItemId(orderItem.getOrderItemId());
            orderSelectPlan.setDtlId(dto.getDtlId());
            orderSelectPlan.setEquipmentName(dto.getProductName());
            orderSelectPlan.setCount(dto.getSelectQty());
            orderSelectPlan.setSourcePlanQty(dto.getQty()); // 没意义
            orderSelectPlan.setBillId(rDto.getBillId());
            orderSelectPlan.setBillNo(rDto.getBillNo());
            orderSelectPlan.setProductType(13);
            orderSelectPlan.setOrgId(user.getOrgId());
            orderSelectPlan.setBillType(synthesizeTemporary.getBillType());
            orderSelectPlan.setOrgName(user.getEnterpriseName());
            orderSelectPlan.setPrice(dto.getSynthesizePrice());
            orderSelectPlan.setAccount(dto.getSynthesizePrice().multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
            orderSelectPlan.setSynthesizeTemporaryDtlId(dto.getSynthesizeTemporaryDtlId());
            // TODO 都赋值为供应商内部机构id
            orderSelectPlan.setStorageId(rDto.getSupplierOrgFarId());
            orderSelectPlan.setStorageOrgId(rDto.getSupplierOrgFarId());


            orderSelectPlan.setStorageName(rDto.getSupplierOrgName());
            orderSelectPlan.setShortCode(rDto.getOrgShort());
            orderSelectPlan.setCreditCode(rDto.getCreditCode());
            orderSelectPlan.setPlanType(3);
            orderSelectPlan.setTaxRate(order.getTaxRate());
            // 如果信用代码为空表示是内部供应商
            if (StringUtils.isEmpty(rDto.getCreditCode())) {
                orderSelectPlan.setSupplierType(2);
            } else {
                orderSelectPlan.setSupplierType(1);
            }
            orderSelectPlans.add(orderSelectPlan);

            // 组装反写计划
            HashMap<String, Object> onePlanMap = new HashMap<>();
            onePlanMap.put("dtlId", dto.getDtlId());
            onePlanMap.put("billId", dto.getBillId());
            onePlanMap.put("amount", orderItem.getNoRateAmount());
            onePlanMap.put("number", dto.getSelectQty());
            retPlanList.add(onePlanMap);
        }


    }

    @Override
    public void batchCreateBidingOrder(Orders order, SynthesizeTemporary synthesizeTemporary, EnterpriseInfo enterpriseInfo, Shop shopOne, ArrayList<ProductSku> productSkus, ArrayList<OrderSelectPlan> orderSelectPlans, ArrayList<Map<String, Object>> retPlanList, List<String> outProductId, ArrayList<OrderItem> orderItems, List<GetSynthesizeTemporaryPlanDetailItemVO> details, GetSynthesizeTemporaryPlanDetailVO rDto, BigDecimal taxRate, List<HitBidVo> hitBidVos) {
        Integer shopClass = shopOne.getShopClass();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        for (GetSynthesizeTemporaryPlanDetailItemVO dto : details) {
            for (HitBidVo bidVo : hitBidVos) {
                if (dto.getProductSn().equals(bidVo.getProductSn())) {
                    BigDecimal bidPrice = bidVo.getBidPrice();
                    BigDecimal bidRatePrice = bidVo.getBidRatePrice();
                    ProductSku productSku = productSkuService.getProductSkuByProductId(dto.getProductId(), null).get(0);
                    OrderItem orderItem = new OrderItem();
                    orderItem.setOrderId(order.getOrderId());
                    orderItem.setOrderSn(order.getOrderSn());
                    orderItem.setUnit(dto.getUnit());
                    orderItem.setProductId(dto.getProductId());
                    orderItem.setProductSn(dto.getProductSn());
                    orderItem.setProductName(dto.getProductName().trim());
                    orderItem.setRelevanceName(dto.getMaterialName().trim());
                    orderItem.setRelevanceNo(dto.getMaterialSn());
                    orderItem.setRelevanceId(dto.getMaterialId());

                    Product byId = productService.getById(dto.getProductId());
                    orderItem.setProductImg(byId.getProductMinImg());
                    if (productSku != null) {
                        orderItem.setSkuId(productSku.getSkuId());
                    }
                    if (dto.getSpec() != null) {
                        orderItem.setSkuName(dto.getSpec().trim());
                    }
                    // 成本价是竞价含税单价
                    orderItem.setCostPrice(bidRatePrice);
                    orderItem.setCostAmount(bidRatePrice.multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
                    orderItem.setOriginalPrice(BigDecimal.ZERO);
                    orderItem.setProductPrice(dto.getSynthesizePrice());
                    orderItem.setTaxRate(bidVo.getTaxRate());
                    // 不含税单价
                    //BigDecimal noRatePrice = null;
                    if (synthesizeTemporary.getBillType() == 1) {
                        //BigDecimal pr1 = TaxCalculator.calculateNotTarRateAmount(bidVo.getNetPrice(), bidVo.getTaxRate());
                        //BigDecimal pr2 = TaxCalculator.calculateNotTarRateAmount(bidVo.getFixationPrice(), bidVo.getTaxRate());
                        //noRatePrice = pr1.add(pr2);
                    }
                    if (synthesizeTemporary.getBillType() == 2) {
                        // 报价的时候出场价和运杂费可能不存在这两个值
                        //BigDecimal pr1 = TaxCalculator.calculateNotTarRateAmount(bidVo.getOutFactoryPrice(), bidVo.getTaxRate());
                        //BigDecimal pr2 = TaxCalculator.calculateNotTarRateAmount(bidVo.getTransportPrice(), bidVo.getTaxRate());
                        //noRatePrice = pr1.add(pr2);
                    }
                    //noRatePrice = TaxCalculator.calculateNotTarRateAmount(bidRatePrice,taxRate);
                    orderItem.setNoRatePrice(bidPrice);
                    orderItem.setTotalAmount(dto.getSelectQty().multiply(dto.getSynthesizePrice()).setScale(2, RoundingMode.HALF_UP));
//            orderItem.setNoRateAmount(noRatePrice.multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
                    orderItem.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(orderItem.getTotalAmount(), taxRate));

                    orderItem.setBuyCounts(dto.getSelectQty());
                    orderItem.setProductType(13);
                    if (shopOne.getShopClass() == 2) {
                        orderItem.setState(1);
                    } else {
                        orderItem.setState(0);
                    }
                    orderItem.setTotalAmount(dto.getSynthesizePrice().multiply(dto.getSelectQty()));
                    orderItem.setIsComment(OrderEnum.ORDER_ITEM_NOT_COMMENT.getCode());
                    orderItem.setBuyTime(new Date());
                    orderItem.setProductType(13);
                    // 供应商使用竞价供应商  TODO
                    orderItem.setSupplierId(bidVo.getSupplierId());
                    orderItem.setSupplierName(bidVo.getSupplierName());
                    orderItem.setCostPrice(bidRatePrice);
                    orderItem.setCostAmount(bidRatePrice.multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
                    orderItem.setClassPathName(dto.getClassNamePath());
                    orderItem.setClassPathId(dto.getClassIdPath());
                    orderItem.setClassId(dto.getClassId());
                    orderItem.setBrandId(dto.getBrandId());
                    orderItem.setTexture(dto.getTexture() == null ? null : dto.getTexture().trim());
                    orderItem.setBrandName(dto.getBrandName());
                    // 价格(一级订单，使用清单的价格)
                    orderItem.setNetPrice(dto.getNetPrice());
                    orderItem.setFixationPrice(dto.getFixationPrice());
                    orderItem.setOutFactoryPrice(dto.getOutFactoryPrice());
                    orderItem.setTransportPrice(dto.getTransportPrice());
                    //竞价的价格是二级订单的价格，携带到外面。外面保存近二级订单
                    orderItem.setTwoFixationPrice(bidVo.getFixationPrice());
                    orderItem.setTwoNetPrice(bidVo.getNetPrice());
                    orderItem.setTwoOutFactoryPrice(bidVo.getOutFactoryPrice());
                    orderItem.setTwoTransportPrice(bidVo.getTransportPrice());
                    //
                    orderItem.setProfitPrice(dto.getSynthesizePrice().subtract(bidPrice));

                    // 副单位
                    orderItem.setIsTwoUnit(dto.getIsTwoUnit());
                    orderItem.setTwoUnit(dto.getTwoUnit());
                    orderItem.setTwoUnitNum(dto.getTwoUnitNum());
                    orderItem.setSecondUnitNum(dto.getSecondUnitNum());

                    orderItemService.save(orderItem);
                    // 拆单所需数据
                    if (shopClass == 2) {
                        orderItems.add(orderItem);
                    }

                    // 扣减库存
                    String skuId = productSku.getSkuId();
                    BigDecimal newStock = productSku.getStock().subtract(dto.getSelectQty());
                    ProductSku productSku2 = new ProductSku();
                    productSku2.setSkuId(skuId);
                    productSku2.setStock(newStock);
                    productSkus.add(productSku2);
                    // 判断库存是否等于0如果等于0则下架商品
                    if (newStock.compareTo(BigDecimal.ZERO) == 0) {
                        outProductId.add(dto.getProductId());
                    }

                    // 保存计划订单
                    OrderSelectPlan orderSelectPlan = new OrderSelectPlan();
                    orderSelectPlan.setOrderId(order.getOrderId());
                    orderSelectPlan.setOrderSn(order.getOrderSn());
                    orderSelectPlan.setOrderItemId(orderItem.getOrderItemId());
                    orderSelectPlan.setDtlId(dto.getDtlId());
                    orderSelectPlan.setEquipmentName(dto.getProductName());
                    orderSelectPlan.setCount(dto.getSelectQty());
                    orderSelectPlan.setSourcePlanQty(dto.getQty()); // 没意义
                    orderSelectPlan.setBillId(rDto.getBillId());
                    orderSelectPlan.setBillNo(rDto.getBillNo());
                    orderSelectPlan.setProductType(13);
                    orderSelectPlan.setOrgId(user.getOrgId());
                    orderSelectPlan.setBillType(synthesizeTemporary.getBillType());
                    orderSelectPlan.setOrgName(user.getEnterpriseName());
                    orderSelectPlan.setPrice(dto.getSynthesizePrice());
                    orderSelectPlan.setAccount(dto.getSynthesizePrice().multiply(dto.getSelectQty()).setScale(2, RoundingMode.HALF_UP));
                    orderSelectPlan.setSynthesizeTemporaryDtlId(dto.getSynthesizeTemporaryDtlId());
                    // TODO 都赋值为供应商内部机构id 竞价查询对应内部
                    orderSelectPlan.setStorageId(rDto.getSupplierOrgFarId());
                    orderSelectPlan.setStorageOrgId(rDto.getSupplierOrgFarId());


                    orderSelectPlan.setStorageName(rDto.getSupplierOrgName());
                    orderSelectPlan.setShortCode(rDto.getOrgShort());
                    orderSelectPlan.setCreditCode(rDto.getCreditCode());
                    orderSelectPlan.setPlanType(3);
                    orderSelectPlan.setTaxRate(order.getTaxRate());
                    // 如果信用代码为空表示是内部供应商
                    if (StringUtils.isEmpty(rDto.getCreditCode())) {
                        orderSelectPlan.setSupplierType(2);
                    } else {
                        orderSelectPlan.setSupplierType(1);
                    }
                    orderSelectPlans.add(orderSelectPlan);

                    // 组装反写计划
                    HashMap<String, Object> onePlanMap = new HashMap<>();
                    onePlanMap.put("dtlId", dto.getDtlId());
                    onePlanMap.put("billId", dto.getBillId());
                    onePlanMap.put("amount", orderItem.getNoRateAmount());
                    onePlanMap.put("number", dto.getSelectQty());
                    retPlanList.add(onePlanMap);
                }
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void batchCreateLinXingTwoOrder(BatchCreateTwoOrderDTO dto) {
        List<String> orderItemId = dto.getOrderItemId();
        String supplierId = dto.getSupplierId();
        String supplierName = dto.getSupplierName();
        List<OrderItem> orderItems = orderItemService.lambdaQuery().in(OrderItem::getOrderItemId, orderItemId).list();
        if (CollectionUtils.isEmpty(orderItems)) {
            throw new BusinessException("订单不存在！");
        }
        for (OrderItem orderItem : orderItems) {
            if (orderItem.getBuyCounts().compareTo(orderItem.getReturnCounts()) == 0) {
                throw new BusinessException("物资【" + orderItem.getProductName() + "】已全部完成退货不能生成二级订单！");
            }
        }
        String orderId = orderItems.get(0).getOrderId();
        Orders orders = getById(orderId);
        // 保存订单、计算金额
        Orders newOrders = new Orders();
        BeanUtils.copyProperties(orders, newOrders);
        newOrders.setOrderId(null);
        newOrders.setUntitled(null);
        newOrders.setOrderSn(OrderUtils.getOrder());
        newOrders.setOrderClass(3);
        BigDecimal costPriceTotal2 = new BigDecimal(0);
        EnterpriseInfo en = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, supplierId)
                .select(EnterpriseInfo::getTaxRate, EnterpriseInfo::getEnterpriseId).one();
        if (en == null) {
            throw new BusinessException("供应商不存在！");
        }
        BigDecimal taxRate = en.getTaxRate();
        if (taxRate == null) {
            throw new BusinessException("供应商未设置税率！");
        }
        BigDecimal noRatecostPriceTotal2 = new BigDecimal(0);
        String untitled2 = "";
        for (OrderItem newOrderItem : orderItems) {
            OrderItem parentId = orderItemService.getOrderItemByParentId(newOrderItem.getOrderItemId());
            if (parentId != null) {
                throw new BusinessException("该订单项已经选择选择供应商，不能在选择");
            }
            costPriceTotal2 = costPriceTotal2.add(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
//            BigDecimal noTatePrice = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate);
//            noRatecostPriceTotal2 = noRatecostPriceTotal2.add(noTatePrice.multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));

            BigDecimal noRateAmount = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP), taxRate);
            noRatecostPriceTotal2 = noRatecostPriceTotal2.add(noRateAmount);
            untitled2 = untitled2 + newOrderItem.getProductName() + ",";
        }

        newOrders.setUntitled(untitled2.substring(0, untitled2.length() - 1));
        newOrders.setActualAmount(costPriceTotal2);
        if (mallConfig.isNotRateAmount == 1) {
            newOrders.setNoRateAmount(noRatecostPriceTotal2);
        } else {
            newOrders.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(costPriceTotal2, taxRate));
        }
        newOrders.setTotalAmount(BigDecimal.ZERO);
        newOrders.setCostPriceTotal(BigDecimal.ZERO);

        newOrders.setParentOrderId(orders.getOrderId());
        newOrders.setOrderSourceId(orders.getParentOrderId());

        newOrders.setSupplierId(supplierId);
        newOrders.setSupplierName(supplierName);
        newOrders.setTaxRate(en.getTaxRate());

        save(newOrders);
        // 保存好了订单项，
        for (OrderItem newOrderItem : orderItems) {
            newOrderItem.setOrderId(newOrders.getOrderId());
            newOrderItem.setOrderSn(newOrders.getOrderSn());
            newOrderItem.setState(1);
            newOrderItem.setProductPrice(newOrderItem.getCostPrice());
            newOrderItem.setNoRatePrice(TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate));
            newOrderItem.setTaxRate(taxRate);
            BigDecimal amount = newOrderItem.getCostPrice().multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP);
            newOrderItem.setTotalAmount(amount);
            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(newOrderItem.getCostPrice(), taxRate);
//            newOrderItem.setNoRateAmount(noRatePrice.multiply(newOrderItem.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));
            newOrderItem.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(amount, taxRate));

            newOrderItem.setCostPrice(BigDecimal.ZERO);
            newOrderItem.setCostAmount(BigDecimal.ZERO);
            newOrderItem.setOriginalPrice(BigDecimal.ZERO);
            // 保存父明细id
            newOrderItem.setParentOrderItemId(newOrderItem.getOrderItemId());
            newOrderItem.setOrderItemId(null);
        }
        orderItemService.saveBatch(orderItems);

        // 修改主订单明细状态
        orderItemService.lambdaUpdate().in(OrderItem::getOrderItemId, orderItemId)
                .set(OrderItem::getState, 1)
                .set(OrderItem::getGmtModified, new Date()).update();


    }

    /**
     * 删除大宗月供订单
     *
     * @param orderId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteYGOrderByOrderId(String orderId) {
        Orders orders = getById(orderId);
        if (orders.getState() != 6) {
            throw new BusinessException("订单不能删除！");
        }
        Integer count = orderShipService.lambdaQuery().eq(OrderShip::getOrderId, orderId).count();
        if (count > 0) {
            throw new BusinessException("该订单已经生成发货单不能删除操作！");
        }
        delete(orderId);
        List<OrderItem> list = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, orderId).list();
        for (OrderItem orderItem : list) {
            OrderSelectPlan orderSelectPlan = orderSelectPlanService.lambdaQuery().eq(OrderSelectPlan::getOrderItemId, orderItem.getOrderItemId()).one();
            MaterialMonthSupplyPlanDtl dtlServiceById = materialMonthSupplyPlanDtlService.getById(orderSelectPlan.getDtlId());
            dtlServiceById.setOrderQty(dtlServiceById.getOrderQty().subtract(orderItem.getBuyCounts())); // 没用，数量使用的最细统计的
            materialMonthSupplyPlanDtlService.update(dtlServiceById);

            orderItemService.delete(orderItem.getOrderItemId());
            orderItemService.lambdaUpdate().eq(OrderItem::getParentOrderItemId, orderItem.getOrderItemId()).remove();
        }
        lambdaUpdate().eq(Orders::getParentOrderId, orderId).remove();
        orderSelectPlanService.lambdaUpdate().eq(OrderSelectPlan::getOrderId, orderId).remove();

    }

    /**
     * 删除大宗临购
     *
     * @param orderId
     * @param stringBuilder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteLGOrderByOrderId(String orderId, String idStr, StringBuilder stringBuilder) {
        Orders orders = getById(orderId);
        if (orders.getState() != 6) {
            throw new BusinessException("订单不能删除！");
        }
        Integer count = orderShipService.lambdaQuery().eq(OrderShip::getOrderId, orderId).count();
        if (count > 0) {
            throw new BusinessException("该订单已经生成发货单不能删除操作！");
        }
        delete(orderId);
//        List<OrderItem> list = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, orderId).list();
//
//
//        ArrayList<Map<String, Object>> retPlanList = new ArrayList<>();
//        for (OrderItem orderItem : list) {
//            OrderSelectPlan orderSelectPlan = orderSelectPlanService.lambdaQuery().eq(OrderSelectPlan::getOrderItemId, orderItem.getOrderItemId()).one();
//            // 组装反写计划
//            HashMap<String, Object> onePlanMap = new HashMap<>();
//            onePlanMap.put("dtlId", orderSelectPlan.getDtlId());
//            onePlanMap.put("billId", orderSelectPlan.getBillId());
//            onePlanMap.put("amount", orderItem.getNoRateAmount().negate());
//            onePlanMap.put("number", orderSelectPlan.getCount().negate());
//            retPlanList.add(onePlanMap);
//        }
        orderSelectPlanService.lambdaUpdate().eq(OrderSelectPlan::getOrderId, orderId).remove();
        List<OrderItem> list = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, orderId)
                .select(OrderItem::getOrderItemId).list();
        for (OrderItem orderItem : list) {
            orderItemService.delete(orderItem.getOrderItemId());
            orderItemService.lambdaUpdate().eq(OrderItem::getParentOrderItemId, orderItem.getOrderItemId()).remove();
        }
        lambdaUpdate().eq(Orders::getParentOrderId, orderId).remove();


//        UserLogin user = ThreadLocalUtil.getCurrentUser();
//
//        // 调用反写接口
//        HashMap<Object, Object> subPlanDDTO = new HashMap<>();
//        subPlanDDTO.put("data", retPlanList);
//        subPlanDDTO.put("keyId", idStr);
//        subPlanDDTO.put("orgId", user.getOrgId());
//
//        String content = JSON.toJSONString(subPlanDDTO);
//        stringBuilder.append(content);
//        log.warn("反写大宗临购计划接口请求参数：" + content);
        // 发送请求
//        String url = mallConfig.prodPcwp2Url02 + "/thirdapi/bulkRetailPlan/updateBulkRetailPlanDtl";
//        LogUtil.writeInfoLog(idStr, "deleteLGOrderByOrderId", orderId, subPlanDDTO, null, OrdersServiceImpl.class);
//        R rMap = null;
//        try {
//            rMap = restTemplateUtils.postPCWP2(url, subPlanDDTO);
//        } catch (Exception e) {
//            LogUtil.writeErrorLog(idStr, "deleteLGOrderByOrderId", orderId, subPlanDDTO, null, e.getMessage(), OrdersServiceImpl.class);
//            log.error(e.getMessage());
//            throw new BusinessException("远程反写大宗临购计划服务异常：" + e.getMessage());
//        }
//        if (rMap.getCode() == null || rMap.getCode() != 200) {
//            LogUtil.writeErrorLog(idStr, "deleteLGOrderByOrderId", orderId, subPlanDDTO, rMap, rMap.getMessage(), OrdersServiceImpl.class);
//            log.error("反写大宗临购计划接口错误！返回：" + rMap);
//            throw new BusinessException("远程反写大宗临购计划服务异常：" + rMap);
//        }

    }

    /**
     * 订单总量：订单总量减商城退货总量
     * 发货/收货总量：如果没有收货数量：发货量减去PCWP退货量（如果有收货数量：收货数量减去PCWP退货量）
     *
     * @param orderItemIds
     * @return
     */
    @Override
    public BigDecimal getOrderUseCount(List<String> orderItemIds) {
//        BigDecimal p1 = orderReturnItemService.getDataByIdAndIsReconciliation(orderItemIds);
//        BigDecimal p2 = orderShipDtlService.getShipCounts(orderItemIds);
//        if (p1 == null) {
//            p1 = new BigDecimal(0);
//        }
//        if (p2 == null) {
//            p2 = new BigDecimal(0);
//        }
//        if (p1.compareTo(p2) == 0) {
//            return p1;
//        } else if (p1.compareTo(p2) == 1) {
//            return p1;
//        } else {
//            return p2;
//        }
//        List<OrderShipmentsQtyIsOkDTO> dtos =  new ArrayList<>();
//        for (String orderItemId : orderItemIds) {
//            OrderShipmentsQtyIsOkDTO orderShipmentsQtyIsOkDTO = new OrderShipmentsQtyIsOkDTO();
//            orderShipmentsQtyIsOkDTO.setOrderItemId(orderItemId);
//            orderShipmentsQtyIsOkDTO.setQty(new BigDecimal(0));
//            dtos.add(orderShipmentsQtyIsOkDTO);
//        }
        BigDecimal p2 = orderShipDtlService.getShipCounts2(orderItemIds);
        return p2;
    }

    /**
     * 根据订单关联计划明细id获取对应的最大下单的数量
     *
     * @param selectPlanDtlIds
     * @return
     */
    @Override
    public BigDecimal getOrderUseCountBySelectPlanDtlIds(List<String> selectPlanDtlIds) {
        if (CollectionUtils.isEmpty(selectPlanDtlIds)) {
            throw new BusinessException("未携带计划明细id！");
        }
        List<OrderSelectPlan> list = orderSelectPlanService.lambdaQuery().in(OrderSelectPlan::getDtlId, selectPlanDtlIds).list();
        if (CollectionUtils.isEmpty(list)) {
            return new BigDecimal(0);
        }
        List<String> orderItemIds = list.stream().map(t -> t.getOrderItemId()).collect(Collectors.toList());
        return getOrderUseCount(orderItemIds);
    }

    /**
     * 根据订单关联计划明细id获取对应的最大下单的数量
     *
     * @param selectPlanDtlId
     * @return
     */
    @Override
    public BigDecimal getOrderUseCountBySelectPlanDtlIds(String selectPlanDtlId) {
        if (StringUtils.isEmpty(selectPlanDtlId)) {
            throw new BusinessException("未携带计划明细id！");
        }
        List<OrderSelectPlan> list = orderSelectPlanService.lambdaQuery().eq(OrderSelectPlan::getDtlId, selectPlanDtlId).list();
        if (CollectionUtils.isEmpty(list)) {
            return new BigDecimal(0);
        }
        List<String> orderItemIds = list.stream().map(t -> t.getOrderItemId()).collect(Collectors.toList());
        return getOrderUseCount(orderItemIds);

    }

    /**
     * 修改竞价
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBidingByOrder(BiddingPurchase dto) {

        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        switch (dto.getPageType()){
            case 1:
                updateBidingPurchase(currentUser,dto);
                break;
            case 2:
                updateBidingRecord(currentUser,dto);
                break;
                //撤回
            case 3:
                break;
                //作废
            case 4:
                break;
        }


    }

    @Autowired
    BiddingBidRecordService biddingBidRecordService;

    @Autowired
    ProcessConfigService processConfigService;

    private void updateBidingRecord(UserLogin currentUser,BiddingPurchase dto) {
        String biddingExplain = dto.getBiddingExplain();
        if (biddingExplain != null) {
            if (biddingExplain.length() > 2500) {
                throw new BusinessException("竞价说明内容过大！");
            }
        }
        BiddingBidRecord biddingBidRecord = new BiddingBidRecord();

        biddingBidRecord.setBidRecordId(dto.getRecordId());
        biddingBidRecord.setRemarks(biddingExplain);

        biddingBidRecordService.update(biddingBidRecord);
        if (!CollectionUtils.isEmpty(dto.getDtos())) {
            for (BatchUpdateBiddingItemInfoDTO d : dto.getDtos()) {
                Integer count = biddingPurchaseService.lambdaQuery().eq(BiddingPurchase::getBiddingId, d.getBiddingId())
                        .in(BiddingPurchase::getState, 0, 2).count();
                if (count == 0) {
                    continue;
                }
                BiddingProduct biddingProduct = new BiddingProduct();
                BeanUtils.copyProperties(d, biddingProduct);
                if (dto.getBillType() == 1 && dto.getBiddingSourceType() == 3) {
                    if (d.getNetPrice() == null || d.getNetPrice().compareTo(new BigDecimal(0)) < 0) {
                        throw new BusinessException("网价设置错误");
                    }
                }
                biddingProductService.update(biddingProduct);
            }
        }
        Integer isSubmit = dto.getIsSubmit();
        if (isSubmit != null && isSubmit == 1) {
            boolean bb = ThreadLocalUtil.getCurrentUser().getRoles().contains("物资竞价提交权限");
            if (!bb) {
                throw new BusinessException("没有权限请联系管理员！");
            }
            biddingBidRecordService.lambdaUpdate().eq(BiddingBidRecord::getBidRecordId, dto.getRecordId())
                    .set(BiddingBidRecord::getState, 1).update();

            //更新工作流
            if (dto.getAuditProcessType() == 1){
                processConfigService.myFunc(ProcessConstants.BIDDING_PROCESS_ID_FIRST,
                        currentUser, 1, dto.getBiddingId(), "【同意】");
            }else {
                processConfigService.myFunc(ProcessConstants.BIDDING_PROCESS_ID_NO_FIRST,
                        currentUser, 1, dto.getBiddingId(), "【同意】");
            }

        }

    }

    private void updateBidingPurchase(UserLogin currentUser,BiddingPurchase dto) {
        String biddingExplain = dto.getBiddingExplain();
        if (biddingExplain != null) {
            if (biddingExplain.length() > 2500) {
                throw new BusinessException("竞价说明内容过大！");
            }
        }
        biddingPurchaseService.update(dto);
        if (!CollectionUtils.isEmpty(dto.getDtos())) {
            for (BatchUpdateBiddingItemInfoDTO dtl : dto.getDtos()) {
                Integer count = biddingPurchaseService.lambdaQuery().eq(BiddingPurchase::getBiddingId, dtl.getBiddingId())
                        .in(BiddingPurchase::getState, 0, 2).count();
                if (count == 0) {
                    continue;
                }
                BiddingProduct biddingProduct = new BiddingProduct();
                BeanUtils.copyProperties(dtl, biddingProduct);
                if (dto.getBiddingSourceType() == 1) {
                    dto.setBillType(2);
                }
                if (dto.getBiddingSourceType() == 3 && dto.getBillType() == 1) {
                    if (dtl.getNetPrice() == null || dtl.getNetPrice().compareTo(new BigDecimal(0)) < 0) {
                        throw new BusinessException("网价设置错误");
                    }
                }
                biddingProductService.update(biddingProduct);
            }
        }
        Integer isSubmit = dto.getIsSubmit();
        if (isSubmit != null && isSubmit == 1) {
            boolean bb = ThreadLocalUtil.getCurrentUser().getRoles().contains("物资竞价提交权限");
            if (bb == false) {
                throw new BusinessException("没有权限请联系管理员！");
            }
            biddingPurchaseService.lambdaUpdate().eq(BiddingPurchase::getBiddingId, dto.getBiddingId())
                    .set(BiddingPurchase::getState, 1).update();

            //更新工作流
            processConfigService.myFunc(ProcessConstants.PUBLISH_BIDDING_PROCESS_ID,
                    currentUser, 1, dto.getBiddingSn(), "【同意】");
        }
    }

    /**
     * 根据可对账物资获取订单含税单价
     *
     * @param maps
     * @return
     */
    @Override
    public List<Map> getOrderItemPriceByMas(List<Map> maps) {
        for (Map map : maps) {
            String orderSn = (String) map.get("orderSn");
            String unit = (String) map.get("unit");
            String materialName = (String) map.get("materialName");
            String orderDtlId = (String) map.get("orderDtlId");
            String spec = (String) map.get("spec");
            String texture = (String) map.get("texture");
            OrderItem one = null;
            try {
                one = orderItemService.lambdaQuery()
                        .eq(OrderItem::getOrderSn, orderSn)
                        .eq(unit != null && unit != "", OrderItem::getUnit, unit)
                        .eq(OrderItem::getRelevanceName, materialName)
                        .eq(spec != null && spec != "", OrderItem::getSkuName, spec)
                        .eq(orderDtlId != null && orderDtlId != "", OrderItem::getOrderItemId, orderDtlId)
                        .eq(OrderItem::getProductName, texture) // pcwp商城存储的材质
                        .one();
            } catch (Exception e) {
                Integer qu = ((Integer) map.get("quantity"));
                List<OrderItem> itemList = orderItemService.lambdaQuery()
                        .eq(OrderItem::getOrderSn, orderSn)
                        .eq(unit != null && unit != "", OrderItem::getUnit, unit)
                        .eq(OrderItem::getRelevanceName, materialName)
                        .eq(spec != null && spec != "", OrderItem::getSkuName, spec)
                        .eq(OrderItem::getProductName, texture) // pcwp商城存储的材质
                        .eq(OrderItem::getBuyCounts, qu)
                        .list();
                one = itemList.get(0);
            }
            if (one == null) {
                try {
                    one = orderItemService.lambdaQuery()
                            .eq(OrderItem::getOrderSn, orderSn)
                            .like(OrderItem::getUnit, unit)
                            .like(OrderItem::getRelevanceName, materialName)
                            .eq(OrderItem::getSkuName, spec)
                            .like(OrderItem::getProductName, texture) // pcwp商城存储的材质
                            .one();
                } catch (Exception e) {
                    one = orderItemService.lambdaQuery()
                            .eq(OrderItem::getOrderSn, orderSn)
                            .like(OrderItem::getUnit, unit)
                            .like(OrderItem::getRelevanceName, materialName)
                            .like(OrderItem::getSkuName, spec)
                            .like(OrderItem::getProductName, texture) // pcwp商城存储的材质
                            .one();
                }
                if (one == null) {
                    log.error("未查询到订单明细数据：" + JSON.toJSONString(map));
                    throw new BusinessException("未查询到订单明细数据！");
                }
            }
            map.put("orderItemId", one.getOrderItemId());
            map.put("price", one.getProductPrice());
            map.put("tradeId", one.getProductId());
            map.put("taxRate", one.getTaxRate());
        }
        return maps;
    }

    /**
     * 查询可以选择的竞价采购订单列表
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listBidingOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> q) {
        Integer productType = (Integer) jsonObject.get("productType");
        Integer billType = (Integer) jsonObject.get("billType");
        String orderSn = (String) jsonObject.get("orderSn");
        q.isNull(Orders::getParentOrderId)
                .eq(Orders::getShopId, ThreadLocalUtil.getCurrentUser().getShopId())
                .eq(Orders::getOrderClass, 2)
                .eq(billType != null, Orders::getBillType, billType)
                .eq(StringUtils.isNotBlank(orderSn), Orders::getOrderSn, orderSn)
                .eq(productType == null, Orders::getProductType, 10)
                .eq(productType != null, Orders::getProductType, productType)
                .orderByDesc(Orders::getGmtCreate)
                .select(Orders::getOrderId, Orders::getOrderSn, Orders::getGmtCreate);
        IPage<Orders> page = this.page(
                new Query<Orders>().getPage(jsonObject),
                q
        );
        return new PageUtils(page);
    }


    @Override
    public void shopManageListByParametersExport2(JSONObject jsonObject, LambdaQueryWrapper<Orders> ordersLambdaQueryWrapper, HttpServletResponse response) {
        QueryWrapper<Orders> q = new QueryWrapper<>();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        Integer state = (Integer) innerMap.get("state");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        String startDate = (String) innerMap.get("startDate");
        String parentOrderId = (String) innerMap.get("parentOrderId");
        String endDate = (String) innerMap.get("endDate");
        String okStartDate = (String) innerMap.get("okStartDate");
        String okEndDate = (String) innerMap.get("okEndDate");
        String deliverGoodsStartDate = (String) innerMap.get("deliverGoodsStartDate");
        String deliverGoodsEndDate = (String) innerMap.get("deliverGoodsEndDate");
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        Integer productType = (Integer) innerMap.get("productType");
        String shopName = (String) innerMap.get("shopName");
        String untitled = (String) innerMap.get("untitled");
        String orderSn = (String) innerMap.get("orderSn");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        Boolean isQueryTwoOrder = (Boolean) innerMap.get("isQueryTwoOrder");
        List<String> ids = (ArrayList<String>) jsonObject.get("ids");
        // 只查询一级订单
        if (isQueryTwoOrder == null || !isQueryTwoOrder) {
            q.isNull("o.parent_order_id");
        }
        // 没有大宗月供了
        // if (productType != null && productType == 12) {
        //     q.eq("o.supplier_id", ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        // } else {
            q.eq("o.shop_id", ThreadLocalUtil.getCurrentUser().getShopId());
        // }
        q.in(io.seata.common.util.CollectionUtils.isNotEmpty(ids), "o.order_id", ids);
        q.like(StringUtils.isNotEmpty(shopName), "o.shop_name", shopName);
        q.like(StringUtils.isNotEmpty(untitled), "o.untitled", untitled);
        q.like(StringUtils.isNotEmpty(orderSn), "o.order_sn", orderSn);
        q.eq(StringUtils.isNotEmpty(parentOrderId), "o.parent_order_id", parentOrderId);
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like("o.order_sn", keywords)
                        .or()
                        .like("o.untitled", keywords)
                        .or()
                        .like("o.shop_name", keywords)
                        .or()
                        .like("o.receiver_mobile", keywords);
            });
        }
        q.ge(StringUtils.isNotBlank(abovePrice), "o.actual_amount", abovePrice);
        q.le(StringUtils.isNotBlank(belowPrice), "o.actual_amount", belowPrice);
        q.eq(orderClass != null, "o.order_class", orderClass);
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), "o.gmt_create", startDate, endDate);
        q.between(StringUtils.isNotEmpty(okStartDate) && StringUtils.isNotEmpty(okEndDate), "o.flish_time", okStartDate, okEndDate);
        q.between(StringUtils.isNotEmpty(deliverGoodsStartDate) && StringUtils.isNotEmpty(deliverGoodsEndDate), "o.delivery_time", deliverGoodsStartDate, deliverGoodsEndDate);
        q.eq(state != null, "o.state", state);
        q.eq(productType != null, "o.product_type", productType);
        if (orderBy == null) {
            orderBy = 1;
        }
        if (orderBy == 1) {
            q.orderByDesc("o.gmt_create");
        }
        if (orderBy == 2) {
            q.orderByDesc("o.flish_time");
        }
        if (orderBy == 3) {
            q.orderByDesc("o.delivery_time");
        }
        if (orderBy == 4) {
            q.orderByDesc("o.success_date");
        }
        q.eq("o.is_delete", 0);
        q.eq("oi.is_delete", 0);
        q.last("LIMIT 5000");
        List<GetShopManageOrderOutZIPVO> vos = baseMapper.selectOutOrderList(q);
        if (CollectionUtils.isEmpty(vos)) {
            throw new BusinessException("数据为空！");
        }
        FileInputStream fin = null;
        Workbook workBook = null;
        ByteArrayOutputStream excelOut = null;
        InputStream excelIS = null;
        ByteArrayOutputStream pdfOut = null;
        HashMap<String, InputStream> zipMapList = new HashMap<>();
        for (GetShopManageOrderOutZIPVO vo : vos) {
            try {
                try {
                    // 解析数据为excel
                    String templateFormUrl = mallConfig.templateFormUrl;
                    String url = null;
                    if (productType == 1) {

                        url = templateFormUrl + File.separator + "大宗零购合并订单导出模板.xlsx";
                    } else if (productType == 0) {
                        url = templateFormUrl + File.separator + "零星采购合并订单导出模板.xlsx";
                    }else { 
                        url = templateFormUrl + File.separator + "周转材料合并订单导出模板.xlsx";
                   }
                    fin = new FileInputStream(url);
                    Map<String, Object> map = new HashMap<>();
                    map.put("dataList", vo.getDataList());

                    BigDecimal subtotalTotalAmount = new BigDecimal(0);
                    BigDecimal subtotalMoRateAmount = new BigDecimal(0);

                    // 计算金额
                    for (GetShopManageOrderOutZIPDataItemVO d : vo.getDataList()) {
                        subtotalTotalAmount = subtotalTotalAmount.add(d.getTotalAmount());
                        subtotalMoRateAmount = subtotalMoRateAmount.add(d.getNoRateAmount());
                    }
                    if (productType != 0) {
                        map.put("paymentWeek", vo.getPaymentWeek());
                        map.put("outPhaseInterest", vo.getOutPhaseInterest().toPlainString());
                    }
                    map.put("subtotalMoRateAmountStr", subtotalMoRateAmount.toPlainString());
                    map.put("subtotalTotalAmountStr", subtotalTotalAmount.toPlainString());
                    map.put("supplierName", vo.getSupplierName());
                    map.put("enterpriseName", vo.getEnterpriseName());

//                XLSTransformer transformer = new XLSTransformer();
//                workBook = transformer.transformXLS(fin, map);
//                Sheet sheetAt = workBook.getSheetAt(0);
//                workBook.write(excelOut);
                    // 最后几个合并
//                sheetAt.addMergedRegion(new CellRangeAddress(sheetAt.getLastRowNum(), sheetAt.getLastRowNum(), 1, 10));
//                sheetAt.addMergedRegion(new CellRangeAddress(sheetAt.getLastRowNum() - 1, sheetAt.getLastRowNum() - 1, 1, 10));

                    // 获取输入流
                    excelOut = new ByteArrayOutputStream();
                    PoiExporter.export2Destination(fin, map, excelOut);
                    excelIS = new ByteArrayInputStream(excelOut.toByteArray());

                    // excel转成pdf
                    List<ExcelObject> excelSList = new ArrayList<>();
                    excelSList.add(new ExcelObject("", excelIS));
                    pdfOut = new ByteArrayOutputStream();
                    Excel2Pdf pdf = new Excel2Pdf(excelSList, pdfOut);
                    pdf.convert();

                    InputStream pdfIS = new ByteArrayInputStream(pdfOut.toByteArray());
                    Date successDate = vo.getSuccessDate();
                    if (successDate != null) {
                        String yyyYmm = DateUtil.getYYYYMMDD(successDate);
                        zipMapList.put(yyyYmm + vo.getEnterpriseName() + "订单数据.pdf", pdfIS);
                    } else {
                        zipMapList.put(vo.getEnterpriseName() + "订单数据.pdf", pdfIS);
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    try {
                        if (pdfOut != null) pdfOut.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        if (excelIS != null) excelIS.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        if (excelOut != null) excelOut.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        if (workBook != null) workBook.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        if (fin != null) fin.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } catch (Exception e) {
                e.getMessage();
            }
        }
        try {
//            response.setContentType("application/zip");
//            String fileName = URLEncoder.encode("零星采购合并订单.zip", StandardCharsets.UTF_8.toString());
//            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            ZipUtil.zipFileInput(zipMapList, new ZipOutputStream(response.getOutputStream()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 完结订单
     *
     * @param orderId
     * @param idStr
     * @param farArg
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer orderCloseClick(String orderId, String idStr, StringBuilder farArg) {
        Orders byId = getById(orderId);
        Integer state = byId.getState();
        if (state == 6) {
            throw new BusinessException("订单待发货不能完结订单！");
        }
        List<Orders> list = lambdaQuery().eq(Orders::getParentOrderId, orderId)
                .select(Orders::getOrderId, Orders::getState)
                .list();
        if (!CollectionUtils.isEmpty(list)) {
            for (Orders orders : list) {
                if (orders.getState() == 6) {
                    throw new BusinessException("还存在待发货订单不能完结订单！");
                }
                // 完成订单
                orders.setSuccessDate(new Date());
                orders.setState(10);
                orders.setFinishType(1);
                update(orders);
            }
        }
        if (state >= 8 && state < 10) {
            // 手动完结：退货申请已经结束 && 所有发货单都收货，订单增加按钮是否完结订单，提示未执行数量还回计划，不能再发货操作
            Integer count = orderReturnService.lambdaQuery().eq(OrderReturn::getOrderId, orderId)
                    .in(OrderReturn::getState, 1, 2).count();
            if (count > 0) {
                throw new BusinessException("该订单还存在退货申请不能完结订单！");
            }
            Integer count2 = orderShipService.lambdaQuery()
                    .eq(OrderShip::getOrderId, orderId)
                    .eq(OrderShip::getType, 0).count();
            if (count2 > 0) {
                throw new BusinessException("该订单存在已生成发货单不能完结订单！");
            }
            Integer count1 = orderShipService.lambdaQuery()
                    .eq(OrderShip::getOrderId, orderId)
                    .eq(OrderShip::getType, 1).count();
            if (count1 > 0) {
                throw new BusinessException("该订单存在还未收货的发货单不能完结订单！");
            }
            // 完成订单
            byId.setSuccessDate(new Date());
            byId.setState(10);
            byId.setFinishType(1);
            update(byId);

            // 如果是零星采购订单
            if (byId.getProductType() == 10) {
                List<OrderItem> list1 = orderItemService.lambdaQuery().eq(OrderItem::getOrderId, orderId).list();
                ArrayList<Map<String, Object>> retPlanList = new ArrayList<>();
                for (OrderItem orderItem : list1) {
                    OrderSelectPlan one = orderSelectPlanService.lambdaQuery().eq(OrderSelectPlan::getOrderItemId, orderItem.getOrderItemId()).one();
                    BigDecimal subtract = orderItem.getBuyCounts().subtract(orderItem.getReturnCounts()).subtract(orderItem.getConfirmCounts());
                    if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                    } else if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                        throw new BusinessException("完结数量出现错误！");
                    } else {
                        // 组装反写计划
                        HashMap<String, Object> onePlanMap = new HashMap<>();
                        onePlanMap.put("dtlId", one.getDtlId());
                        onePlanMap.put("billId", one.getBillId());
                        BigDecimal multiply = orderItem.getNetPrice().multiply(subtract).setScale(2, RoundingMode.HALF_UP);
                        onePlanMap.put("amount", multiply.negate());
                        onePlanMap.put("number", subtract.negate());
                        retPlanList.add(onePlanMap);
                    }

                }

                // 调用反写接口
                HashMap<Object, Object> subPlanDDTO = new HashMap<>();
                subPlanDDTO.put("data", retPlanList);
                subPlanDDTO.put("keyId", idStr);
                subPlanDDTO.put("orgId", byId.getOrgId());
                String content = JSON.toJSONString(subPlanDDTO);
                farArg.append(content);
                log.warn("反写计划接口请求参数：" + content);
                // 发送请求
                String url = mallConfig.prodPcwp2Url02 + "/thirdapi/sporadicPurchasePlan/updateShopDtl";
                LogUtil.writeInfoLog(idStr, "orderCloseClick", orderId, subPlanDDTO, null, OrdersServiceImpl.class);
                R rMap = null;
                try {
                    rMap = restTemplateUtils.postPCWP2(url, subPlanDDTO);
                } catch (Exception e) {
                    LogUtil.writeErrorLog(idStr, "orderCloseClick", orderId, subPlanDDTO, null, e.getMessage(), OrdersServiceImpl.class);
                    log.error(e.getMessage());
                    throw new BusinessException("远程反写计划服务异常！");
                }
                if (rMap.getCode() == null || rMap.getCode() != 200) {
                    LogUtil.writeErrorLog(idStr, "orderCloseClick", orderId, subPlanDDTO, rMap, rMap.getMessage(), OrdersServiceImpl.class);
                    log.error("反写计划接口错误！返回：" + rMap);
                    throw new BusinessException("远程反写计划服务异常");
                }

                InterfaceLogs iLog = new InterfaceLogs();
                iLog.setSecretKey(idStr);
                iLog.setClassPackage(OrdersServiceImpl.class.getName());
                iLog.setMethodName("orderCloseClick");
                iLog.setLocalArguments(orderId);
                iLog.setFarArguments(content);
                iLog.setIsSuccess(1);
                iLog.setLogType(1);
                iLog.setErrorInfo(null);
                interfaceLogsService.create(iLog);
            }
        }
        return byId.getProductType();
    }

    @Override
    public void listByParametersLCExport(JSONObject jsonObject, LambdaQueryWrapper<Orders> ordersLambdaQueryWrapper, HttpServletResponse response) {

    }

    @Override
    public PageUtils getTwoMaterialOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> q) {

        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String startTime = (String) innerMap.get("startTime");
        Integer billType = (Integer) innerMap.get("billType");
        String endTime = (String) innerMap.get("endTime");
        String supplierId = (String) innerMap.get("supplierId");
        String twoSupplierOrgId = (String) innerMap.get("twoSupplierOrgId");
        Integer productType = (Integer) innerMap.get("productType");
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(Orders::getOrderSn, keywords)
                        .or()
                        .like(Orders::getUntitled, keywords);
            });
        }
        if (StringUtils.isNotBlank(startTime)) {
            q.gt(Orders::getGmtCreate, startTime);
        }
        if (StringUtils.isNotBlank(endTime)) {
            q.lt(Orders::getGmtCreate, endTime);
        }
        if (StringUtils.isNotBlank(twoSupplierOrgId)) {
            q.eq(Orders::getSupplierId, twoSupplierOrgId);
        } else {
            q.eq(Orders::getSupplierId, supplierId);
        }
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        //暂时只有物资公司一个二级对账公司
        q.eq(Orders::getShopId, currentUser.getShopId());
        q.eq(productType != null, Orders::getProductType, productType);
        q.eq(billType != null, Orders::getBillType, billType);
        q.eq(Orders::getOrderClass, 3);
        q.orderByDesc(MustBaseEntity::getGmtCreate);
        q.isNotNull(Orders::getDeliveryTime);
        IPage<Orders> page = this.page(new Query<Orders>().getPage(jsonObject), q);
        return new PageUtils(page);
    }

    @Override
    public PageUtils getHaveTwoMaterialOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String startTime = (String) innerMap.get("startTime");
        Integer billType = (Integer) innerMap.get("billType");
        String endTime = (String) innerMap.get("endTime");
        String supplierId = (String) innerMap.get("supplierId");
        String twoSupplierOrgId = (String) innerMap.get("twoSupplierOrgId");
        Integer productType = (Integer) innerMap.get("productType");

        // 先批量查询有可对账物资的订单编号列表
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("supplierOrgId", supplierId);
        queryParams.put("twoSupplierOrgId", twoSupplierOrgId);
        queryParams.put("startTime", startTime);
        queryParams.put("endTime", endTime);

        List<String> reconcilableOrderSnList = baseMapper.getReconcilableOrderSnList(queryParams);
        if (CollectionUtils.isEmpty(reconcilableOrderSnList)) {
            // 如果没有可对账的订单，直接返回空结果
            IPage<Orders> emptyPage = this.page(new Query<Orders>().getPage(jsonObject), q.eq(Orders::getOrderId, ""));
            return new PageUtils(emptyPage);
        }

        // 基于可对账的订单编号进行查询，其他逻辑与getTwoMaterialOrderList一致
        q.in(Orders::getParentOrderId, reconcilableOrderSnList);

        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(Orders::getOrderSn, keywords)
                .or()
                .like(Orders::getUntitled, keywords);
            });
        }
        if (StringUtils.isNotBlank(startTime)) {
            q.gt(Orders::getGmtCreate, startTime);
        }
        if (StringUtils.isNotBlank(endTime)) {
            q.lt(Orders::getGmtCreate, endTime);
        }
        if (StringUtils.isNotBlank(twoSupplierOrgId)) {
            q.eq(Orders::getSupplierId, twoSupplierOrgId);
        } else {
            q.eq(Orders::getSupplierId, supplierId);
        }
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        //暂时只有物资公司一个二级对账公司
        q.eq(Orders::getShopId, currentUser.getShopId());
        if (productType != null) {
            if (productType == 0) {
                // 当productType等于0时
                q.in(Orders::getProductType, 0, 10);
            } else if (productType == 1) {
                // 当productType等于1时，查询条件为 in (1,13)
                q.in(Orders::getProductType, 1, 13);
            } else if (productType == 2) {
                // 当productType等于2时，查询条件为 = 2
                q.eq(Orders::getProductType, 2);
            }
        }
//        q.eq(productType != null, Orders::getProductType, productType);
        q.eq(billType != null, Orders::getBillType, billType);
        q.eq(Orders::getOrderClass, 3);
        q.orderByDesc(MustBaseEntity::getGmtCreate);
        q.isNotNull(Orders::getDeliveryTime);
        IPage<Orders> page = this.page(new Query<Orders>().getPage(jsonObject), q);
        return new PageUtils(page);
    }


    @Override
    public PageUtils getTwoSupplierMaterialOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> q) {
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();

        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String startTime = (String) innerMap.get("startTime");
        Integer billType = (Integer) innerMap.get("billType");
        String endTime = (String) innerMap.get("endTime");
        String shopId = (String) innerMap.get("shopId");
        Integer productType = (Integer) innerMap.get("productType");
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like(Orders::getOrderSn, keywords)
                .or()
                .like(Orders::getUntitled, keywords);
            });
        }
        if (StringUtils.isNotBlank(startTime)) {
            q.gt(Orders::getGmtCreate, startTime);
        }
        if (StringUtils.isNotBlank(endTime)) {
            q.lt(Orders::getGmtCreate, endTime);
        }
        q.eq(Orders::getSupplierId, currentUser.getEnterpriseId());
        //暂时只有物资公司一个二级对账公司
        q.eq(Orders::getShopId, shopId);
//        q.eq(productType != null, Orders::getProductType, productType);
        if (productType != null) {
            if (productType == 0) {//零星采购
                // 当productType等于0时,兼容新老数据
                q.in(Orders::getProductType, 0, 10);
            } else if (productType == 1) {
                // 当productType等于1时，,兼容新老数据
                q.in(Orders::getProductType, 1, 13);
            } else if (productType == 2) {
                // 当productType等于2时，查询条件为 = 2
                q.eq(Orders::getProductType, 2);
            }
        }

        // 先批量查询有可对账物资的订单编号列表
        UserLogin UserLogin = ThreadLocalUtil.getCurrentUser();
        Map<String, Object> queryParams = new HashMap<>();
//        queryParams.put("supplierOrgId", shopId);
        queryParams.put("twoSupplierOrgId", UserLogin.getEnterpriseId());
        queryParams.put("startTime", startTime);
        queryParams.put("endTime", endTime);
        //条件过滤条件，订单只有可对账的物资才查询出来
        List<String> reconcilableOrderSnList = baseMapper.getReconcilableOrderSnList(queryParams);
        if (CollectionUtils.isEmpty(reconcilableOrderSnList)) {
            // 如果没有可对账的订单，直接返回空结果
            IPage<Orders> emptyPage = this.page(new Query<Orders>().getPage(jsonObject), q.eq(Orders::getOrderId, ""));
            return new PageUtils(emptyPage);
        }
        // 基于可对账的订单编号进行查询，其他逻辑与getTwoMaterialOrderList一致
        q.in(Orders::getParentOrderId, reconcilableOrderSnList);
        // 先批量查询有可对账物资的订单编号列表
        q.eq(billType != null, Orders::getBillType, billType);
        q.eq(Orders::getOrderClass, 3);
        q.orderByDesc(MustBaseEntity::getGmtCreate);
        q.isNotNull(Orders::getDeliveryTime);
        IPage<Orders> page = this.page(new Query<Orders>().getPage(jsonObject), q);
        return new PageUtils(page);
    }


    @Override
    public void getPlatformOrdersShopCountExcel(JSONObject jsonObject, QueryWrapper<ProductFromVo> productFromVoQueryWrapper, HttpServletResponse response) {
        QueryWrapper<PlatformShopCountVo> q = new QueryWrapper<>();
        QueryWrapper<ShopCountVo> wrapper = new QueryWrapper<>();
        Integer mallType = mallConfig.mallType;
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String startCreateDate = (String) innerMap.get("startDate");
        String endCreateDate = (String) innerMap.get("endDate");
        String shopName = (String) innerMap.get("homeName");
        wrapper.ne("o.order_class", 3);
        q.ne("order_class", 3);
        if (startCreateDate != null || endCreateDate != null) {
            q.between("gmt_create", startCreateDate, endCreateDate);
            wrapper.between("o.gmt_create", startCreateDate, endCreateDate);
        }
        if (shopName != null) {
            wrapper.like("s.shop_name", shopName);
        }

        q.eq("mall_type", mallType);
        PlatformShopCountVo vo = ordersMapper.getPlatformOrderTotalCount(q);
        wrapper.eq("o.mall_type", mallType);
        wrapper.groupBy("o.shop_id");
        List<ShopCountVo> list = ordersMapper.getPlatformShopOrderCountExcel(wrapper);
        vo.setShopCountVoList(list);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", list);
        try {
            String src = mallConfig.templateFormUrl;
            ExcelForWebUtil.exportExcel(response, dataMap, "店铺商品统计模板.xlsx", src, "店铺商品统计.xlsx");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 为订单创建TT待办对象
     * @param order 订单对象
     * @return TT待办对象
     */
    private ToDoMessageBody createTTTodoForOrder(Orders order) {
        ToDoMessageBody todo = new ToDoMessageBody();

        // 根据产品类型设置不同的待办信息
        Integer productType = order.getProductType();
        String orderId = order.getOrderId();
        String orderSn = order.getOrderSn();

        // 设置待办ID和基本信息
        switch (productType) {
            case 10: // 零星采购
                todo.setToDoId("LXCG" + orderId + "MA6CQYXG4H");
                todo.setModule("物资采购-零星采购");
                todo.setTodoType("零星采购审核");
                todo.setTitle("零星采购订单审核");
                todo.setDescription("订单编号：" + orderSn + "，零星采购订单。请尽快审核。");
                todo.setWebUrl("https://wzgp.scrbg.com/retail-order/approve?id=" + orderId + "&type=approval");
                break;
            case 13: // 大宗临购
                todo.setToDoId("WZGP" + orderId + "MA6CQYXG4H");
                todo.setModule("物资采购-大宗临购审核");
                todo.setTodoType("物资采购中心审核");
                todo.setTitle("大宗临购订单审核");
                todo.setDescription("订单编号：" + orderSn + "，大宗临购订单。请尽快审核。");
                todo.setWebUrl("https://wzgp.scrbg.com/bulk-order/approve?id=" + orderId + "&type=approval");
                break;
            case 14: // 周转材料
                todo.setToDoId("ZZCL" + orderId + "MA6CQYXG4H");
                todo.setModule("物资采购-周转材料");
                todo.setTodoType("周转材料审核");
                todo.setTitle("周转材料订单审核");
                todo.setDescription("订单编号：" + orderSn + "，周转材料订单。请尽快审核。");
                todo.setWebUrl("https://wzgp.scrbg.com/turnover-material/approve?id=" + orderId + "&type=approval");
                break;
            default:
                // 默认处理
                todo.setToDoId("ORDER" + orderId + "MA6CQYXG4H");
                todo.setModule("物资采购-订单审核");
                todo.setTodoType("订单审核");
                todo.setTitle("订单审核");
                todo.setDescription("订单编号：" + orderSn + "，订单审核。请尽快审核。");
                todo.setWebUrl("https://wzgp.scrbg.com/order/approve?id=" + orderId + "&type=approval");
                break;
        }

        // 设置通用字段
        todo.setOrginSystem("物资采购平台");
        todo.setEmployeeNumber("036529");  // 指定账号
        todo.setUserId("391E2FB8-F295-4045-8CDC-340AD3DE6700");  // 指定账号用户ID
        todo.setStatus(0);  // 0=待办
        todo.setLastupdateTime(getCurrentTime());
        todo.setAppId("wzgp.scrbg.com");
        todo.setUwpUrl("");
        todo.setIosUrl("");
        todo.setAndroidUrl("");

        return todo;
    }

    /**
     * 获取当前时间
     */
    private String getCurrentTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
    }

    @Override
    public void acceptOrder(String orderId, String assigneeId) {
        Orders orders = getById(orderId);
        if (orders==null) {
            throw new BusinessException("订单不存在");
        }
        User assignee = userService.getById(assigneeId);
        if (assignee==null) {
            throw new BusinessException("用户不存在");
        }
        if (orders.getAssignState()==OrderEnum.DISPATCH_STATE_ASSIGNED.getCode()) {
            throw new BusinessException("订单已被接单");
        }
        boolean flag = this.lambdaUpdate()
            .eq(Orders::getOrderId, orderId)
            .eq(Orders::getAssignState, OrderEnum.DISPATCH_STATE_UNASSIGNED.getCode())
            .set(Orders::getAssignState, OrderEnum.DISPATCH_STATE_ASSIGNED.getCode())
            .set(Orders::getAssigneeTime, LocalDateTime.now())
            .set(Orders::getAssigneeId, assigneeId)
            .set(Orders::getAssigneeName, assignee.getNickName())
            .set(Orders::getRejectReason, null)
            .update();
        if (!flag) {
            throw new BusinessException("操作失败，请刷新页面重试");
        }
    }

    @Override
    public void rejectOrder(String orderId, String reason) {
        Orders orders = getById(orderId);
        if (orders==null) {
            throw new BusinessException("订单不存在");
        }
        if (orders.getAssignState()==OrderEnum.DISPATCH_STATE_UNASSIGNED.getCode()) {
            throw new BusinessException("订单状态异常");
        }
        UserLogin assignee = ThreadLocalUtil.getCurrentUser();
        if (!orders.getAssigneeId().equals(assignee.getUserId())) {
            throw new BusinessException("只能退单自己的订单");
        }
        boolean flag = this.lambdaUpdate()
            .eq(Orders::getOrderId, orderId)
            .eq(Orders::getAssignState, OrderEnum.DISPATCH_STATE_ASSIGNED.getCode())
            .eq(Orders::getAssigneeId, assignee.getUserId())
            .set(Orders::getAssignState, OrderEnum.DISPATCH_STATE_UNASSIGNED.getCode())
            .set(Orders::getRejectReason, reason)
            .update();
        if (!flag) {
            throw new BusinessException("退单失败");
        }
    }

    @Override
    public void supplierExportOrders(List<String> ids, HttpServletResponse response) {
        List<Orders> orders = this.lambdaQuery().in(Orders::getOrderId, ids).list();
        List<OrderItem> orderItems = orderItemService.lambdaQuery().in(OrderItem::getOrderId, ids).list();
        Map<String, List<OrderItem>> groupedOrderItems = orderItems.stream().collect(Collectors.groupingBy(OrderItem::getOrderId));
        FileInputStream fin = null;
        Workbook workBook = null;
        ByteArrayOutputStream excelOut = null;
        InputStream excelIS = null;
        ByteArrayOutputStream pdfOut = null;
        HashMap<String, InputStream> zipMapList = new HashMap<>();
        for (Orders order : orders) {
            try {
                try {
                    int productType = order.getProductType();
                    // 解析数据为excel
                    String templateFormUrl = mallConfig.templateFormUrl;
                    String url = null;
                    if (productType == 1) {

                        url = templateFormUrl + File.separator + "大宗零购合并订单导出模板.xlsx";
                    } else if (productType == 0) {
                        url = templateFormUrl + File.separator + "零星采购合并订单导出模板.xlsx";
                    }else { 
                        url = templateFormUrl + File.separator + "周转材料合并订单导出模板.xlsx";
                   }
                    fin = new FileInputStream(url);
                    Map<String, Object> map = new HashMap<>();
                    List<OrderItem> items = groupedOrderItems.get(order.getOrderId());
                    map.put("dataList", items.stream().map(o -> {
                        GetShopManageOrderOutZIPDataItemVO v = new GetShopManageOrderOutZIPDataItemVO();
                        BeanUtils.copyProperties(o, v);
                        v.setCreateTime(o.getGmtCreate());
                        v.setOrderRemark(order.getOrderRemark());
                        return v;
                    }).collect(Collectors.toList()));
                    // TODO 模板里并没有使用这两个变量
                    if (productType != 0) {
                        map.put("paymentWeek", order.getPaymentWeek());
                        map.put("outPhaseInterest", mapToNullablePlanString(order.getOutPhaseInterest()));
                    }
                    map.put("subtotalTotalAmountStr", mapToNullablePlanString(order.getTotalAmount()));
                    map.put("subtotalMoRateAmountStr", mapToNullablePlanString(order.getNoRateAmount()));
                    map.put("supplierName", order.getSupplierName());
                    map.put("enterpriseName", order.getEnterpriseName());

                    // 获取输入流
                    excelOut = new ByteArrayOutputStream();
                    PoiExporter.export2Destination(fin, map, excelOut);
                    excelIS = new ByteArrayInputStream(excelOut.toByteArray());

                    // excel转成pdf
                    List<ExcelObject> excelSList = new ArrayList<>();
                    excelSList.add(new ExcelObject("", excelIS));
                    pdfOut = new ByteArrayOutputStream();
                    Excel2Pdf pdf = new Excel2Pdf(excelSList, pdfOut);
                    pdf.convert();

                    InputStream pdfIS = new ByteArrayInputStream(pdfOut.toByteArray());
                    Date successDate = order.getSuccessDate();
                    if (successDate != null) {
                        String yyyYmm = DateUtil.getYYYYMMDD(successDate);
                        zipMapList.put(yyyYmm + order.getEnterpriseName() + order.getOrderSn() + "订单数据.pdf", pdfIS);
                    } else {
                        zipMapList.put(order.getEnterpriseName() + order.getOrderSn() + "订单数据.pdf", pdfIS);
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    try {
                        if (pdfOut != null) pdfOut.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        if (excelIS != null) excelIS.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        if (excelOut != null) excelOut.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        if (workBook != null) workBook.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    try {
                        if (fin != null) fin.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } catch (Exception e) {
                e.getMessage();
            }
        }
        try {
            ZipUtil.zipFileInput(zipMapList, new ZipOutputStream(response.getOutputStream()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    private String mapToNullablePlanString(BigDecimal v){
        if (v==null) {
            return null;
        }
        return v.toPlainString();
    }

    /**
     * 订单拆分辅助方法（多供方订单）
     * 参考processOrderSplitting方法，将子订单和子订单项插入到对应表中
     * @param mainOrder 主订单
     * @param orderItems 订单明细列表
     * @param mainTaxRate 主订单税率
     */
    private void processOrderSplittingHelper(Orders mainOrder, List<OrderItem> orderItems, BigDecimal mainTaxRate) {
        if (mainOrder == null || CollectionUtils.isEmpty(orderItems)) {
            throw new BusinessException("主订单或订单明细不能为空！");
        }

        // 按供应商分组
        Map<String, List<OrderItem>> supplierOrderItems = orderItems.stream()
                .collect(Collectors.groupingBy(OrderItem::getSupplierId));

        for (Map.Entry<String, List<OrderItem>> entry : supplierOrderItems.entrySet()) {
            String supplierId = entry.getKey();
            List<OrderItem> items = entry.getValue();

            // 创建子订单
            Orders subOrder = createSubOrderHelper(mainOrder, supplierId, items);

            // 更新订单明细
            updateOrderItemsForSubOrderHelper(subOrder, items);
        }
    }

    /**
     * 拆分子订单辅助方法
     * @param mainOrder 主订单
     * @param orderItems 订单明细列表
     * @return 拆分后的子订单列表
     */
    private List<Orders> splitSubOrdersHelper(Orders mainOrder, List<OrderItem> orderItems) {
        if (mainOrder == null || CollectionUtils.isEmpty(orderItems)) {
            throw new BusinessException("主订单或订单明细不能为空！");
        }

        // 按供应商分组
        Map<String, List<OrderItem>> supplierOrderItems = orderItems.stream()
                .collect(Collectors.groupingBy(OrderItem::getSupplierId));

        List<Orders> subOrders = new ArrayList<>();

        for (Map.Entry<String, List<OrderItem>> entry : supplierOrderItems.entrySet()) {
            String supplierId = entry.getKey();
            List<OrderItem> items = entry.getValue();

            // 创建子订单
            Orders subOrder = createSubOrderHelper(mainOrder, supplierId, items);
            subOrders.add(subOrder);
        }

        return subOrders;
    }

    /**
     * 拆分子订单项辅助方法
     * @param subOrder 子订单
     * @param orderItems 原订单明细列表
     * @return 拆分后的子订单项列表
     */
    private List<OrderItem> splitSubOrderItemsHelper(Orders subOrder, List<OrderItem> orderItems) {
        if (subOrder == null || CollectionUtils.isEmpty(orderItems)) {
            throw new BusinessException("子订单或订单明细不能为空！");
        }

        // 筛选出属于该供应商的订单项
        List<OrderItem> supplierItems = orderItems.stream()
                .filter(item -> subOrder.getSupplierId().equals(item.getSupplierId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(supplierItems)) {
            throw new BusinessException("未找到属于供应商【" + subOrder.getSupplierName() + "】的订单明细！");
        }

        // 更新订单明细
        updateOrderItemsForSubOrderHelper(subOrder, supplierItems);

        return supplierItems;
    }

    /**
     * 创建子订单辅助方法
     * 参考OrderServiceImpl.createSubOrder方法
     */
    private Orders createSubOrderHelper(Orders mainOrder, String supplierId, List<OrderItem> orderItems) {
        // 获取供应商信息
        EnterpriseInfo supplierInfo = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getEnterpriseId, supplierId)
                .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getTaxRate)
                .one();

        if (supplierInfo == null) {
            throw new BusinessException("供应商不存在！");
        }

        BigDecimal taxRate = supplierInfo.getTaxRate();
        if (taxRate == null || taxRate.compareTo(BigDecimal.ZERO) == 0) {
            throw new BusinessException("【" + supplierInfo.getEnterpriseName() + "】供应商未设置税率！");
        }

        // 计算子订单金额
        BigDecimal totalCostPrice = BigDecimal.ZERO;
        StringBuilder productNames = new StringBuilder();

        for (OrderItem item : orderItems) {
            if (productNames.length() > 0) {
                productNames.append(",");
            }
            productNames.append(item.getProductName());

            BigDecimal itemCostPrice = item.getProductPrice().multiply(item.getBuyCounts()).setScale(2, RoundingMode.HALF_UP);
            totalCostPrice = totalCostPrice.add(itemCostPrice);
        }

        // 创建子订单
        Orders subOrder = new Orders();
        BeanUtils.copyProperties(mainOrder, subOrder);

        subOrder.setOrderId(null);
        subOrder.setOrderSn(BillNumberGenerator.generateOrderNumber(mainOrder.getProductType(), true));
        subOrder.setUntitled(productNames.toString());
        subOrder.setSupplierId(supplierId);
        subOrder.setSupplierName(supplierInfo.getEnterpriseName());
        subOrder.setTaxRate(taxRate);
        subOrder.setActualAmount(totalCostPrice);
        subOrder.setTotalAmount(BigDecimal.ZERO);
        subOrder.setCostPriceTotal(BigDecimal.ZERO);
        subOrder.setOrderClass(3);
        subOrder.setParentOrderId(mainOrder.getOrderId());
        subOrder.setOrderSourceType(1);
        subOrder.setOrderSourceId(mainOrder.getOrderId());

        if (mallConfig.isNotRateAmount == 1) {
            subOrder.setNoRateAmount(BigDecimal.ZERO);
        } else {
            subOrder.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(totalCostPrice, taxRate));
        }

        boolean saved = this.save(subOrder);
        if (!saved) {
            throw new BusinessException("子订单保存失败！");
        }

        return subOrder;
    }

    /**
     * 更新子订单的订单明细辅助方法
     * 参考OrderServiceImpl.updateOrderItemsForSubOrder方法
     */
    private void updateOrderItemsForSubOrderHelper(Orders subOrder, List<OrderItem> orderItems) {
        BigDecimal taxRate = subOrder.getTaxRate();

        for (OrderItem item : orderItems) {
            // 保存父明细ID
            item.setParentOrderItemId(item.getOrderItemId());
            item.setOrderItemId(null);
            item.setOrderId(subOrder.getOrderId());
            item.setOrderSn(subOrder.getOrderSn());
            item.setState(1);

            // 更新价格信息（子订单使用成本价）
            item.setProductPrice(item.getCostPrice());
            item.setTotalAmount(item.getCostPrice().multiply(item.getBuyCounts()).setScale(2, RoundingMode.HALF_UP));

            // 更新税率信息
            item.setTaxRate(taxRate);
            BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(item.getCostPrice(), taxRate).setScale(2,
                    RoundingMode.HALF_UP);
            item.setNoRatePrice(noRatePrice);
            BigDecimal noRateAmount = TaxCalculator.noTarRateItemAmount(item.getTotalAmount(), noRatePrice,
                    item.getBuyCounts(), taxRate);
            item.setNoRateAmount(noRateAmount);

            // 保存订单明细
            orderItemService.save(item);
        }
    }
}