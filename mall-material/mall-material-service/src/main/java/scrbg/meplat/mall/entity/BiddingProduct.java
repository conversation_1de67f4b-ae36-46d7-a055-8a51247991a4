package scrbg.meplat.mall.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：竞价商品信息
 * @作者: ye
 * @日期: 2023-07-19
 */
@ApiModel(value = "竞价商品信息")
@Data
@TableName("bidding_product")
public class BiddingProduct extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "竞价采购商品id")
    private String biddingProductId;

    @ApiModelProperty(value = "竞价采购id")

    private String biddingId;


    @ApiModelProperty(value = "竞价采购编号")

    private String biddingSn;


    @ApiModelProperty(value = "订单id")

    private String orderId;


    @ApiModelProperty(value = "订单编号")

    private String orderSn;


    @ApiModelProperty(value = "订单明细id")

    private String orderItemId;
    @ApiModelProperty(value = "大宗临购单id")
    private String synthesizeTemporaryId;

    @ApiModelProperty(value = "大宗临购单编号")

    private String synthesizeTemporarySn;
    @ApiModelProperty(value = "大宗临购单明细id")
    private String synthesizeTemporaryDtlId;

    @ApiModelProperty(value = "商品名称")

    private String productName;


    @ApiModelProperty(value = "品牌名称")

    private String brand;


    @ApiModelProperty(value = "规格型号")

    private String spec;


    @ApiModelProperty(value = "计量单位")

    private String unit;


    @ApiModelProperty(value = "数量")

    private BigDecimal num;

    @ApiModelProperty(value = "商品材质")

    private String productTexture;


    @ApiModelProperty(value = "状态")

    private Integer state;


    @ApiModelProperty(value = "分类id")

    private String classId;


    @ApiModelProperty(value = "分类路径名称（xxx/xxxx/xxx）")

    private String classPathName;


    @ApiModelProperty(value = "创建机构id")

    private String createOrgId;

    @ApiModelProperty(value = "创建机构名称")

    private String createOrgName;

    @ApiModelProperty(value = "参考单价（限制最高价）")

    private BigDecimal referencePrice;


    @ApiModelProperty(value = "供货时间")

    private Date deliveryDate;


    @ApiModelProperty(value = "供货地点")

    private String deliveryAddress;

    @ApiModelProperty(value = "商品编号")

    private String productSn;
    @ApiModelProperty(value = "网价(清单浮动报价使用)")

    private BigDecimal netPrice;

    //上下游账期月差
    private BigDecimal monthlyDifference;

    /**
     * 含税拟销售单价
     */
    private  BigDecimal unitPriceIncludingTax;

    /**
     * 含税拟销售金额
     */
    private BigDecimal taxInclusiveAmount;

    //供应商报价
    @ApiModelProperty(value = "不含税到场单价")
    private BigDecimal bidPrice;

    //供应商报价
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    //供应商报价
    @ApiModelProperty(value = "含税到场单价")
    private BigDecimal bidRatePrice;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal bidRateAmount;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal bidAmount;

    @ApiModelProperty(value = "修改后的竞价价格")
    private BigDecimal newPrice;

    @ApiModelProperty(value = "价格是否修改 1已修改 0未修改 2修改审批完成")
    private Integer isUpdatePrice;

    @ApiModelProperty(value = "价格更新时间")
    private Date priceTime;
}