package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.dto.order.OrderShipmentsQtyIsOkDTO;
import scrbg.meplat.mall.dto.order.UpdateCostPriceDTO;
import scrbg.meplat.mall.dto.order.UpdateOrderItemQtyDTO;
import scrbg.meplat.mall.dto.order.UpdateOrderPriceDTO;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.OrderReturnItem;
import scrbg.meplat.mall.entity.OrderShip;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.vo.product.order.OrderItemVo;
import scrbg.meplat.mall.vo.shopManage.reportForms.PlatformProductFromVo;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @描述：订单项 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface OrderItemService extends IService<OrderItem> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderItem> queryWrapper);

    List<OrderItem> getOrderItemByOrderIds(List<String> orderIds);


    void create(OrderItem orderItem);

    void update(OrderItem orderItem);
    void updateShipCounts(OrderItem orderItem);

    OrderItem getById(String id);

    void delete(String id);


    List<OrderItem> getOrderItemList(JSONObject jsonObject, LambdaQueryWrapper<OrderItem> orderItemLambdaQueryWrapper);

    /**
     * 批量修改订单项
     * @param orderItems
     */
    void batchUpdateOrderItem(List<OrderItem> orderItems);

    /**
     * 装备结算提交合同
     * @param map
     */
    void deviceSubContract(Map map);

    /**
     * 改价
     * @param dtos
     */
    void updateOrderPrice(List<UpdateOrderPriceDTO> dtos);

    /**
     * 查询订单所有订单项
     * @param jsonObject
     * @param orderItemVoQueryWrapper
     * @return
     */
    PageUtils orderItemListByOrderId(JSONObject jsonObject, QueryWrapper<OrderItemVo> orderItemVoQueryWrapper);


    void changReturnState(String orderItemId, BigDecimal count);

    Orders isFilshAllOrderItem(String orderId);

    /**
     * 根据订单项数量修改订单状态
     * @param orderSn
     * @return
     */
    void updateOrderStateAndOrderItemByShip(OrderShip orderShip);


    /**
     * 根据订单查询所有的订单项
     * @param orderId
     */
    List<OrderItem> findByOrderIdList(String orderId);


    /**
     * 根据父级订单id查询父级订单信息
     * @param orderId
     */
    OrderItem getOrderItemByParentId(String parentOrderItemId);

    void updateReturnState(OrderItem orderItem);

    /**
     * 查询可以选择的竞价采购的零星采购多供方订单明细
     * @param jsonObject
     * @return
     */
    PageUtils listBidingOrderItemsList(JSONObject jsonObject);



    /**
     * 根据订单项集合查询所有子订单项的信息
     * @param
     * @return
     */
    List<OrderItem> selectsonAllByIds(List<String> orderItemIds);


    /**
     * 零星采购发货单项根据发货数量后确认发货数量生成 金额 和单价
     * @param item   订单项
     * @param orderReturnItem    退货项
     * @param returnCounts  发货数量后确认发货数量
     */

    void createAmouns(OrderItem item, OrderReturnItem orderReturnItem, BigDecimal returnCounts);

    List<OrderItem> findAllByOrderId(String orderId);

    PageUtils getPlatformOrderItemCount(JSONObject jsonObject, QueryWrapper<OrderItem> orderItemQueryWrapper);
    /**
     *
     * 后台管理订单项维度统计
     * */
    PageUtils getAssetNameDimensionList(JSONObject jsonObject, QueryWrapper<OrderItem> orderItemQueryWrapper);

    /**
     * 商品交易量统计
     * @param jsonObject
     * @param wrapper
     * @return
     */
    PageUtils getShopManageMaterial(JSONObject jsonObject, QueryWrapper<PlatformProductFromVo> wrapper);

    void supplierOutputExcel(JSONObject jsonObject, QueryWrapper<PlatformProductFromVo> listShipByAffirmListVOQueryWrapper, HttpServletResponse response);

    /**
     * 大宗临购修改供方综合单价
     * @param dtos
     */
    void updateOrderCostPrice(List<UpdateCostPriceDTO> dtos);

    /**
     * 根据订单类型判断获取已完成订单项历史
     * @param productType
     * @return
     */
    PageUtils findAllHistoryOrderItem(JSONObject productType);


    /**
     * 修改大宗月供订单数量
     * @param dtos
     */
    void updateDZYGOrderItemQty(List<UpdateOrderItemQtyDTO> dtos);


    /**
     * 修改大宗临购订单数量
     *
     * @param dtos
     * @param idStr
     * @param stringBuilder
     */
    void updateDZLGOrderItemQty(List<UpdateOrderItemQtyDTO> dtos, String idStr, StringBuilder stringBuilder);



    /**
     * 大宗月供发货校验当前数量
     * @param dtos
     */
    void orderShipmentsQtyIsOkYG(List<OrderShipmentsQtyIsOkDTO> dtos);


    /**
     * 大宗临购发货校验当前数量
     * @param dtos
     */
    void orderShipmentsQtyIsOkLG(List<OrderShipmentsQtyIsOkDTO> dtos);

    /**
     * 查询确认收货数量
     * @param orderItemId
     * @return
     */
    BigDecimal getConfirmCounts(String orderItemId);


    /**
     * 查询可以选择的竞价采购订单号分页
     * @param jsonObject
     * @param orderItemLambdaQueryWrapper
     * @return
     */
    PageUtils listBidingOrderListIds(JSONObject jsonObject, LambdaQueryWrapper<OrderItem> orderItemLambdaQueryWrapper);


    /**
     * 根据订单id获取可以发货的数量
     * @param orderItemId
     * @return
     */
    BigDecimal getSendProductMaxNumByOrderItemId(String orderItemId);


    /**
     * 订单数量-商城退货 > 有效数量（有收货使用收获，未收货使用发货）-pcwp退货数量时，为未全部发货
     * @param orderItemId
     * @return
     */
    boolean getIsNotAllSendProduct(String orderItemId);

    /**
     * 修改所有商品的销量
     * @param priductSn
     */
    void upProductSoldNum();

    //零星计算一级订单价格
    void createOneAmouns(OrderItem item, OrderReturnItem orderReturnItem, BigDecimal returnCounts);


    void getAssetNameDimensionListExcel(JSONObject jsonObject, HttpServletResponse response);
}
