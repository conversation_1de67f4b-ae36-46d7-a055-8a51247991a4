package scrbg.meplat.mall.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MaterialDemand {
    // 金额(元)
    private BigDecimal amount;

    // 临时需用计划id
    private String billId;

    // 临时需用计划明细id
    private String dtlId;

    // 使用结束时间
    private Date endTime;

    // 租赁时间
    private Integer leaseTime;

    // 物资类别id(1级类别id/2级类别id/..)
    private String materialClassId;

    // 物资类别名称(1级类别名称/2级类别名称/..)
    private String materialClassName;

    // 物资id
    private String materialId;

    // 物资名称
    private String materialName;

    // 单价(元)
    private BigDecimal price;

    // 采购方式
    private Integer purchaseType;

    // 数量
    private Integer quantity;

    // 规格型号
    private String spec;

    // 使用开始时间
    private Date startTime;

    // 材质
    private String texture;

    // 计时单位
    private String timeUnit;

    // 计量单位
    private String unit;


}
