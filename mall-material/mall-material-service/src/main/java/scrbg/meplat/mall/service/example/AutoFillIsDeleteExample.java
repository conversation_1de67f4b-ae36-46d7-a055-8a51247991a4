package scrbg.meplat.mall.service.example;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.service.OrdersService;

/**
 * MyMetaObjectHandler自动填充isDelete字段使用示例
 */
@Component
public class AutoFillIsDeleteExample {

    @Autowired
    private OrdersService ordersService;

    /**
     * 示例1：通过设置isDelete字段值，让MyMetaObjectHandler自动填充
     */
    public void exampleAutoFillIsDelete() {
        // 获取订单
        Orders order = ordersService.getById("order-id");
        
        // 方式1：设置为删除状态
        order.setIsDelete(PublicEnum.IS_DELETE_YES.getCode()); // -1
        
        // 调用update方法，MyMetaObjectHandler会自动填充isDelete字段
        boolean result = ordersService.updateById(order);
        
        System.out.println("更新结果: " + result);
        System.out.println("isDelete字段已自动设置为: " + order.getIsDelete());
    }

    /**
     * 示例2：设置为未删除状态
     */
    public void exampleAutoFillIsDeleteNo() {
        Orders order = ordersService.getById("order-id");
        
        // 设置为未删除状态
        order.setIsDelete(PublicEnum.IS_DELETE_NO.getCode()); // 0
        
        // MyMetaObjectHandler会自动填充
        ordersService.updateById(order);
        
        System.out.println("isDelete字段已自动设置为: " + order.getIsDelete());
    }

    /**
     * 示例3：在订单拆分成功后自动设置删除状态
     */
    public void exampleOrderSplittingWithAutoFill() {
        Orders order = ordersService.getById("order-id");
        
        // 设置订单为删除状态（表示主订单已拆分）
        order.setIsDelete(PublicEnum.IS_DELETE_YES.getCode());
        
        // 更新订单，MyMetaObjectHandler会自动处理isDelete字段
        boolean result = ordersService.updateById(order);
        
        if (result) {
            System.out.println("订单拆分完成，主订单已标记为删除状态");
        }
    }

    /**
     * 示例4：批量更新时的自动填充
     */
    public void exampleBatchUpdateWithAutoFill() {
        // 获取需要批量删除的订单
        Orders order1 = ordersService.getById("order-id-1");
        Orders order2 = ordersService.getById("order-id-2");
        
        // 设置删除状态
        order1.setIsDelete(PublicEnum.IS_DELETE_YES.getCode());
        order2.setIsDelete(PublicEnum.IS_DELETE_YES.getCode());
        
        // 批量更新，每个订单的isDelete字段都会被自动填充
        ordersService.updateBatchById(java.util.Arrays.asList(order1, order2));
        
        System.out.println("批量更新完成，isDelete字段已自动填充");
    }

    /**
     * 示例5：使用Lambda更新（注意：Lambda更新不会触发MyMetaObjectHandler）
     */
    public void exampleLambdaUpdateNote() {
        // 注意：这种方式不会触发MyMetaObjectHandler
        // ordersService.lambdaUpdate()
        //     .eq(Orders::getOrderId, "order-id")
        //     .set(Orders::getIsDelete, PublicEnum.IS_DELETE_YES.getCode())
        //     .update();
        
        // 如果要使用自动填充，应该使用updateById方式
        Orders order = ordersService.getById("order-id");
        order.setIsDelete(PublicEnum.IS_DELETE_YES.getCode());
        ordersService.updateById(order);
        
        System.out.println("使用updateById方式可以触发自动填充");
    }
}
