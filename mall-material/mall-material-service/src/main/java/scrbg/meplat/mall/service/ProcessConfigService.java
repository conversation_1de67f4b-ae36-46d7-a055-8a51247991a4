package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;

import scrbg.meplat.mall.dto.process.ProcessConfigDtlDTO;
import scrbg.meplat.mall.entity.ProcessConfig;
import scrbg.meplat.mall.entity.ProcessNodeOperation;
import scrbg.meplat.mall.entity.ProcessWorkflowResult;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.processConfig.ProcessConfigDtlVO;

import java.util.List;

/**
 * 流程配置表 服务类
 * <AUTHOR>
 * @date: 2025年6月20日 上午9:51:25
 */
public interface ProcessConfigService extends IService<ProcessConfig> {

        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<ProcessConfig> queryWrapper);

        void create(ProcessConfigDtlDTO processConfigDtlDTO);

        void update(ProcessConfigDtlDTO processConfigDtlDTO);

        ProcessConfig getById(String id);
        
        ProcessConfigDtlVO getProcessConfigDtlById(String processId);

        void delete(String id);

        /**
         * 通用流程处理接口
         * @param processId 流程Id
         * @param currentUser 操作人
         * @param operation 操作类型(0提交 1通过 2不通过 3-撤回)
         * @param businessKey 业务表主键ID
         * @param remark 操作备注
         * @return 处理结果，包含流程实例ID和当前节点信息
         */
        ProcessWorkflowResult myFunc(String processId, UserLogin currentUser, Integer operation, String businessKey, String remark);

        String getAuditUser(String businessKey);

        String getSubmitUser(String processId);

        List<ProcessNodeOperation> getAuditLog(String businessKey);

        boolean processIsFinished(String businessKey);

}
