package scrbg.meplat.mall.service.order;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

import scrbg.meplat.mall.dto.order.ProductBuyInfoDTO;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.entity.plan.Plan;

/**
 * 订单相关业务，OrdersService快1万行了，新逻辑在这里完成
 */
public interface OrderService extends IService<Orders> {
    /**
     * 下订单
     */
    void createMaterialOrder(List<ProductBuyInfoDTO> dtos, Plan plan);

    /**
     * 拆分子订单
     * @param mainOrder 主订单
     * @param orderItems 订单明细列表
     * @return 拆分后的子订单列表
     */
    List<Orders> splitSubOrders(Orders mainOrder, List<OrderItem> orderItems);

    /**
     * 拆分子订单项
     * @param subOrder 子订单
     * @param orderItems 原订单明细列表
     * @return 拆分后的子订单项列表
     */
    List<OrderItem> splitSubOrderItems(Orders subOrder, List<OrderItem> orderItems);

}
