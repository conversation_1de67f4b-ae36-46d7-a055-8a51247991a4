package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import scrbg.meplat.mall.config.auth.IsRole;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.config.logRecording.LogRecord;
import scrbg.meplat.mall.config.logRecording.enums.BusinessType;
import scrbg.meplat.mall.config.logRecording.enums.OperatorType;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.product.UpdateProductAndStateDTO;
import scrbg.meplat.mall.dto.product.UpdateProductStateDTO;
import scrbg.meplat.mall.dto.product.UpdateProductSupplierSubStateDTO;
import scrbg.meplat.mall.dto.product.material.*;
import scrbg.meplat.mall.entity.ErrorInfo;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.excelTemplate.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.ErrorInfoService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.util.excel.EasyExcelUtils;
import scrbg.meplat.mall.vo.product.ImportExcelResultVO;
import scrbg.meplat.mall.vo.product.ProductFullInfoVO;
import scrbg.meplat.mall.vo.product.material.MaterInfoVO;
import scrbg.meplat.mall.vo.product.website.material.WMaterialVO;
import scrbg.meplat.mall.vo.product.website.material.WMaterialnfoVO;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @描述：店铺商品信息控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@Log4j2
@RestController
@RequestMapping("/")
@ApiSort(value = 500)
@Api(tags = "店铺商品信息（后台）")
public class ProductController {

    @Autowired
    private ProductService productService;

    @Autowired
    ErrorInfoService errorInfoService;


    // ----------------------------通用------------------------------------------------------------------------------

    /**
     * 用于商品选择组件
     *
     * @param jsonObject
     * @return
     */
    @PostMapping("/product/listProductFullInfo")
    @ApiOperation(value = "获取商品信息（物资平台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productType", value = "商品类型：0物资  2周材（物资）", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称、店铺名称、规格名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "skuName", value = "规格名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "商品状态（0待上架 1已上架 2已下架）", dataTypeClass = List.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startPutawayDate", value = "上架开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endPutawayDate", value = "下架结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startModifiedDate", value = "修改开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endModifiedDate", value = "修改结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "isOneself", value = "是否自有物资", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按上架时间排序1:按排序值排序2:按创建时间排序3:按修改时间排序)", dataTypeClass = Integer.class),
    })
    public PageR<ProductFullInfoVO> listProductFullInfoVOPage(@RequestBody JSONObject jsonObject) {
        PageUtils page = productService.listProductFullInfoVOPage(jsonObject, Wrappers.lambdaQuery(Product.class));
        return PageR.success(page);
    }


    @PostMapping("/product/updateBatch")
    @ApiOperation(value = "批量修改（生成的接口）")
    public R updateBatch(@RequestBody List<Product> products) {
        productService.updateBatch(products);
        return R.success();
    }


    /**
     * 根据物资id获取物资商品信息(登录时)
     *
     * @param productId
     * @return
     */
    @GetMapping("product/materialInfo")
    @ApiOperation(value = "物资详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "商品ID", required = true,
                    dataType = "String", paramType = "query"),
    })
    public R materialInfo(String productId) {
        WMaterialnfoVO vo = productService.materialInfoLogin(productId);
        return R.success(vo);
    }


    @PostMapping("/product/updateProductState")
    @ApiOperation(value = "批量修改商品上下架状态（通用）")
    @LogRecord(title = "商品管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)

    public R updateProductState(@Valid @RequestBody UpdateProductStateDTO dto) {
        productService.updateProductState(dto);
        return R.success();
    }


    @PostMapping("product/materialPageList")
    @ApiOperation(value = "物资列表登录时查看")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productType", value = "商品类型：0物资  2周材（物资）", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "shopId", value = "店铺id", dataTypeClass = String.class),
            @DynamicParameter(name = "province", value = "省", dataTypeClass = String.class),
            @DynamicParameter(name = "city", value = "市", dataTypeClass = String.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称、店铺名称、规格名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "brandId", value = "品牌Id", dataTypeClass = String.class),
            @DynamicParameter(name = "isBusiness", value = "营业方式（0:全部1:平台自营2:路桥内部店3:其它）", dataTypeClass = Integer.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:综合排序1:按价格2:按更新时间", dataTypeClass = String.class, required = true),
    })
    public PageR<WMaterialVO> LogInmaterialPageList(@RequestBody JSONObject jsonObject) {
        PageUtils page = productService.materialPageList(jsonObject, Wrappers.lambdaQuery(Product.class));
        return PageR.success(page);
    }


    @PostMapping("product/getProductDetailDealRecord")
    @ApiOperation(value = "获取商品交易记录（登陆时）")
    public R<PageUtils> getProductDetailDealRecord(@RequestBody JSONObject jsonObject) {
        PageUtils vo = productService.getProductDetailDealRecord(jsonObject);
        return R.success(vo);
    }



    @PostMapping("/product/getMaterialInfo")
    @ApiOperation(value = "获取物资（通用）")
    public R<MaterInfoVO> getMaterialInfo(@RequestBody GetMaterialInfoDTO dto) {
        MaterInfoVO vo = productService.getMaterialInfo(dto);
        return R.success(vo);
    }

    @PostMapping("/supplier/product/getMaterialInfoSupplier")
    @ApiOperation(value = "获取物资（供应商查询专用）")
    public R<MaterInfoVO> getMaterialInfoSupplier(@RequestBody GetMaterialInfoDTO dto) {
        MaterInfoVO vo = productService.getMaterialInfoSupplier(dto);
        return R.success(vo);
    }

    @PostMapping("/product/getCheckMaterialInfo")
    @ApiOperation(value = "获取物资（检查物资详情用）")
    public R<MaterInfoVO> getCheckMaterialInfo(@RequestBody GetMaterialInfoDTO dto) {
        MaterInfoVO vo = productService.getCheckMaterialInfo(dto);
        return R.success(vo);
    }

    // ----------------------------店铺------------------------------------------------------------------------------
    // 通用
    @PostMapping("/shopManage/product/deleteBatch")
    @ApiOperation(value = "批量删除（通用）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "商品IDS", required = true,
                    dataType = "list", paramType = "query")
    })
    @LogRecord(title = "商品管理",businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)

    public R deleteBatch(@RequestBody List<String> ids) {
        productService.removeLogicBatch(ids);
        return R.success();
    }


    // 物资
    @PostMapping("/shopManage/product/listMaterialPage")
    @ApiOperation(value = "物资分页列表（店铺）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productType", value = "商品类型：0物资  2周材（物资）", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供方名称", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "serialNum", value = "商品编码", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "商品状态（0待上架 1已上架 2已下架）", dataTypeClass = List.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startPutawayDate", value = "上架开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endPutawayDate", value = "下架结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startModifiedDate", value = "修改开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endModifiedDate", value = "修改结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "isOneself", value = "是否自有物资", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按上架时间排序1:按排序值排序2:按创建时间排序3:按修改时间排序)", dataTypeClass = Integer.class),
    })
    @LogRecord(title = "商品管理",businessType = BusinessType.EXPORT,operatorType = OperatorType.MANAGE)

    public PageR<Product> listShopManageMaterialPage(@RequestBody JSONObject jsonObject) {
        PageUtils page = productService.listShopManageMaterialPage(jsonObject, Wrappers.lambdaQuery(Product.class));
        return PageR.success(page);
    }

    // 物资
    @PostMapping("/shopManage/product/putawayProductExport")
    @ApiOperation(value = "导出出售中的商品（店铺）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productType", value = "商品类型：0物资  2周材（物资）", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "商品状态（0待上架 1已上架 2已下架）", dataTypeClass = List.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startPutawayDate", value = "上架开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endPutawayDate", value = "下架结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startModifiedDate", value = "修改开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endModifiedDate", value = "修改结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "isOneself", value = "是否自有物资", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按上架时间排序1:按排序值排序2:按创建时间排序3:按修改时间排序)", dataTypeClass = Integer.class),
    })
    @LogRecord(title = "商品管理",businessType = BusinessType.EXPORT,operatorType = OperatorType.MANAGE)

    public R putawayProductExport(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        productService.putawayProductExport(jsonObject, response);
        return R.success();
    }

    @PostMapping("/shopManage/product/importBatchMaterial")
    @ApiOperation(value = "批量导入物资（店铺）")
    public R importBatchMaterial(@RequestBody List<ImportMaterialDTO> dtos) {
        productService.importBatchMaterial(dtos);
        return R.success();
    }

    @PostMapping("/shopManage/product/createMaterial")
    @ApiOperation(value = "新增物资（店铺）")
    @NotResubmit
    public R createMaterial(@RequestBody CreateMaterialDTO dto) {
        try {
            productService.createMaterial(dto);
        } catch (BusinessException e) {
            if (e.getCode() == 500085) {
                // 触发保存
                ErrorInfo errorInfo = new ErrorInfo();
                errorInfo.setBusinessType(1);
                errorInfo.setErrorRqJson(JSON.toJSONString(dto));
                errorInfo.setCreateTime(new Date());
                errorInfo.setIsDispose(0);
                errorInfo.setMethodName("createMaterial");
                errorInfo.setErrorInfo(e.getMessage());
                UserLogin user = ThreadLocalUtil.getCurrentUser();
                errorInfo.setUserId(user.getUserId());
                errorInfo.setUserName(user.getUserName());
                errorInfoService.save(errorInfo);
            }
            throw new BusinessException(e.getMessage());
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @PostMapping("/supplier/product/createMaterialSupplier")
    @ApiOperation(value = "新增物资（供应商）")
    @NotResubmit
    @Transactional
    @LogRecord(title = "商品管理",businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE,isSaveRequestData = false)

    public R createMaterialSupplier(@RequestBody CreateMaterialSupplierDTO dto) {
        try {
            productService.createMaterialSupplier(dto);
        } catch (BusinessException e) {
            if (e.getCode() == 500085) {
                // 触发保存
                ErrorInfo errorInfo = new ErrorInfo();
                errorInfo.setBusinessType(1);
                errorInfo.setErrorRqJson(JSON.toJSONString(dto));
                errorInfo.setCreateTime(new Date());
                errorInfo.setIsDispose(0);
                errorInfo.setMethodName("createMaterialSupplier");
                errorInfo.setErrorInfo(e.getMessage());
                UserLogin user = ThreadLocalUtil.getCurrentUser();
                errorInfo.setUserId(user.getUserId());
                errorInfo.setUserName(user.getUserName());
                errorInfoService.save(errorInfo);
            }
            throw new BusinessException(e.getMessage());
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @PostMapping("/supplier/product/onSubmitMaterialSupplier")
    @ApiOperation(value = "新增物资（供应商）")
    @NotResubmit
    @LogRecord(title = "商品管理",businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE,isSaveRequestData = false)

    public R onSubmitMaterialSupplier(@RequestBody CreateMaterialSupplierDTO dto) {
        productService.onSubmitMaterialSupplier(dto);
        return R.success();
    }

    @PostMapping("/shopManage/product/updateMaterial")
    @NotResubmit
    @ApiOperation(value = "修改物资（店铺）")
    @LogRecord(title = "商品管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE,isSaveRequestData = false)

    public R updateMaterial(@RequestBody UpdateMaterialDTO dto) {
        try {
            productService.updateMaterial(dto);
        } catch (BusinessException e) {
            if (e.getCode() == 500085) {
                // 触发保存
                ErrorInfo errorInfo = new ErrorInfo();
                errorInfo.setBusinessType(1);
                errorInfo.setErrorRqJson(JSON.toJSONString(dto));
                errorInfo.setCreateTime(new Date());
                errorInfo.setIsDispose(0);
                errorInfo.setMethodName("updateMaterial");
                errorInfo.setErrorInfo(e.getMessage());
                UserLogin user = ThreadLocalUtil.getCurrentUser();
                errorInfo.setUserId(user.getUserId());
                errorInfo.setUserName(user.getUserName());
                errorInfoService.save(errorInfo);
            }
            throw new BusinessException(e.getMessage());
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }

    @PostMapping("/supplier/product/updateMaterialSupplier")
    @ApiOperation(value = "修改物资（供应商）")
    @LogRecord(title = "商品管理",businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE,isSaveRequestData = false)

    public R updateMaterialSupplier(@RequestBody UpdateMaterialSupplierDTO dto) {
        try {
            productService.updateMaterialSupplier(dto);
        } catch (BusinessException e) {
            if (e.getCode() == 500085) {
                // 触发保存
                ErrorInfo errorInfo = new ErrorInfo();
                errorInfo.setBusinessType(1);
                errorInfo.setErrorRqJson(JSON.toJSONString(dto));
                errorInfo.setCreateTime(new Date());
                errorInfo.setIsDispose(0);
                errorInfo.setMethodName("updateMaterialSupplier");
                errorInfo.setErrorInfo(e.getMessage());
                UserLogin user = ThreadLocalUtil.getCurrentUser();
                errorInfo.setUserId(user.getUserId());
                errorInfo.setUserName(user.getUserName());
                errorInfoService.save(errorInfo);
            }
            throw new BusinessException(e.getMessage());
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        return R.success();
    }


    // 设备
    @PostMapping("/shopManage/product/listDevicePage")
    @ApiOperation(value = "设备分页列表（店铺）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productTypes", value = "商品类型： 1设备 2周材 3周材（设备）4二手设备 5租赁设备", required = true,
                    dataTypeClass = List.class),
            @DynamicParameter(name = "productType", value = "商品类型：1设备 3周材（设备） 4二手设备 5租赁设备 6维修服务 7金融服务 8保险服务", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "商品状态（0待上架 1已上架 2已下架）", dataTypeClass = List.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startPutawayDate", value = "上架开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endPutawayDate", value = "下架结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startModifiedDate", value = "修改开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endModifiedDate", value = "修改结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "isOneself", value = "是否自有设备", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按上架时间排序1:按排序值排序2:按创建时间排序3:按修改时间排序)", dataTypeClass = Integer.class),
    })
    public PageR<Product> listShopManageDevicePage(@RequestBody JSONObject jsonObject) {
        PageUtils page = productService.listShopManageDevicePage(jsonObject, Wrappers.lambdaQuery(Product.class));
        return PageR.success(page);
    }


    // ----------------------------平台------------------------------------------------------------------------------

    @PostMapping("/platform/product/listMaterialPage")
    @ApiOperation(value = "物资分页列表（平台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productType", value = "商品类型：0物资  2周材（物资）", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "serialNum", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "商品状态（0待上架 1已上架 2已下架）", dataTypeClass = List.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startPutawayDate", value = "上架开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endPutawayDate", value = "下架结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startModifiedDate", value = "修改开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endModifiedDate", value = "修改结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按上架时间排序1:按排序值排序2:按创建时间排序3:按修改时间排序)", dataTypeClass = Integer.class),
    })
    public PageR listPlatformMaterialPage(@RequestBody JSONObject jsonObject) {
        PageUtils page = productService.listPlatformMaterialPage(jsonObject, Wrappers.lambdaQuery(Product.class));
        return PageR.success(page);
    }

    @PostMapping("/platform/product/listMaterialPagePVP")
    @ApiOperation(value = "物资分页列表（平台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productType", value = "商品类型：0物资  2周材（物资）", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "serialNum", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "商品状态（0待上架 1已上架 2已下架）", dataTypeClass = List.class),
            @DynamicParameter(name = "isOpenImport", value = "是否外部导入商品", dataTypeClass = List.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startPutawayDate", value = "上架开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endPutawayDate", value = "下架结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startModifiedDate", value = "修改开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endModifiedDate", value = "修改结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPriceAve", value = "以下价格（商品均价最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePriceAve", value = "以上价格（商品均价最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按上架时间排序1:按排序值排序2:按创建时间排序3:按修改时间排序)", dataTypeClass = Integer.class),
    })
    public PageR listPlatformMaterialPagePVP(@RequestBody JSONObject jsonObject) {
        PageUtils page = productService.listPlatformMaterialPagePVP(jsonObject, Wrappers.lambdaQuery(Product.class));
        return PageR.success(page);
    }

    @PostMapping("/platform/product/outputExcel")
    @ApiOperation(value = "导出商品信息数据库")
    public R outputExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        productService.outputExcel(jsonObject, response);
        return R.success();
    }


    @PostMapping("/platform/product/allProductStatePass")
    @ApiOperation(value = "根据当前查询条件全部通过商品（平台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productType", value = "商品类型：0物资  2周材（物资）", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "商品状态（0待上架 1已上架 2已下架）", dataTypeClass = List.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startPutawayDate", value = "上架开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endPutawayDate", value = "下架结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startModifiedDate", value = "修改开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endModifiedDate", value = "修改结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按上架时间排序1:按排序值排序2:按创建时间排序3:按修改时间排序)", dataTypeClass = Integer.class),
    })
    public R allProductStatePass(@RequestBody JSONObject jsonObject) {
        productService.allProductStatePass(jsonObject);
        return R.success();
    }



    @PostMapping("/product/getProductCountList")
    @ApiOperation(value = "商品分页统计列表（商品统计商铺平台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "isShop", value = "是否店铺统计", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "商品类型", dataTypeClass = Integer.class),
    })
    public PageR getSupplierProductCountList(@RequestBody JSONObject jsonObject) {
        PageUtils page = productService.getProductCountList(jsonObject, Wrappers.lambdaQuery(Product.class));
        return PageR.success(page);
    }

    @PostMapping("/product/getProductCountListExcel")
    @ApiOperation(value = "商品分页统计列表（商品统计商铺平台）(导出)")
    @DynamicParameters(name = "根据实体属性查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "isShop", value = "是否店铺统计", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "商品类型", dataTypeClass = Integer.class),
    })
    public R getSupplierProductCountListExcel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        productService.getProductCountListExcel(jsonObject,response);
        return R.success();
    }

    // ----------------------------其他服务------------------------------------------------------------------------------

    /**
     * 下载Excel模板
     */
    @GetMapping("/product/excel/template")
    @ApiOperation(value = "下载模板")
    @NotResubmit
    public void downloadTemplate(@RequestParam Integer productType, HttpServletResponse response) {
        try {
            if (productType == 0) {
                EasyExcelUtils.writeWeb("物资模板", Material.class, null, "物资模板", response);
            }
            if (productType == 1) {
                EasyExcelUtils.writeWeb("临购物资模板", LcMaterial.class, null, "临购物资模板", response);
            }
            if (productType == 6) {
                EasyExcelUtils.writeWeb("物资基础库模板", MaterialDtlInfos.class, null, "物资基础库模板", response);
            }
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
        }
    }

    /**
     *

     * @param response
     */
    @PostMapping ("/product/excel/zoneTemplate")
    @ApiOperation(value = "下载销售区域模板")
    @NotResubmit
    public void downloadzoneTemplate(@RequestBody  JSONObject jsonObject , HttpServletResponse response) {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        ArrayList ids = (ArrayList) jsonObject.get("ids");
        Integer shopType = (Integer) jsonObject.get("shopType");
        try {
            if (ids != null && ids.size() > 0) {
                List<Product> list = productService.lambdaQuery().in(Product::getProductId,ids).list();
                ArrayList<ZoneMaterial> zoneMaterials = new ArrayList<>();
                Long i= 0L;
                for (Product product : list) {
                    if (product.getSupperBy().equals(enterpriseId)){
                        ZoneMaterial zoneMaterial = new ZoneMaterial();
                        zoneMaterial.setId(++i);
                        zoneMaterial.setProductSn(product.getSerialNum());
                        zoneMaterials.add(zoneMaterial);
                    }
                }
                EasyExcelUtils.writeWeb("物资模板", ZoneMaterial.class, zoneMaterials, "物资模板", response);
            }else {
                if (shopType==1){
                    EasyExcelUtils.writeWeb("物资模板", ZoneMaterial.class, null, "物资模板", response);
                }else {
                    EasyExcelUtils.writeWeb("物资模板", ZoneMaterial.class, null, "物资模板", response);
                }

            }
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
        }
    }

    @GetMapping("/supplier/product/supplierExcelTemplate")
    @ApiOperation(value = "供方商品导入下载模板")
    @NotResubmit
    public void supplierExcelTemplate(HttpServletResponse response) {
        try {
            EasyExcelUtils.writeWeb("供方物资模板", ShopMaterial.class, null, "供方物资模板", response);
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
        }
    }

    @GetMapping("/supplier/product/supplierLcExcelTemplate")
    @ApiOperation(value = "供方临购商品导入下载模板")
    @NotResubmit
    public void supplierLcExcelTemplate(HttpServletResponse response) {
        try {
            EasyExcelUtils.writeWeb("供方临购物资模板", LcShopMaterial.class, null, "供方临购物资模板", response);
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
        }
    }


    @PostMapping("/product/uploadExcelFile")
    @ApiOperation(value = "导入商品")
    @NotResubmit
    public R uploadExcelFile(@RequestPart("file") MultipartFile file, Integer productType) {
        List<ImportExcelResultVO> vos = productService.uploadExcelFile(file, productType);
        return R.success(vos);
    }


    @PostMapping("/supplier/product/supplierUploadExcelFile")
    @ApiOperation(value = "供方商品Excel导入")
    @NotResubmit
    public R supplierUploadExcelFile(@RequestPart("file") MultipartFile file, String shopId) {
        List<ImportExcelResultVO> vos = productService.supplierUploadExcelFile(file, shopId);
        return R.success(vos);
    }

    @PostMapping("/supplier/product/shopUploadProductMallExcelFile")
    @ApiOperation(value = "店铺商品Excel导入")
    @NotResubmit
    public R supplierUploadProductMallExcelFile(@RequestPart("file") MultipartFile file) {
        List<ImportExcelResultVO> vos = productService.shopUploadProductMallExcelFile(file);
        return R.success(vos);
    }


    @PostMapping("/supplier/product/shopUploadProductMallZoneSupplierExcelFile")
    @ApiOperation(value = "供方临购商品Excel导入")
    @NotResubmit
    public R shopUploadProductMallZoneSupplierExcelFile(@RequestPart("file") MultipartFile file, String shopId) {
        List<ImportExcelResultVO> vos = productService.shopUploadProductMallZoneExcelFile(file, shopId);
        return R.success(vos);
    }

    @PostMapping("/supplier/product/shopUploadLcProductMallExcelFile")
    @ApiOperation(value = "店铺临购商品Excel导入")
    @NotResubmit
    public R shopUploadLcProductMallExcelFile(@RequestPart("file") MultipartFile file) {
        List<ImportExcelResultVO> vos = productService.shopUploadLcProductMallExcelFile(file);
        return R.success(vos);
    }

    @PostMapping("/supplier/product/supplierUploadLcExcelFile")
    @ApiOperation(value = "供方临购商品Excel导入")
    @NotResubmit
    public R supplierUploadLcExcelFile(@RequestPart("file") MultipartFile file, String shopId) {
        List<ImportExcelResultVO> vos = productService.supplierUploadLcExcelFile(file, shopId);
        return R.success(vos);
    }

    @PostMapping("/supplier/product/listMaterialSupplier")
    @ApiOperation(value = "物资店铺查询供方商品（供应商）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productType", value = "商品类型：0物资  2周材（物资）", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "商品状态（0待上架 1已上架 2已下架）", dataTypeClass = List.class),
            @DynamicParameter(name = "supplierSubmitStates", value = "提交状态（0默认1待提交2待确认3已确认4已拒绝）供方使用，", dataTypeClass = List.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startPutawayDate", value = "上架开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endPutawayDate", value = "下架结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startModifiedDate", value = "修改开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endModifiedDate", value = "修改结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按上架时间排序1:按排序值排序2:按创建时间排序3:按修改时间排序)", dataTypeClass = Integer.class),
    })
    public PageR listMaterialSupplier(@RequestBody JSONObject jsonObject) {
        PageUtils page = productService.listMaterialSupplier(jsonObject, Wrappers.lambdaQuery(Product.class));
        return PageR.success(page);
    }

    @PostMapping("/supplier/product/listMaterialSupplierExport")
    @ApiOperation(value = "物资店铺查询供方商品（供应商）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productType", value = "商品类型：0物资  2周材（物资）", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "商品状态（0待上架 1已上架 2已下架）", dataTypeClass = List.class),
            @DynamicParameter(name = "supplierSubmitStates", value = "提交状态（0默认1待提交2待确认3已确认4已拒绝）供方使用，", dataTypeClass = List.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startPutawayDate", value = "上架开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endPutawayDate", value = "下架结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startModifiedDate", value = "修改开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endModifiedDate", value = "修改结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按上架时间排序1:按排序值排序2:按创建时间排序3:按修改时间排序)", dataTypeClass = Integer.class),
    })
    public R listMaterialSupplierExport(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        productService.listMaterialSupplierExport(jsonObject, response);
        return R.success("商品导出成功");
    }


    // FIXME 这里是查询待确认的供方商品，如果这里的条件变化，全部修改通过里面的条件也要增加

    @PostMapping("/shopManage/product/listMaterialSupplierAffirm")
    @ApiOperation(value = "查询供方提供的商品（店铺）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productType", value = "商品类型：0物资  2周材（物资）", required = true,
                    dataTypeClass = Integer.class),
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "商品状态（0待上架 1已上架 2已下架）", dataTypeClass = List.class),
            @DynamicParameter(name = "supplierSubmitStates", value = "提交状态（0默认1待提交2待确认3已确认4已拒绝）供方使用，", dataTypeClass = List.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startPutawayDate", value = "上架开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endPutawayDate", value = "下架结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startModifiedDate", value = "修改开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endModifiedDate", value = "修改结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按上架时间排序1:按排序值排序2:按创建时间排序3:按修改时间排序)", dataTypeClass = Integer.class),
    })
    public PageR listMaterialSupplierAffirm(@RequestBody JSONObject jsonObject) {
        PageUtils page = productService.listMaterialSupplierAffirm(jsonObject, Wrappers.lambdaQuery(Product.class));
        return PageR.success(page);
    }

    @PostMapping("/supplier/product/updateProductSupplierSubState")
    @ApiOperation(value = "批量修改供方提交状态")
    @NotResubmit
    public R updateProductSupplierSubState(@Valid @RequestBody UpdateProductSupplierSubStateDTO dto) {
        productService.updateProductSupplierSubState(dto);
        return R.success();
    }

    @PostMapping("/shopManage/product/batchAffirmSupplierProduct")
    @ApiOperation(value = "确认全部商品（店铺）")
    @DynamicParameters(name = "根据查询条件批量确认", properties = {
            @DynamicParameter(name = "productType", value = "商品类型：0物资  2周材（物资）", required = true,
                    dataTypeClass = Integer.class),
            // 不要分页
//            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
//            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "keywords", value = "关键字（商品名称）", dataTypeClass = String.class),
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopName", value = "店铺名称", dataTypeClass = String.class),
            @DynamicParameter(name = "supplierName", value = "供应商名称", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "state", value = "商品状态（0待上架 1已上架 2已下架）", dataTypeClass = List.class),
            @DynamicParameter(name = "supplierSubmitStates", value = "提交状态（0默认1待提交2待确认3已确认4已拒绝）供方使用，", dataTypeClass = List.class),
            @DynamicParameter(name = "startCreateDate", value = "创建开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "创建结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startPutawayDate", value = "上架开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endPutawayDate", value = "下架结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "startModifiedDate", value = "修改开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endModifiedDate", value = "修改结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "belowPrice", value = "以下价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "abovePrice", value = "以上价格（商品最低价）", dataTypeClass = String.class),
            @DynamicParameter(name = "orderBy", value = "排序字段(0:按上架时间排序1:按排序值排序2:按创建时间排序3:按修改时间排序)", dataTypeClass = Integer.class),
            @DynamicParameter(name = "updateState", value = "提交状态（0默认1待提交2待确认3已确认4已拒绝）供方使用", dataTypeClass = Integer.class),
            @DynamicParameter(name = "updateFailReason", value = "拒绝原因", dataTypeClass = Integer.class),
    })
    @NotResubmit
    public R batchAffirmSupplierProduct(@RequestBody JSONObject jsonObject) {
        productService.batchAffirmSupplierProduct(jsonObject, Wrappers.lambdaQuery(Product.class));
        return R.success();
    }

    @PostMapping("/shopManage/product/updateMaterialAndState")
    @ApiOperation(value = "修改物资并上架（店铺）")
    public R updateMaterialAndState(@RequestBody UpdateProductAndStateDTO dto) {
        productService.updateMaterialAndState(dto);
        return R.success();
    }

    @PostMapping("/product/updateProductJcState")
    @ApiOperation(value = "修改商品加成率")
    @NotResubmit
    public R updateProductJcState(@RequestBody Product product) {
        productService.updateProductJcState(product);
        return R.success();
    }

    @PostMapping("/platform/product/getStockUpInfo")
    @ApiOperation(value = "一键铺货查询商品信息")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "productName", value = "商品名称", dataTypeClass = String.class),
            @DynamicParameter(name = "shopId", value = "店铺id", dataTypeClass = String.class),
            @DynamicParameter(name = "classId", value = "分类ID", dataTypeClass = String.class),
            @DynamicParameter(name = "serialNum", value = "分类ID", dataTypeClass = String.class),
    })
    public PageR<Product> getStockUpInfo(@RequestBody JSONObject jsonObject) {
        PageUtils<Product> page = productService.getStockUpInfo(jsonObject);
        return PageR.success(page);
    }
    @PostMapping("/platform/product/oneClickStockUp")
    @ApiOperation(value = "一键铺货")
    public PageR<Product> oneClickStockUp(@RequestBody List<String> ids) {
        productService.oneClickStockUp(ids);
        return PageR.success();
    }

    @PostMapping("/product/updateProductStock")
    @ApiOperation(value = "修改商品库存")
    @NotResubmit
    public R updateProductStock(@RequestBody Product product) {
        productService.updateProductStock(product);
        return R.success();
    }
    @PostMapping("/product/operand")
    @ApiOperation(value = "商品操作数统计（后台）")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "startCreateDate", value = "完成时间开始时间", dataTypeClass = String.class),
            @DynamicParameter(name = "endCreateDate", value = "完成时间结束时间", dataTypeClass = String.class),
            @DynamicParameter(name = "productType", value = "商品类型", dataTypeClass = Integer.class),
            @DynamicParameter(name = "isShop", value = "是否店铺统计", dataTypeClass = Integer.class),
    })
    @IsRole(roleName = RoleEnum.ROLE_11)
    public PageR getOperand(@RequestBody JSONObject jsonObject) {
        PageUtils page = productService.statisticsByShopAndProductTypeWithSupplierPage(jsonObject);
        return PageR.success(page);
    }
}

