package scrbg.meplat.mall.service;

import scrbg.meplat.mall.entity.MaterialReconciliation;
import scrbg.meplat.mall.entity.MaterialReconciliationDtl;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.MaterialReconciliationDtl;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @描述：物资验收明细 服务类
 * @作者: ye
 * @日期: 2023-07-26
 */
public interface MaterialReconciliationDtlService extends IService<MaterialReconciliationDtl> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliationDtl> queryWrapper);

        void create(MaterialReconciliationDtl materialReconciliationDtl);

        void update(MaterialReconciliationDtl materialReconciliationDtl);

        MaterialReconciliationDtl getById(String id);

        void delete(String id);



        PageUtils ListByBillIds(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliationDtl> materialReconciliationDtlLambdaQueryWrapper);

        PageUtils materialReconciliationLedger(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper);

        PageUtils materialReconciliationOrgLedger(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper);

        PageUtils materialReconciliationLedgerSupplier(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper);


        void materialReconciliationLedgerExcel(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper, HttpServletResponse response);

        void materialReconciliationLedgerSupplierExcel(JSONObject jsonObject, LambdaQueryWrapper<MaterialReconciliation> materialReconciliationLambdaQueryWrapper, HttpServletResponse response);
}
