package scrbg.meplat.mall.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.entity.system.SysUser;

/**
 * <AUTHOR>
 * @create 2023-01-18 9:45
 */
@Data
public class UserShopEnterpriseInfoVO {

    @ApiModelProperty(value = "用户信息")
    private User user;

    @ApiModelProperty(value = "用户信息")
    private SysUser sysUser;

    @ApiModelProperty(value = "店铺")
    private Shop shop;

    @ApiModelProperty(value = "企业信息")
    private EnterpriseInfo enterpriseInfo;
}
