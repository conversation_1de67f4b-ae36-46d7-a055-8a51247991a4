package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.PcwpUser;
import scrbg.meplat.mall.entity.pcwpmq.PcwpOrginfos;
import scrbg.meplat.mall.entity.pcwpmq.PcwpPersonPermissions;

import java.util.List;

public interface PcwpUserService extends IService<PcwpUser> {

    PageUtils getInUserLedger(JSONObject jsonObject);
    PageUtils getInUserLedgerByOrg(JSONObject jsonObject);

    PcwpOrginfos selectUserCurrentOrgInfo(String shortCode);

    List<PcwpOrginfos> getChildrenOrgList(String userNumber);
}
