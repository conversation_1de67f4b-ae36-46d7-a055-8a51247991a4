package scrbg.meplat.mall.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import scrbg.meplat.mall.entity.MaterialReconciliationDtl;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;
import org.apache.ibatis.annotations.Mapper;
import scrbg.meplat.mall.vo.platform.ListShipByAffirmListVO;
import scrbg.meplat.mall.vo.platform.ReconciliationLedgerListVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @描述：物资验收明细 Mapper 接口
 * @作者: ye
 * @日期: 2023-07-26
 */
@Mapper
@Repository
public interface MaterialReconciliationDtlMapper extends BaseMapper<MaterialReconciliationDtl> {


    @Select("SELECT\n" +
            "\tmaterial_class_id,\n" +
            "\tmaterial_class_name,\n" +
            "\tmaterial_id,\n" +
            "\tmaterial_name,\n" +
            "\tspec,\n" +
            "\tunit,\n" +
            "\torder_id,\n" +
            "\torder_sn,\n" +
            "\ttexture,\n" +
            "\ttax_amount,\n" +
            "\twarehouse_id,\n" +
            "\twarehouse_name,\n" +
            "\tsum( quantity ) AS quantity,\n" +
            "\tsum( acceptance_amount ) AS acceptance_amount, \n" +
            "\tsum( acceptance_no_rate_amount ) AS acceptance_no_rate_amount \n" +
            "FROM\n" +
            "\tmaterial_reconciliation_dtl \n" +
            "WHERE reconciliation_id = #{reconciliationId}\n" +
            "GROUP BY\n" +
            "\tmaterial_id,\n" +
            "\tmaterial_name,\n" +
            "\tspec,\n" +
            "\tunit,\n" +
            "\ttexture, \n" +
            "\twarehouse_id, \n" +
            "\twarehouse_name\n"
    )
    List<MaterialReconciliationDtl> getDtlCountListByReconciliationId(String reconciliationId);

    // 台账相关查询
    int listLedgerCount(@Param("dto") Map<String, Object> dto);

    List<ReconciliationLedgerListVo> ledgerList(Page<ReconciliationLedgerListVo> pages, @Param("dto") Map<String, Object> dto);
    List<ReconciliationLedgerListVo> ledgerList( @Param("dto") Map<String, Object> dto);
    List<ReconciliationLedgerListVo> ledgerListExcel(@Param("dto") Map<String, Object> dto);

    BigDecimal selCountAmount(@Param("dto") Map<String, Object> dto);

    BigDecimal selCountNoRateAmount(@Param("dto") Map<String, Object> dto);
}