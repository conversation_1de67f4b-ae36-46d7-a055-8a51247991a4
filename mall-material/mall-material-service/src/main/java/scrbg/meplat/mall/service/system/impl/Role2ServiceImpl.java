package scrbg.meplat.mall.service.system.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.scrbg.common.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.dto.system.SysRole2DTO;
import scrbg.meplat.mall.entity.system.SysJg;
import scrbg.meplat.mall.entity.system.SysRole2;
import scrbg.meplat.mall.mapper.system.Role2Mapper;
import scrbg.meplat.mall.mapper.system.RoleMenu2Mapper;
import scrbg.meplat.mall.service.system.Role2Service;
import scrbg.meplat.mall.util.DateUtil;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;

import java.util.Date;
import java.util.List;

@Service
public class Role2ServiceImpl implements Role2Service{

    @Autowired
    private Role2Mapper roleMapper;

    @Autowired
    private RoleMenu2Mapper roleMenuMapper;

    @Override
    public PageUtils<SysRole2> getRoleList(SysRole2DTO role) {
        // 设置分页参数（与前端page/limit对应）
        Integer pageNum = role.getPage() != null ? role.getPage() : 1;
        Integer pageSize = role.getLimit() != null ? role.getLimit() : 10;

        // 计算起始行（用于XML中的LIMIT）
        Integer offset = (pageNum - 1) * pageSize;
        role.setOffset(offset);
        role.setPageSize(pageSize);

        // 执行XML查询
        List<SysRole2> list = roleMapper.selectRoleList(role);
        int total = roleMapper.countRoleList(role);

        // 构建PageUtils（与图片1表格需求匹配）
        return new PageUtils<>(list, total, pageSize, pageNum);
    }

    @Override
    @Transactional
    public boolean addRole(SysRole2DTO role) {
        UserLogin userLogin = ThreadLocalUtil.getCurrentUser();
        String roleId = IdWorker.getIdStr();
        role.setRoleId(roleId);
        role.setGmtCreate(DateUtil.getyyyymmddHHmmss(new Date()));
        role.setFounderId(userLogin.getUserId());
        role.setFounderName(userLogin.getUserName());
        role.setIsDelete(0);
        int result = roleMapper.insertRole(role);

        // 设置角色菜单权限
        if (role.getMenuIds() != null && !role.getMenuIds().isEmpty()) {
            setRoleMenus(role.getRoleId(), role.getMenuIds());
        }

        return result > 0;
    }

    @Override
    @Transactional
    public boolean updateRole(SysRole2DTO role) {
        role.setGmtModified(DateUtil.getyyyymmddHHmmss(new Date()));

        int result = roleMapper.updateRole(role);
        // 更新角色菜单权限
        if (role.getMenuIds() != null) {
            setRoleMenus(role.getRoleId(), role.getMenuIds());
        }

        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteRole(String roleId) {
        // 逻辑删除角色
        UserLogin userLogin = ThreadLocalUtil.getCurrentUser();
        String modifyTime = DateUtil.getyyyymmddHHmmss(new Date());
        String modifyUserId = userLogin.getUserId();
        String modifyUserName = userLogin.getUserName();
        int result = roleMapper.deleteRole(roleId,modifyTime,modifyUserId,modifyUserName);
        // 删除角色关联的菜单权限
        roleMenuMapper.deleteByRoleId(roleId,modifyTime,modifyUserId,modifyUserName);
        return result > 0;
    }

    @Override
    public SysRole2 getRoleDetail(String roleId) {
        SysRole2 role = roleMapper.selectRoleById(roleId);
        if (role != null) {
            // 查询角色关联的菜单ID列表
            List<String> menuIds = roleMenuMapper.selectMenuIdsByRoleId(roleId);
            role.setMenuIds(menuIds);
        }
        return role;
    }

    @Override
    @Transactional
    public boolean setRoleMenus(String roleId, List<String> menuIds) {
        // 先删除原有权限
        UserLogin userLogin = ThreadLocalUtil.getCurrentUser();
        String founderId = userLogin.getUserId();
        String founderName = userLogin.getUserName();
        String modifyTime = DateUtil.getyyyymmddHHmmss(new Date());

        roleMenuMapper.deleteByRoleId(roleId, modifyTime, founderId, founderName);

        // 批量插入新权限
        if (menuIds != null && !menuIds.isEmpty()) {

            return roleMenuMapper.batchInsertRoleMenus(roleId, menuIds,modifyTime,founderId,founderName) > 0;
        }
        return true;
    }

    @Override
    public List<SysJg> getAllJglist() {
        return roleMapper.getAllJglist();
    }

    @Override
    public List<SysRole2> getRoleListAll(SysRole2DTO role) {
        return roleMapper.getRoleListAll();
    }
}
