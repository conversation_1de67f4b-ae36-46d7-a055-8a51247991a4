package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.UserAddress;
import scrbg.meplat.mall.service.UserAddressService;
import scrbg.meplat.mall.util.ThreadLocalUtil;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @描述：用户地址控制类
 * @作者: y
 * @日期: 2022-11-02
 */
@RestController
@RequestMapping("/userAddress")
@ApiSort(value = 500)
@Api(tags = "用户地址")
public class UserAddressController {

    @Autowired
    public UserAddressService userAddressService;

    @PostMapping("/listByEntity")
    @ApiOperation(value = "根据实体属性分页查询")
    @DynamicParameters(name = "根据实体属性分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class)
    })
    public PageR<UserAddress> listByEntity(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        PageUtils page = userAddressService.queryPage(jsonObject, new LambdaQueryWrapper<UserAddress>(), userId);
        return PageR.success(page);
    }

    @GetMapping("/findById")
    @ApiOperation(value = "根据主键查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R<UserAddress> findById(String id) {
        UserAddress userAddress = userAddressService.getById(id);
        return R.success(userAddress);
    }


    @GetMapping("/isDefaultAddress")
    @ApiOperation(value = "当前机构是否有默认地址")
    public R<Integer> isDefaultAddress() {
        int count = userAddressService.isDefaultAddress();
        return R.success(count);
    }

    /**
     * 给对应用户添加地址
     *
     * @param userAddress
     * @return
     */
    @PostMapping("/createByUserId")
    @ApiOperation(value = "根据userId新增地址")
    public R createByUserId(@RequestBody UserAddress userAddress) {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        userAddress.setUserId(userId);
        userAddress.setEnterpriseId(enterpriseId);
        userAddressService.create(userAddress, new LambdaQueryWrapper<>());
        return R.success();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public R update(@RequestBody UserAddress userAddress) {
        userAddressService.update(userAddress);
        return R.success();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "根据主键删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", required = true,
                    dataType = "String", paramType = "query")
    })
    public R delete(String id) {
        userAddressService.delete(id);
        return R.success();
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "根据主键批量删除")

    public R deleteBatch(@RequestBody List<String> ids) {
        userAddressService.removeByIds(ids);
        return R.success();
    }

    @PostMapping("/setDefaultAddress")
    @ApiOperation(value = "设置默认地址")
    public R setDefaultAddress(@RequestBody UserAddress userAddress) {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        userAddress.setUserId(userId);
        userAddressService.setDefaultAddress(userAddress, new LambdaQueryWrapper<>());
        return R.success();
    }
}

