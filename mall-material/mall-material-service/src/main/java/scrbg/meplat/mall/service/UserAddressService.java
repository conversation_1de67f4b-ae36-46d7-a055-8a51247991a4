package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.entity.UserAddress;

/**
 * @描述：用户地址 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface UserAddressService extends IService<UserAddress> {
    PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<UserAddress> queryWrapper, String userId);

    /**
     * Description:  根据userId进行新增
     */
    R create(UserAddress userAddress, LambdaQueryWrapper<UserAddress> queryWrapper);

    void update(UserAddress userAddress);

    UserAddress getById(String id);

    void delete(String id);

    R setDefaultAddress(UserAddress userAddress,LambdaQueryWrapper<UserAddress> queryWrapper);

    UserAddress getDefaultAddress();

    String getCityByAddress(String receiverAddress);

    int isDefaultAddress();


}
