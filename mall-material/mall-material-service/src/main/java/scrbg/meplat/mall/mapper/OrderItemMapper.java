package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.scrbg.common.utils.PageUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.vo.order.CetHistoryOrderItem;
import scrbg.meplat.mall.vo.platform.PlatformOrdersCountVO;
import scrbg.meplat.mall.vo.platform.TransactionProductVo;
import scrbg.meplat.mall.vo.product.order.OrderItemVo;
import scrbg.meplat.mall.vo.product.website.ProductDetailDealRecordVO;
import scrbg.meplat.mall.vo.shopManage.reportForms.PlatformProductFromVo;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @描述：订单项 Mapper 接口
 * @作者: y
 * @日期: 2022-11-02
 */
@Mapper
@Repository
public interface OrderItemMapper extends BaseMapper<OrderItem> {

    /**
     * 获取商品交易记录
     *
     * @param pages
     * @param innerMap
     * @return
     */
    List<ProductDetailDealRecordVO> getProductDetailDealRecord(Page<ProductDetailDealRecordVO> pages, @Param("dto") Map<String, Object> innerMap);

    /**
     * 获取商品交易记录
     * @param innerMap
     * @return
     */
    int getProductDetailDealRecordCount(@Param("dto") Map<String, Object> innerMap);


    List<OrderItemVo> orderItemListByOrderId(PageUtils<OrderItemVo> pageUtils,@Param("ew") QueryWrapper<OrderItemVo> orderItemVoQueryWrapper);

    List<OrderItem> getPlatformOrderItemCount(IPage<OrderItem> pages, @Param("ew") QueryWrapper<OrderItem> q);
    List<OrderItem> getPlatformOrderItemCountExcel(@Param("ew") QueryWrapper<OrderItem> q);

    List<PlatformOrdersCountVO> getPlatformWeekOrderItemCount(@Param("dto") Map<String, Object> innerMap);

    List<TransactionProductVo> selectAllTransactionProduct(Page<TransactionProductVo> pages,@Param("ew") QueryWrapper<PlatformProductFromVo> wrapper);
    BigDecimal selectAllTransactionProductAmount(@Param("ew") QueryWrapper<PlatformProductFromVo> wrapper);
    BigDecimal selectAllTransactionProductNoRateAmount(@Param("ew") QueryWrapper<PlatformProductFromVo> wrapper);

    List<CetHistoryOrderItem> findAllHistoryOrderItem(Page<CetHistoryOrderItem> pages, @Param("ew") QueryWrapper<CetHistoryOrderItem> wrapper);


    List<HashMap<String, Object>> selectProductSoldNumBySn();
}
