<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ShopMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.Shop" id="ShopMap">
        <result property="shopId" column="shop_id"/>
        <result property="orgId" column="org_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="shopBalance" column="shop_balance"/>
        <result property="shopFreezeMoney" column="shop_freeze_money"/>
        <result property="shopType" column="shop_type"/>
        <result property="shopImg" column="shop_img"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="county" column="county"/>
        <result property="detailedAddress" column="detailed_address"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="state" column="state"/>
        <result property="shopDescrible" column="shop_describle"/>
        <result property="enterpriseId" column="enterprise_id"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="isBusiness" column="is_business"/>
        <result property="isSupplier" column="is_supplier"/>
        <result property="isInternalShop" column="is_internal_shop"/>
        <result property="isInternalSettlement" column="is_internal_settlement"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="mallType" column="mall_type"/>
    </resultMap>

	<select id="getIndexSupplierList" resultType="scrbg.meplat.mall.entity.Shop"
		parameterType="Map">
        SELECT s.shop_id,s.shop_name,s.shop_img,s.open_date,s.city,s.main_business from shop_supplier_rele ss LEFT JOIN shop s on ss.supplier_id = s.enterprise_id
		<where>
			ss.is_delete = 0 and ss.shop_id = '1878734518961074177' and s.shop_id is not null
            and s.state = 1
            <if test="dto.keywords != null and dto.keywords != ''">
                and s.shop_name LIKE CONCAT('%',#{dto.keywords},'%')
            </if>
            <if test="dto.area != null and dto.area != ''">
                and s.`city` = #{dto.area}
            </if>
            <if test="dto.initial != null and dto.initial != ''">
                and s.`initial` = #{dto.initial}
            </if>
        </where>
        GROUP BY s.shop_name, s.shop_id
    </select>

    <!--    前台物资列表-->
    <select id="findPublicShop" resultType="scrbg.meplat.mall.entity.Shop"
            parameterType="Map">
        select
            s.shop_id,
            s.serial_num,
            s.shop_name,
            s.shop_balance,
            s.shop_freeze_money,
            s.shop_type,
            s.shop_img,
            s.ad_img,
            s.province,
            s.city,
            s.county,
            s.detailed_address,
            s.longitude,
            s.latitude,
            s.state,
            s.shop_describle,
            s.enterprise_id,
            s.main_business,
            s.is_business,
            s.is_supplier,
            s.is_internal_shop,
            s.is_internal_settlement,
            s.audit_status,
            s.open_date,
            s.shu_dao_flag,
            s.link_man,
            s.contact_number,
            s.initial,
            s.synthesis_sort,
            s.fail_reason,
            s.is_index_show,
            s.is_other_auth,
            s.return_address,
            s.return_relation_name,
            s.return_relation_number,
            s.shop_class,
            s.is_principal_material,
            s.audit_pass_time,
            s.is_arrearage,
            s.pay_days,
            s.sort,
            s.mall_type,
            s.gmt_create,
            s.gmt_modified,
            s.founder_name,
            s.founder_id,
            s.modify_name,
            s.modify_id,
            s.remarks,
            s.is_delete
        from shop s
        <if test="dto.orderBy == 40 or dto.orderBy == 41">
            left join (select comment_service_score,shop_id from  shop_comment where is_delete='0') sc ON sc.shop_id = s.shop_id
        </if>
        <where>
            s.`is_delete` = 0 AND s.`mall_type` = 0 AND s.`state` = 1 AND s.`audit_status` = 1
            <if test="dto.keywords != null and dto.keywords != ''">
                and s.`shop_name` LIKE CONCAT('%',#{dto.keywords},'%')
            </if>
            <if test="dto.city != null and dto.city != ''">
                and s.`city` = #{dto.city}
            </if>
            <if test="dto.type != null and dto.type == 1">
                and s.`is_business` = 1
            </if>
            <if test="dto.type != null and dto.type == 2">
                and s.`is_internal_shop` = 1
            </if>
            <if test="dto.type != null and dto.type == 3">
                and s.`is_internal_shop` = 0
            </if>
            <if test="dto.initial != null and dto.initial != ''">
                and s.`initial` = #{dto.initial}
            </if>
        </where>

        <if test="dto.orderBy == 0">
            order by s.is_business desc  ,s.is_internal_shop desc , s.synthesis_sort desc , s.open_date
        </if>
        <if test="dto.orderBy == 40">
            order by sc.comment_service_score asc, s.is_business desc  ,s.is_internal_shop desc , s.synthesis_sort desc , s.open_date
        </if>
        <if test="dto.orderBy == 41">
            order by sc.comment_service_score desc, s.is_business desc  ,s.is_internal_shop desc , s.synthesis_sort desc , s.open_date
        </if>
    </select>

    <select id="getIndexSupplierListCount" resultType="int" parameterType="Map">
        SELECT count(*) from shop_supplier_rele ss LEFT JOIN shop s on ss.supplier_id = s.enterprise_id
		<where>
			ss.is_delete = 0 and ss.shop_id = '1878734518961074177' and s.shop_id is not null and s.state = 1
            <if test="dto.keywords != null and dto.keywords != ''">
                and s.shop_name LIKE CONCAT('%',#{dto.keywords},'%')
            </if>
            <if test="dto.area != null and dto.area != ''">
                and s.`city` = #{dto.area}
            </if>
            <if test="dto.initial != null and dto.initial != ''">
                and s.`initial` = #{dto.initial}
            </if>
        </where>
    </select>
</mapper>