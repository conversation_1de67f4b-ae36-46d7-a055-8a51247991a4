<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.mall.mapper.system.SysUserRoleMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="scrbg.meplat.mall.entity.system.SysUserRole">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="org_id" property="orgId" jdbcType="VARCHAR"/>
        <result column="role_id" property="roleId" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap id="roleMenuMap" type="scrbg.meplat.mall.dto.user.MallRole">
        <id column="role_id" property="roleId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="org_search" property="orgSearch"/>
        <result column="category_type" property="categoryType"/>
        <collection property="mallSysMenus" ofType="scrbg.meplat.mall.dto.user.MallSysMenu">
            <id column="menu_id" property="menuId"/>
            <result column="smCode" property="code"/>
            <result column="title" property="title"/>
            <result column="type" property="type"/>
            <result column="auth_label" property="authLabel"/>
            <result column="icon" property="icon"/>
            <result column="path_url" property="pathUrl"/>
            <result column="class_code" property="classCode"/>
            <result column="parent_menu_id" property="parentMenuId"/>
            <result column="level" property="level"/>
        </collection>
    </resultMap>
    <insert id="insertBatch">
        INSERT INTO sys_user_role (user_id,org_id, role_id) VALUES
        <foreach collection="userRoles" item="item" separator=",">
            (#{item.userId}, #{item.orgId}, #{item.roleId})
        </foreach>
    </insert>

    <delete id="deleteByUserIdAndRoleId">
        delete from sys_user_role where user_id = #{userId}  and org_id = #{orgId} and roleId in #{roleIds}
    </delete>
    <select id="selectListByUserId" resultMap="BaseResultMap">
        select * from sys_user_role where user_id = #{userId} and org_id = #{orgId}
    </select>
    <select id="selectListByUserId" resultType="java.lang.String">
       select distinct role_id from sys_role1 where role_id in ( select role_id from sys_user_role where user_id = #{userId} and org_id = #{orgId})
    </select>
    <select id="selectByUserId" resultType="java.lang.String">
        select distinct role_id from sys_role1 where role_id in ( select role_id from sys_user_role where user_id = #{userId} and org_id = #{orgId})
    </select>
    <select id="getRoleMenuListByRoleNames" resultMap="roleMenuMap">
        SELECT DISTINCT
        sr.role_id,
        sr.code,
        sr.name,
        sr.org_search,
        sr.category_type,
        sm.menu_id,
        sm.code as smCode,
        sm.title,
        sm.type,
        sm.auth_label,
        sm.icon,
        sm.path_url,
        sm.class_code,
        sm.parent_menu_id,
        sm.level
        FROM
        sys_role1 sr
        LEFT JOIN sys_menu_role1 smr ON sr.role_id COLLATE utf8mb4_unicode_ci= smr.role_id and smr.is_delete = 0
        LEFT JOIN sys_menu1 sm ON sm.menu_id = smr.menu_id AND sm.is_delete = 0 AND sm.state = 1
        WHERE
        sr.is_delete = 0 and IFNULL(sr.state,1) = 1
        and sr.`role_id` in  ( select role_id from sys_user_role where user_id = #{userId} and org_id = #{orgId})
    </select>
    <select id="getPermissionListByRoleNames" resultType="java.lang.String">
        select perms from sys_menu1 where menu_id in (select menu_id from sys_menu_role1 where role_id COLLATE utf8mb4_unicode_ci in (select role_id from sys_user_role where user_id = #{userId} and org_id = #{orgId} ))
    </select>
</mapper>