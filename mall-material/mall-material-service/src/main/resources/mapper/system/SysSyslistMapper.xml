<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.mall.mapper.system.SysSyslistMapper">

    <resultMap id="BaseResultMap" type="scrbg.meplat.mall.entity.system.SysSyslist">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, is_delete
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_syslist
        WHERE is_delete = 0
    </select>

</mapper>