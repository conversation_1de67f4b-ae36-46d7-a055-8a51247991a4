<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.mall.mapper.system.RoleMenu2Mapper">

    <delete id="deleteByRoleId">
        update sys_menu_role1
        SET is_delete = -1,
        gmt_modified = #{modifyTime},
        modify_id = #{modifyUserId},
        modify_name = #{modifyUserName}
        WHERE role_id = #{roleId}
    </delete>

    <insert id="batchInsertRoleMenus">
        INSERT INTO sys_menu_role1 (
        sys_menu_role_id, role_id, menu_id,
        gmt_create, founder_id, founder_name
        ) VALUES
        <foreach collection="menuIds" item="menuId" separator=",">
            (
            REPLACE(UUID(), '-', ''),
            #{roleId},
            #{menuId},
            #{gmtCreate},
            #{founderId},
            #{founderName}
            )
        </foreach>
    </insert>

    <select id="selectMenuIdsByRoleId" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT menu_id
        FROM sys_menu_role1
        WHERE role_id = #{roleId} AND is_delete = 0
    </select>
</mapper>