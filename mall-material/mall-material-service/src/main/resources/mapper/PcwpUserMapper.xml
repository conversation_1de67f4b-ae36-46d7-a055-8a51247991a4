<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.PcwpUserMapper">

    <select id="queryUserOrg" resultType="scrbg.meplat.mall.entity.PcwpUser">
        SELECT DISTINCT
            pp.*,
            ppp.org_name AS orgName,
            ppp.user_name AS userName
        FROM pcwp_person_permissions ppp
                 LEFT JOIN (
            SELECT
                xmbm,
                pname,
                SUBSTRING_INDEX(GROUP_CONCAT(pnumber ORDER BY id), ',', 1) AS pnumber
            FROM pcwp_rzjlinfos
            GROUP BY xmbm, pname, pnumber
        ) pr ON ppp.short_code = pr.xmbm AND ppp.user_name = pr.pname
                 left join pcwp_personinfos pp ON pp.pnumber = pr.pnumber
        where ${ew.sqlSegment}
    </select>
    <select id="queryUserByOrg" resultType="scrbg.meplat.mall.entity.PcwpUser">
        select pp.*, pr.xmmc as orgName,pr.xmbm as orgId from pcwp_personinfos pp
          left join pcwp_rzjlinfos pr on pr.pnumber = pp.pnumber
        where ${ew.sqlSegment}
    </select>
    <select id="selectUserCurrentOrgInfo" resultType="scrbg.meplat.mall.entity.pcwpmq.PcwpOrginfos">
        SELECT * from pcwp_orginfos where number = (select orgnumber from pcwp_personinfos where pnumber = #{userNumber} and mdmstate != -1)
    </select>
    <select id="getChildrenOrgList" resultType="scrbg.meplat.mall.entity.pcwpmq.PcwpOrginfos">
        SELECT * from pcwp_orginfos where number in (select xmbm from pcwp_rzjlinfos where pnumber = #{userNumber}
                                                                                     and mdmstate != -1) and mdmstate != -1
    </select>
</mapper>