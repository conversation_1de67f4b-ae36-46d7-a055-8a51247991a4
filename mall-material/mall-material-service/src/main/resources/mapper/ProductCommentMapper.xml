<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ProductCommentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ProductComment" id="ProductCommentMap">
        <result property="commentId" column="comment_id"/>
        <result property="productId" column="product_id"/>
        <result property="userId" column="user_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="orderItemId" column="order_item_id"/>
        <result property="productName" column="product_name"/>
        <result property="isAnonymous" column="is_anonymous"/>
        <result property="commentType" column="comment_type"/>
        <result property="commentLevel" column="comment_level"/>
        <result property="commentContent" column="comment_content"/>
        <result property="commentImgs" column="comment_imgs"/>
        <result property="evaluateTime" column="evaluate_time"/>
        <result property="isReply" column="is_reply"/>
        <result property="replyContent" column="reply_content"/>
        <result property="replyTime" column="reply_time"/>
        <result property="isShow" column="is_show"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="remarks" column="remarks"/>
        <result property="productType" column="product_type"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="orderSn" column="order_sn"/>
        <result property="shopName" column="shop_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="enterpriseName" column="enterprise_name"/>
    </resultMap>

    <select id="commentManageListCount" resultType="int"
            parameterType="map">
        select count(*)
        FROM
        product_comment pc left join orders o on pc.`order_id`=o.`order_id` left join user u on pc.`user_id`=u.`user_id`
        <where>
            and pc.`is_delete` = 0 and pc.`product_id` is null
            and o.`shop_id` = #{dto.shopId}
            <if test="dto.productTypes != null and dto.productTypes.size()>0">
                and o.`product_type` in
                <foreach collection="dto.productTypes" item="productType" open="(" close=")" separator=",">
                    #{productType}
                </foreach>
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                EXISTS (select 1 from product_comment where pc.comment_id=parent_id and `product_name` LIKE CONCAT('%',#{dto.keywords},'%'))
                or o.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%') or u.`nick_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and EXISTS (select 1 from product_comment where pc.comment_id=parent_id and `product_name` LIKE CONCAT('%',#{dto.productName},'%'))
            </if>
            <if test="dto.isZd == 0">
                and pc.`sort` = 0
            </if>
            <if test="dto.isZd == 1">
                and pc.`sort`>0
            </if>
            <if test="dto.isShow != null and dto.isShow != ''">
                and pc.`is_show` = #{dto.isShow}
            </if>
            <if test="dto.commentLevel != null and dto.commentLevel != ''">
                and pc.`comment_level` = #{dto.commentLevel}
            </if>
            <if test="dto.commentSupply != null and dto.commentSupply != ''">
                and pc.`comment_supply` = #{dto.commentSupply}
            </if>
            <if test="dto.commentIntegrity != null and dto.commentIntegrity != ''">
                and pc.`comment_integrity` = #{dto.commentIntegrity}
            </if>
            <if test="dto.commentService != null and dto.commentService != ''">
                and pc.`comment_service` = #{dto.commentService}
            </if>
            <if test="dto.commentStartDate != null">
                and pc.`evaluate_time` &gt;= #{dto.commentStartDate}
            </if>
            <if test="dto.commentEndDate != null">
                and pc.`evaluate_time` &lt;= #{dto.commentEndDate}
            </if>
        </where>
    </select>

    <select id="commentManageList" resultType="scrbg.meplat.mall.entity.ProductComment"
            parameterType="map">
        select
        pc.`comment_id`,
        pc.`product_id`,
        pc.`user_id`,
        pc.`parent_id`,
        pc.`order_id`,
        pc.`order_item_id`,
        pc.`product_name`,
        pc.`is_anonymous`,
        pc.`comment_type`,
        pc.`comment_level`,
        pc.`comment_supply`,
        pc.`comment_integrity`,
        pc.`comment_service`,
        pc.`comment_content`,
        pc.`comment_imgs`,
        pc.`evaluate_time`,
        pc.`is_reply`,
        pc.`reply_content`,
        pc.`reply_time`,
        pc.`is_show`,
        pc.`state`,
        pc.`sort`,
        pc.`mall_type`,
        pc.`gmt_create`,
        pc.`gmt_modified`,
        pc.`founder_name`,
        pc.`founder_id`,
        pc.`modify_name`,
        pc.`modify_id`,
        pc.`remarks`,
        o.`order_sn`,
        o.`shop_name`,
        o.`product_type`,
        o.`enterprise_name`,
        u.`nick_name`
        FROM
        product_comment pc left join orders o on pc.`order_id`=o.`order_id` left join user u on pc.`user_id`=u.`user_id`
        <where>
            and pc.`is_delete` = 0 and pc.`product_id` is null
            and o.`shop_id` = #{dto.shopId}
            <if test="dto.productTypes != null and dto.productTypes.size()>0">
                and o.`product_type` in
                <foreach collection="dto.productTypes" item="productType" open="(" close=")" separator=",">
                    #{productType}
                </foreach>
            </if>
            <if test="dto.keywords != null and dto.keywords != ''">
                and (
                EXISTS (select 1 from product_comment where pc.comment_id=parent_id and `product_name` LIKE CONCAT('%',#{dto.keywords},'%'))
                or o.`order_sn` LIKE CONCAT('%',#{dto.keywords},'%') or u.`nick_name` LIKE CONCAT('%',#{dto.keywords},'%')
                )
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and EXISTS (select 1 from product_comment where pc.comment_id=parent_id and `product_name` LIKE CONCAT('%',#{dto.productName},'%'))
            </if>
            <if test="dto.isZd == 0">
                and pc.`sort` = 0
            </if>
            <if test="dto.isZd == 1">
                and pc.`sort`>0
            </if>
            <if test="dto.isShow != null and dto.isShow != ''">
                and pc.`is_show` = #{dto.isShow}
            </if>
            <if test="dto.commentLevel != null and dto.commentLevel != ''">
                and pc.`comment_level` = #{dto.commentLevel}
            </if>
            <if test="dto.commentSupply != null and dto.commentSupply != ''">
                and pc.`comment_supply` = #{dto.commentSupply}
            </if>
            <if test="dto.commentIntegrity != null and dto.commentIntegrity != ''">
                and pc.`comment_integrity` = #{dto.commentIntegrity}
            </if>
            <if test="dto.commentService != null and dto.commentService != ''">
                and pc.`comment_service` = #{dto.commentService}
            </if>
            <if test="dto.commentStartDate != null">
                and pc.`evaluate_time` &gt;= #{dto.commentStartDate}
            </if>
            <if test="dto.commentEndDate != null">
                and pc.`evaluate_time` &lt;= #{dto.commentEndDate}
            </if>
        </where>
        order by pc.`gmt_create` desc
    </select>

</mapper>